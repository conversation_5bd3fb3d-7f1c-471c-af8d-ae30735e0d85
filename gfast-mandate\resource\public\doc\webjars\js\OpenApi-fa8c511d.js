import{l as s,B as v,_ as b,$ as r,V as A,d as j,a as C,b as D,c as I,f as R,u as x,m as u,r as i,o as U,j as E,w as c,k as l,t as m,p as M,L as P,y as S}from"./doc-30bb18f4.js";import{C as V}from"./clipboard-cedf2745.js";import{m as L,a as B,t as k,e as N}from"./ext-language_tools-602acc1a.js";import{C as $}from"./CopyOutlined-bc9c8f52.js";var T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};const J=T;function g(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?Object(arguments[e]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable}))),o.forEach(function(a){q(n,a,t[a])})}return n}function q(n,e,t){return e in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}var p=function(e,t){var o=g({},e,t.attrs);return s(v,g({},o,{icon:J}),null)};p.displayName="DownloadOutlined";p.inheritAttrs=!1;const z=p,H=""+new URL("mode-json5-ed4a693f.js",import.meta.url).href;r.config.setModuleUrl("ace/mode/json",L);r.config.setModuleUrl("ace/mode/json",H);r.config.setModuleUrl("ace/mode/xml",B);r.config.setModuleUrl("ace/theme/eclipse",k);r.config.setModuleUrl("ace/ext-language/tools",N);const G={name:"Document",components:{editor:A,CopyOutlined:$,DownloadOutlined:z,EditorShow:j(()=>C(()=>import("./EditorShow-edef4df9.js"),["./EditorShow-edef4df9.js","./doc-30bb18f4.js","..\\css\\doc-e469198e.css","./ext-language_tools-602acc1a.js"],import.meta.url))},props:{api:{type:Object,required:!0},swaggerInstance:{type:Object,required:!0}},setup(){const n=D(),e=I(()=>n.language),{messages:t}=R();return{language:e,messages:t}},data(){return{openApiRaw:"",name:"OpenAPI.json"}},created(){this.openApiRaw=x.json5stringify(this.api.openApiRaw),this.name=this.api.summary+"_OpenAPI.json",setTimeout(()=>{this.copyOpenApi()},500)},methods:{getCurrentI18nInstance(){return this.messages[this.language]},triggerDownloadOpen(){var n=this.openApiRaw,e=document.createElement("a"),t={},o=this.name,a=window.URL.createObjectURL(new Blob([n],{type:(t.type||"text/plain")+";charset="+(t.encoding||"utf-8")}));e.href=a,e.download=o||"file",e.click(),window.URL.revokeObjectURL(a)},copyOpenApi(){const n="btnCopyOpenApi"+this.api.id,e=new V("#"+n,{text:()=>this.openApiRaw});e.on("success",()=>{const o=this.getCurrentI18nInstance().message.copy.open.success;u.info(o)}),e.on("error",t=>{console.log(t);const o=this.getCurrentI18nInstance();console.log(o);const a=o.message.copy.open.fail;u.info(a)})}}},X={class:"document"},F={style:{"margin-top":"10px"},id:"knife4jDocumentOpenApiShowEditor"};function Q(n,e,t,o,a,f){const w=i("CopyOutlined"),d=P,_=i("DownloadOutlined"),h=S,O=i("editor-show");return U(),E("div",X,[s(h,{style:{"margin-top":"10px"}},{default:c(()=>[s(d,{type:"primary",id:"btnCopyOpenApi"+t.api.id},{default:c(()=>[s(w),l("span",null,m(n.$t("open.copy")),1)]),_:1},8,["id"]),s(d,{style:{"margin-left":"10px"},onClick:f.triggerDownloadOpen},{default:c(()=>[s(_),M(),l("span",null,m(n.$t("open.download")),1)]),_:1},8,["onClick"])]),_:1}),l("div",F,[s(O,{value:a.openApiRaw,"onUpdate:value":e[0]||(e[0]=y=>a.openApiRaw=y),theme:"eclipse"},null,8,["value"])])])}const ee=b(G,[["render",Q]]);export{ee as default};
