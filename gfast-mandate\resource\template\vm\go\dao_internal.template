// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：{{date "Y-m-d H:i:s"}}
// 生成路径: {{.table.PackageName}}/dao/internal/{{.table.TableName}}.go
// 生成人：{{.table.FunctionAuthor}}
// desc:{{.table.FunctionName}}
// company:云南奇讯科技有限公司
// ==========================================================================
////
package internal
////
import (
    "context"
    "github.com/gogf/gf/v2/database/gdb"
    "github.com/gogf/gf/v2/frame/g"
)

////
// {{.table.ClassName}}<PERSON><PERSON> is the manager for logic model data accessing and custom defined data operations functions management.
type {{.table.ClassName}}Dao struct {
    table   string         // Table is the underlying table name of the DAO.
    group   string         // Group is the database configuration group name of current DAO.
    columns {{.table.ClassName}}Columns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

////
// {{.table.ClassName}}Columns defines and stores column names for table {{.table.TableName}}.
type {{.table.ClassName}}Columns struct {
    {{range $index, $column := .table.Columns}}
    {{$column.GoField}}  string  // {{$column.ColumnComment}}
    {{end}}
}

////
var {{.table.BusinessName | CaseCamelLower}}Columns = {{.table.ClassName}}Columns{
    {{range $index, $column := .table.Columns}}
    {{$column.GoField}}:  "{{$column.ColumnName}}",
    {{end}}
}

////
// New{{.table.ClassName}}Dao creates and returns a new DAO object for table data access.
func New{{.table.ClassName}}Dao() *{{.table.ClassName}}Dao {
	return &{{.table.ClassName}}Dao{
        group:    "default",
        table: "{{.table.TableName}}",
        columns:{{.table.BusinessName | CaseCamelLower}}Columns,
	}
}

////
// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *{{.table.ClassName}}Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

////
// Table returns the table name of current dao.
func (dao *{{.table.ClassName}}Dao) Table() string {
	return dao.table
}

////
// Columns returns all column names of current dao.
func (dao *{{.table.ClassName}}Dao) Columns() {{.table.ClassName}}Columns {
	return dao.columns
}

////
// Group returns the configuration group name of database of current dao.
func (dao *{{.table.ClassName}}Dao) Group() string {
	return dao.group
}

////
// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *{{.table.ClassName}}Dao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

////
// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *{{.table.ClassName}}Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}