// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-12-01 16:05:36
// 生成路径: internal/app/plugins/sms/controller/plugin_sms_log.go
// 生成人：gfast
// desc:短信日志
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/plugins/sms"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type pluginSmsLogController struct {
	systemController.BaseController
}

var PluginSmsLog = new(pluginSmsLogController)

// List 列表
func (c *pluginSmsLogController) List(ctx context.Context, req *sms.PluginSmsLogSearchReq) (res *sms.PluginSmsLogSearchRes, err error) {
	res = new(sms.PluginSmsLogSearchRes)
	res.PluginSmsLogSearchRes, err = service.PluginSmsLog().List(ctx, &req.PluginSmsLogSearchReq)
	return
}

// Get 获取短信日志
func (c *pluginSmsLogController) Get(ctx context.Context, req *sms.PluginSmsLogGetReq) (res *sms.PluginSmsLogGetRes, err error) {
	res = new(sms.PluginSmsLogGetRes)
	res.PluginSmsLogInfoRes, err = service.PluginSmsLog().GetById(ctx, req.Id)
	return
}

// Add 添加短信日志
func (c *pluginSmsLogController) Add(ctx context.Context, req *sms.PluginSmsLogAddReq) (res *sms.PluginSmsLogAddRes, err error) {
	err = service.PluginSmsLog().Add(ctx, req.PluginSmsLogAddReq)
	return
}

// Edit 修改短信日志
func (c *pluginSmsLogController) Edit(ctx context.Context, req *sms.PluginSmsLogEditReq) (res *sms.PluginSmsLogEditRes, err error) {
	err = service.PluginSmsLog().Edit(ctx, req.PluginSmsLogEditReq)
	return
}

// Delete 删除短信日志
func (c *pluginSmsLogController) Delete(ctx context.Context, req *sms.PluginSmsLogDeleteReq) (res *sms.PluginSmsLogDeleteRes, err error) {
	err = service.PluginSmsLog().Delete(ctx, req.Ids)
	return
}
