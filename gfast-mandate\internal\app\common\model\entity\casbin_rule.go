// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CasbinRule is the golang structure for table casbin_rule.
type CasbinRule struct {
	Ptype string `json:"ptype" description:""`
	V0    string `json:"v0"    description:""`
	V1    string `json:"v1"    description:""`
	V2    string `json:"v2"    description:""`
	V3    string `json:"v3"    description:""`
	V4    string `json:"v4"    description:""`
	V5    string `json:"v5"    description:""`
}
