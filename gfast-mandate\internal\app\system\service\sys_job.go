// ================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/system"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
)

type (
	ISysJob interface {
		List(ctx context.Context, req *system.SysJobSearchReq) (listRes *system.SysJobSearchRes, err error)
		GetByJobId(ctx context.Context, jobId uint64) (res *model.SysJobInfoRes, err error)
		Add(ctx context.Context, req *system.SysJobAddReq) (err error)
		Edit(ctx context.Context, req *system.SysJobEditReq) (err error)
		Delete(ctx context.Context, jobIds []uint64) (err error)
		Start(ctx context.Context, jobId uint64) (err error)
		Stop(ctx context.Context, jobId uint64) (err error)
		Run(ctx context.Context, jobId uint64) (err error)
		GetJobs(ctx context.Context) (jobs []*model.SysJobInfoRes, err error)
		JobStart(ctx context.Context, job *model.SysJobInfoRes) error
	}
)

var (
	localSysJob ISysJob
)

func SysJob() ISysJob {
	if localSysJob == nil {
		panic("implement not found for interface ISysJob, forgot register?")
	}
	return localSysJob
}

func RegisterSysJob(i ISysJob) {
	localSysJob = i
}
