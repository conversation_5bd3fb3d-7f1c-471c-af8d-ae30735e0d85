import{a2 as Ie,ay as Ae,ax as Ne,a5 as pt,a1 as oe,a4 as Se,a3 as Me,a7 as Re,az as De,ab as G,a6 as Mt,ae as w,a8 as Ve,aa as St,ac as Ce,av as B,ag as ce,aQ as le,aY as Oe,aZ as Be}from"./doc-30bb18f4.js";import{d as Ye,a as Fe,g as Rt,b as We,c as qe,e as Xt}from"./svgDrawCommon-f26cad39-7c125e42.js";var zt=function(){var t=function(dt,m,v,_){for(v=v||{},_=dt.length;_--;v[dt[_]]=m);return v},e=[1,2],o=[1,3],i=[1,5],s=[1,7],n=[2,5],l=[1,15],h=[1,17],p=[1,19],r=[1,20],g=[1,22],y=[1,23],u=[1,24],x=[1,30],b=[1,31],k=[1,32],N=[1,33],D=[1,34],S=[1,35],W=[1,36],M=[1,37],it=[1,38],z=[1,39],X=[1,40],Q=[1,41],j=[1,42],F=[1,44],O=[1,45],q=[1,46],H=[1,48],J=[1,49],$=[1,50],tt=[1,51],E=[1,52],L=[1,53],I=[1,56],P=[1,4,5,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,53,54,55,56,58,59,60,65,66,67,68,76,86],U=[4,5,22,56,58,59],A=[4,5,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,56,58,59,60,65,66,67,68,76,86],te=[4,5,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,55,56,58,59,60,65,66,67,68,76,86],Ct=[4,5,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,54,56,58,59,60,65,66,67,68,76,86],ee=[4,5,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,53,56,58,59,60,65,66,67,68,76,86],ht=[74,75,76],rt=[1,133],ie=[1,4,5,7,19,20,22,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,53,54,55,56,58,59,60,65,66,67,68,76,86],Ot={trace:function(){},yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,directive:6,SD:7,document:8,line:9,statement:10,box_section:11,box_line:12,participant_statement:13,openDirective:14,typeDirective:15,closeDirective:16,":":17,argDirective:18,create:19,box:20,restOfLine:21,end:22,signal:23,autonumber:24,NUM:25,off:26,activate:27,actor:28,deactivate:29,note_statement:30,links_statement:31,link_statement:32,properties_statement:33,details_statement:34,title:35,legacy_title:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,loop:42,rect:43,opt:44,alt:45,else_sections:46,par:47,par_sections:48,par_over:49,critical:50,option_sections:51,break:52,option:53,and:54,else:55,participant:56,AS:57,participant_actor:58,destroy:59,note:60,placement:61,text2:62,over:63,actor_pair:64,links:65,link:66,properties:67,details:68,spaceList:69,",":70,left_of:71,right_of:72,signaltype:73,"+":74,"-":75,ACTOR:76,SOLID_OPEN_ARROW:77,DOTTED_OPEN_ARROW:78,SOLID_ARROW:79,DOTTED_ARROW:80,SOLID_CROSS:81,DOTTED_CROSS:82,SOLID_POINT:83,DOTTED_POINT:84,TXT:85,open_directive:86,type_directive:87,arg_directive:88,close_directive:89,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",7:"SD",17:":",19:"create",20:"box",21:"restOfLine",22:"end",24:"autonumber",25:"NUM",26:"off",27:"activate",29:"deactivate",35:"title",36:"legacy_title",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"loop",43:"rect",44:"opt",45:"alt",47:"par",49:"par_over",50:"critical",52:"break",53:"option",54:"and",55:"else",56:"participant",57:"AS",58:"participant_actor",59:"destroy",60:"note",63:"over",65:"links",66:"link",67:"properties",68:"details",70:",",71:"left_of",72:"right_of",74:"+",75:"-",76:"ACTOR",77:"SOLID_OPEN_ARROW",78:"DOTTED_OPEN_ARROW",79:"SOLID_ARROW",80:"DOTTED_ARROW",81:"SOLID_CROSS",82:"DOTTED_CROSS",83:"SOLID_POINT",84:"DOTTED_POINT",85:"TXT",86:"open_directive",87:"type_directive",88:"arg_directive",89:"close_directive"},productions_:[0,[3,2],[3,2],[3,2],[3,2],[8,0],[8,2],[9,2],[9,1],[9,1],[11,0],[11,2],[12,2],[12,1],[12,1],[6,4],[6,6],[10,1],[10,2],[10,4],[10,2],[10,4],[10,3],[10,3],[10,2],[10,3],[10,3],[10,2],[10,2],[10,2],[10,2],[10,2],[10,1],[10,1],[10,2],[10,2],[10,1],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,4],[10,1],[51,1],[51,4],[48,1],[48,4],[46,1],[46,4],[13,5],[13,3],[13,5],[13,3],[13,3],[30,4],[30,4],[31,3],[32,3],[33,3],[34,3],[69,2],[69,1],[64,3],[64,1],[61,1],[61,1],[23,5],[23,5],[23,4],[28,1],[73,1],[73,1],[73,1],[73,1],[73,1],[73,1],[73,1],[73,1],[62,1],[14,1],[15,1],[18,1],[16,1]],performAction:function(m,v,_,T,R,c,vt){var d=c.length-1;switch(R){case 4:return T.apply(c[d]),c[d];case 5:case 10:this.$=[];break;case 6:case 11:c[d-1].push(c[d]),this.$=c[d-1];break;case 7:case 8:case 12:case 13:this.$=c[d];break;case 9:case 14:this.$=[];break;case 18:c[d].type="createParticipant",this.$=c[d];break;case 19:c[d-1].unshift({type:"boxStart",boxData:T.parseBoxData(c[d-2])}),c[d-1].push({type:"boxEnd",boxText:c[d-2]}),this.$=c[d-1];break;case 21:this.$={type:"sequenceIndex",sequenceIndex:Number(c[d-2]),sequenceIndexStep:Number(c[d-1]),sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"sequenceIndex",sequenceIndex:Number(c[d-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 23:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:T.LINETYPE.AUTONUMBER};break;case 24:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 25:this.$={type:"activeStart",signalType:T.LINETYPE.ACTIVE_START,actor:c[d-1]};break;case 26:this.$={type:"activeEnd",signalType:T.LINETYPE.ACTIVE_END,actor:c[d-1]};break;case 32:T.setDiagramTitle(c[d].substring(6)),this.$=c[d].substring(6);break;case 33:T.setDiagramTitle(c[d].substring(7)),this.$=c[d].substring(7);break;case 34:this.$=c[d].trim(),T.setAccTitle(this.$);break;case 35:case 36:this.$=c[d].trim(),T.setAccDescription(this.$);break;case 37:c[d-1].unshift({type:"loopStart",loopText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.LOOP_START}),c[d-1].push({type:"loopEnd",loopText:c[d-2],signalType:T.LINETYPE.LOOP_END}),this.$=c[d-1];break;case 38:c[d-1].unshift({type:"rectStart",color:T.parseMessage(c[d-2]),signalType:T.LINETYPE.RECT_START}),c[d-1].push({type:"rectEnd",color:T.parseMessage(c[d-2]),signalType:T.LINETYPE.RECT_END}),this.$=c[d-1];break;case 39:c[d-1].unshift({type:"optStart",optText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.OPT_START}),c[d-1].push({type:"optEnd",optText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.OPT_END}),this.$=c[d-1];break;case 40:c[d-1].unshift({type:"altStart",altText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.ALT_START}),c[d-1].push({type:"altEnd",signalType:T.LINETYPE.ALT_END}),this.$=c[d-1];break;case 41:c[d-1].unshift({type:"parStart",parText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.PAR_START}),c[d-1].push({type:"parEnd",signalType:T.LINETYPE.PAR_END}),this.$=c[d-1];break;case 42:c[d-1].unshift({type:"parStart",parText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.PAR_OVER_START}),c[d-1].push({type:"parEnd",signalType:T.LINETYPE.PAR_END}),this.$=c[d-1];break;case 43:c[d-1].unshift({type:"criticalStart",criticalText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.CRITICAL_START}),c[d-1].push({type:"criticalEnd",signalType:T.LINETYPE.CRITICAL_END}),this.$=c[d-1];break;case 44:c[d-1].unshift({type:"breakStart",breakText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.BREAK_START}),c[d-1].push({type:"breakEnd",optText:T.parseMessage(c[d-2]),signalType:T.LINETYPE.BREAK_END}),this.$=c[d-1];break;case 47:this.$=c[d-3].concat([{type:"option",optionText:T.parseMessage(c[d-1]),signalType:T.LINETYPE.CRITICAL_OPTION},c[d]]);break;case 49:this.$=c[d-3].concat([{type:"and",parText:T.parseMessage(c[d-1]),signalType:T.LINETYPE.PAR_AND},c[d]]);break;case 51:this.$=c[d-3].concat([{type:"else",altText:T.parseMessage(c[d-1]),signalType:T.LINETYPE.ALT_ELSE},c[d]]);break;case 52:c[d-3].draw="participant",c[d-3].type="addParticipant",c[d-3].description=T.parseMessage(c[d-1]),this.$=c[d-3];break;case 53:c[d-1].draw="participant",c[d-1].type="addParticipant",this.$=c[d-1];break;case 54:c[d-3].draw="actor",c[d-3].type="addParticipant",c[d-3].description=T.parseMessage(c[d-1]),this.$=c[d-3];break;case 55:c[d-1].draw="actor",c[d-1].type="addParticipant",this.$=c[d-1];break;case 56:c[d-1].type="destroyParticipant",this.$=c[d-1];break;case 57:this.$=[c[d-1],{type:"addNote",placement:c[d-2],actor:c[d-1].actor,text:c[d]}];break;case 58:c[d-2]=[].concat(c[d-1],c[d-1]).slice(0,2),c[d-2][0]=c[d-2][0].actor,c[d-2][1]=c[d-2][1].actor,this.$=[c[d-1],{type:"addNote",placement:T.PLACEMENT.OVER,actor:c[d-2].slice(0,2),text:c[d]}];break;case 59:this.$=[c[d-1],{type:"addLinks",actor:c[d-1].actor,text:c[d]}];break;case 60:this.$=[c[d-1],{type:"addALink",actor:c[d-1].actor,text:c[d]}];break;case 61:this.$=[c[d-1],{type:"addProperties",actor:c[d-1].actor,text:c[d]}];break;case 62:this.$=[c[d-1],{type:"addDetails",actor:c[d-1].actor,text:c[d]}];break;case 65:this.$=[c[d-2],c[d]];break;case 66:this.$=c[d];break;case 67:this.$=T.PLACEMENT.LEFTOF;break;case 68:this.$=T.PLACEMENT.RIGHTOF;break;case 69:this.$=[c[d-4],c[d-1],{type:"addMessage",from:c[d-4].actor,to:c[d-1].actor,signalType:c[d-3],msg:c[d]},{type:"activeStart",signalType:T.LINETYPE.ACTIVE_START,actor:c[d-1]}];break;case 70:this.$=[c[d-4],c[d-1],{type:"addMessage",from:c[d-4].actor,to:c[d-1].actor,signalType:c[d-3],msg:c[d]},{type:"activeEnd",signalType:T.LINETYPE.ACTIVE_END,actor:c[d-4]}];break;case 71:this.$=[c[d-3],c[d-1],{type:"addMessage",from:c[d-3].actor,to:c[d-1].actor,signalType:c[d-2],msg:c[d]}];break;case 72:this.$={type:"addParticipant",actor:c[d]};break;case 73:this.$=T.LINETYPE.SOLID_OPEN;break;case 74:this.$=T.LINETYPE.DOTTED_OPEN;break;case 75:this.$=T.LINETYPE.SOLID;break;case 76:this.$=T.LINETYPE.DOTTED;break;case 77:this.$=T.LINETYPE.SOLID_CROSS;break;case 78:this.$=T.LINETYPE.DOTTED_CROSS;break;case 79:this.$=T.LINETYPE.SOLID_POINT;break;case 80:this.$=T.LINETYPE.DOTTED_POINT;break;case 81:this.$=T.parseMessage(c[d].trim().substring(1));break;case 82:T.parseDirective("%%{","open_directive");break;case 83:T.parseDirective(c[d],"type_directive");break;case 84:c[d]=c[d].trim().replace(/'/g,'"'),T.parseDirective(c[d],"arg_directive");break;case 85:T.parseDirective("}%%","close_directive","sequence");break}},table:[{3:1,4:e,5:o,6:4,7:i,14:6,86:s},{1:[3]},{3:8,4:e,5:o,6:4,7:i,14:6,86:s},{3:9,4:e,5:o,6:4,7:i,14:6,86:s},{3:10,4:e,5:o,6:4,7:i,14:6,86:s},t([1,4,5,19,20,24,27,29,35,36,37,39,41,42,43,44,45,47,49,50,52,56,58,59,60,65,66,67,68,76,86],n,{8:11}),{15:12,87:[1,13]},{87:[2,82]},{1:[2,1]},{1:[2,2]},{1:[2,3]},{1:[2,4],4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{16:54,17:[1,55],89:I},t([17,89],[2,83]),t(P,[2,6]),{6:43,10:57,13:18,14:6,19:p,20:r,23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},t(P,[2,8]),t(P,[2,9]),t(P,[2,17]),{13:58,56:F,58:O,59:q},{21:[1,59]},{5:[1,60]},{5:[1,63],25:[1,61],26:[1,62]},{28:64,76:L},{28:65,76:L},{5:[1,66]},{5:[1,67]},{5:[1,68]},{5:[1,69]},{5:[1,70]},t(P,[2,32]),t(P,[2,33]),{38:[1,71]},{40:[1,72]},t(P,[2,36]),{21:[1,73]},{21:[1,74]},{21:[1,75]},{21:[1,76]},{21:[1,77]},{21:[1,78]},{21:[1,79]},{21:[1,80]},t(P,[2,45]),{28:81,76:L},{28:82,76:L},{28:83,76:L},{73:84,77:[1,85],78:[1,86],79:[1,87],80:[1,88],81:[1,89],82:[1,90],83:[1,91],84:[1,92]},{61:93,63:[1,94],71:[1,95],72:[1,96]},{28:97,76:L},{28:98,76:L},{28:99,76:L},{28:100,76:L},t([5,57,70,77,78,79,80,81,82,83,84,85],[2,72]),{5:[1,101]},{18:102,88:[1,103]},{5:[2,85]},t(P,[2,7]),t(P,[2,18]),t(U,[2,10],{11:104}),t(P,[2,20]),{5:[1,106],25:[1,105]},{5:[1,107]},t(P,[2,24]),{5:[1,108]},{5:[1,109]},t(P,[2,27]),t(P,[2,28]),t(P,[2,29]),t(P,[2,30]),t(P,[2,31]),t(P,[2,34]),t(P,[2,35]),t(A,n,{8:110}),t(A,n,{8:111}),t(A,n,{8:112}),t(te,n,{46:113,8:114}),t(Ct,n,{48:115,8:116}),t(Ct,n,{8:116,48:117}),t(ee,n,{51:118,8:119}),t(A,n,{8:120}),{5:[1,122],57:[1,121]},{5:[1,124],57:[1,123]},{5:[1,125]},{28:128,74:[1,126],75:[1,127],76:L},t(ht,[2,73]),t(ht,[2,74]),t(ht,[2,75]),t(ht,[2,76]),t(ht,[2,77]),t(ht,[2,78]),t(ht,[2,79]),t(ht,[2,80]),{28:129,76:L},{28:131,64:130,76:L},{76:[2,67]},{76:[2,68]},{62:132,85:rt},{62:134,85:rt},{62:135,85:rt},{62:136,85:rt},t(ie,[2,15]),{16:137,89:I},{89:[2,84]},{4:[1,140],5:[1,142],12:139,13:141,22:[1,138],56:F,58:O,59:q},{5:[1,143]},t(P,[2,22]),t(P,[2,23]),t(P,[2,25]),t(P,[2,26]),{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[1,144],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[1,145],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[1,146],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{22:[1,147]},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[2,50],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,55:[1,148],56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{22:[1,149]},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[2,48],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,54:[1,150],56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{22:[1,151]},{22:[1,152]},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[2,46],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,53:[1,153],56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{4:l,5:h,6:43,9:14,10:16,13:18,14:6,19:p,20:r,22:[1,154],23:21,24:g,27:y,28:47,29:u,30:25,31:26,32:27,33:28,34:29,35:x,36:b,37:k,39:N,41:D,42:S,43:W,44:M,45:it,47:z,49:X,50:Q,52:j,56:F,58:O,59:q,60:H,65:J,66:$,67:tt,68:E,76:L,86:s},{21:[1,155]},t(P,[2,53]),{21:[1,156]},t(P,[2,55]),t(P,[2,56]),{28:157,76:L},{28:158,76:L},{62:159,85:rt},{62:160,85:rt},{62:161,85:rt},{70:[1,162],85:[2,66]},{5:[2,59]},{5:[2,81]},{5:[2,60]},{5:[2,61]},{5:[2,62]},{5:[1,163]},t(P,[2,19]),t(U,[2,11]),{13:164,56:F,58:O,59:q},t(U,[2,13]),t(U,[2,14]),t(P,[2,21]),t(P,[2,37]),t(P,[2,38]),t(P,[2,39]),t(P,[2,40]),{21:[1,165]},t(P,[2,41]),{21:[1,166]},t(P,[2,42]),t(P,[2,43]),{21:[1,167]},t(P,[2,44]),{5:[1,168]},{5:[1,169]},{62:170,85:rt},{62:171,85:rt},{5:[2,71]},{5:[2,57]},{5:[2,58]},{28:172,76:L},t(ie,[2,16]),t(U,[2,12]),t(te,n,{8:114,46:173}),t(Ct,n,{8:116,48:174}),t(ee,n,{8:119,51:175}),t(P,[2,52]),t(P,[2,54]),{5:[2,69]},{5:[2,70]},{85:[2,65]},{22:[2,51]},{22:[2,49]},{22:[2,47]}],defaultActions:{7:[2,82],8:[2,1],9:[2,2],10:[2,3],56:[2,85],95:[2,67],96:[2,68],103:[2,84],132:[2,59],133:[2,81],134:[2,60],135:[2,61],136:[2,62],159:[2,71],160:[2,57],161:[2,58],170:[2,69],171:[2,70],172:[2,65],173:[2,51],174:[2,49],175:[2,47]},parseError:function(m,v){if(v.recoverable)this.trace(m);else{var _=new Error(m);throw _.hash=v,_}},parse:function(m){var v=this,_=[0],T=[],R=[null],c=[],vt=this.table,d="",It=0,se=0,_e=2,ne=1,ke=c.slice.call(arguments,1),Y=Object.create(this.lexer),ut={yy:{}};for(var Yt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Yt)&&(ut.yy[Yt]=this.yy[Yt]);Y.setInput(m,ut.yy),ut.yy.lexer=Y,ut.yy.parser=this,typeof Y.yylloc>"u"&&(Y.yylloc={});var Ft=Y.yylloc;c.push(Ft);var Pe=Y.options&&Y.options.ranges;typeof ut.yy.parseError=="function"?this.parseError=ut.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Le(){var ot;return ot=T.pop()||Y.lex()||ne,typeof ot!="number"&&(ot instanceof Array&&(T=ot,ot=T.pop()),ot=v.symbols_[ot]||ot),ot}for(var K,ft,et,Wt,yt={},At,at,re,Nt;;){if(ft=_[_.length-1],this.defaultActions[ft]?et=this.defaultActions[ft]:((K===null||typeof K>"u")&&(K=Le()),et=vt[ft]&&vt[ft][K]),typeof et>"u"||!et.length||!et[0]){var qt="";Nt=[];for(At in vt[ft])this.terminals_[At]&&At>_e&&Nt.push("'"+this.terminals_[At]+"'");Y.showPosition?qt="Parse error on line "+(It+1)+`:
`+Y.showPosition()+`
Expecting `+Nt.join(", ")+", got '"+(this.terminals_[K]||K)+"'":qt="Parse error on line "+(It+1)+": Unexpected "+(K==ne?"end of input":"'"+(this.terminals_[K]||K)+"'"),this.parseError(qt,{text:Y.match,token:this.terminals_[K]||K,line:Y.yylineno,loc:Ft,expected:Nt})}if(et[0]instanceof Array&&et.length>1)throw new Error("Parse Error: multiple actions possible at state: "+ft+", token: "+K);switch(et[0]){case 1:_.push(K),R.push(Y.yytext),c.push(Y.yylloc),_.push(et[1]),K=null,se=Y.yyleng,d=Y.yytext,It=Y.yylineno,Ft=Y.yylloc;break;case 2:if(at=this.productions_[et[1]][1],yt.$=R[R.length-at],yt._$={first_line:c[c.length-(at||1)].first_line,last_line:c[c.length-1].last_line,first_column:c[c.length-(at||1)].first_column,last_column:c[c.length-1].last_column},Pe&&(yt._$.range=[c[c.length-(at||1)].range[0],c[c.length-1].range[1]]),Wt=this.performAction.apply(yt,[d,se,It,ut.yy,et[1],R,c].concat(ke)),typeof Wt<"u")return Wt;at&&(_=_.slice(0,-1*at*2),R=R.slice(0,-1*at),c=c.slice(0,-1*at)),_.push(this.productions_[et[1]][0]),R.push(yt.$),c.push(yt._$),re=vt[_[_.length-2]][_[_.length-1]],_.push(re);break;case 3:return!0}}return!0}},ve=function(){var dt={EOF:1,parseError:function(v,_){if(this.yy.parser)this.yy.parser.parseError(v,_);else throw new Error(v)},setInput:function(m,v){return this.yy=v||this.yy||{},this._input=m,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var m=this._input[0];this.yytext+=m,this.yyleng++,this.offset++,this.match+=m,this.matched+=m;var v=m.match(/(?:\r\n?|\n).*/g);return v?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),m},unput:function(m){var v=m.length,_=m.split(/(?:\r\n?|\n)/g);this._input=m+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-v),this.offset-=v;var T=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),_.length-1&&(this.yylineno-=_.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:_?(_.length===T.length?this.yylloc.first_column:0)+T[T.length-_.length].length-_[0].length:this.yylloc.first_column-v},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-v]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(m){this.unput(this.match.slice(m))},pastInput:function(){var m=this.matched.substr(0,this.matched.length-this.match.length);return(m.length>20?"...":"")+m.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var m=this.match;return m.length<20&&(m+=this._input.substr(0,20-m.length)),(m.substr(0,20)+(m.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var m=this.pastInput(),v=new Array(m.length+1).join("-");return m+this.upcomingInput()+`
`+v+"^"},test_match:function(m,v){var _,T,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),T=m[0].match(/(?:\r\n?|\n).*/g),T&&(this.yylineno+=T.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:T?T[T.length-1].length-T[T.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+m[0].length},this.yytext+=m[0],this.match+=m[0],this.matches=m,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(m[0].length),this.matched+=m[0],_=this.performAction.call(this,this.yy,this,v,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),_)return _;if(this._backtrack){for(var c in R)this[c]=R[c];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var m,v,_,T;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),c=0;c<R.length;c++)if(_=this._input.match(this.rules[R[c]]),_&&(!v||_[0].length>v[0].length)){if(v=_,T=c,this.options.backtrack_lexer){if(m=this.test_match(_,R[c]),m!==!1)return m;if(this._backtrack){v=!1;continue}else return!1}else if(!this.options.flex)break}return v?(m=this.test_match(v,R[T]),m!==!1?m:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var v=this.next();return v||this.lex()},begin:function(v){this.conditionStack.push(v)},popState:function(){var v=this.conditionStack.length-1;return v>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(v){return v=this.conditionStack.length-1-Math.abs(v||0),v>=0?this.conditionStack[v]:"INITIAL"},pushState:function(v){this.begin(v)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(v,_,T,R){switch(T){case 0:return this.begin("open_directive"),86;case 1:return this.begin("type_directive"),87;case 2:return this.popState(),this.begin("arg_directive"),17;case 3:return this.popState(),this.popState(),89;case 4:return 88;case 5:return 5;case 6:break;case 7:break;case 8:break;case 9:break;case 10:break;case 11:return 25;case 12:return this.begin("LINE"),20;case 13:return this.begin("ID"),56;case 14:return this.begin("ID"),58;case 15:return 19;case 16:return this.begin("ID"),59;case 17:return _.yytext=_.yytext.trim(),this.begin("ALIAS"),76;case 18:return this.popState(),this.popState(),this.begin("LINE"),57;case 19:return this.popState(),this.popState(),5;case 20:return this.begin("LINE"),42;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),44;case 23:return this.begin("LINE"),45;case 24:return this.begin("LINE"),55;case 25:return this.begin("LINE"),47;case 26:return this.begin("LINE"),49;case 27:return this.begin("LINE"),54;case 28:return this.begin("LINE"),50;case 29:return this.begin("LINE"),53;case 30:return this.begin("LINE"),52;case 31:return this.popState(),21;case 32:return 22;case 33:return 71;case 34:return 72;case 35:return 65;case 36:return 66;case 37:return 67;case 38:return 68;case 39:return 63;case 40:return 60;case 41:return this.begin("ID"),27;case 42:return this.begin("ID"),29;case 43:return 35;case 44:return 36;case 45:return this.begin("acc_title"),37;case 46:return this.popState(),"acc_title_value";case 47:return this.begin("acc_descr"),39;case 48:return this.popState(),"acc_descr_value";case 49:this.begin("acc_descr_multiline");break;case 50:this.popState();break;case 51:return"acc_descr_multiline_value";case 52:return 7;case 53:return 24;case 54:return 26;case 55:return 70;case 56:return 5;case 57:return _.yytext=_.yytext.trim(),76;case 58:return 79;case 59:return 80;case 60:return 77;case 61:return 78;case 62:return 81;case 63:return 82;case 64:return 83;case 65:return 84;case 66:return 85;case 67:return 74;case 68:return 75;case 69:return 5;case 70:return"INVALID"}},rules:[/^(?:%%\{)/i,/^(?:((?:(?!\}%%)[^:.])*))/i,/^(?::)/i,/^(?:\}%%)/i,/^(?:((?:(?!\}%%).|\n)*))/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\->:\n,;]+?([\-]*[^\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[50,51],inclusive:!1},acc_descr:{rules:[48],inclusive:!1},acc_title:{rules:[46],inclusive:!1},open_directive:{rules:[1,8],inclusive:!1},type_directive:{rules:[2,3,8],inclusive:!1},arg_directive:{rules:[3,4,8],inclusive:!1},ID:{rules:[7,8,17],inclusive:!1},ALIAS:{rules:[7,8,18,19],inclusive:!1},LINE:{rules:[7,8,31],inclusive:!1},INITIAL:{rules:[0,5,6,8,9,10,11,12,13,14,15,16,20,21,22,23,24,25,26,27,28,29,30,32,33,34,35,36,37,38,39,40,41,42,43,44,45,47,49,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70],inclusive:!0}}};return dt}();Ot.lexer=ve;function Bt(){this.yy={}}return Bt.prototype=Ot,Ot.Parser=Bt,new Bt}();zt.parser=zt;const ze=zt;let _t,lt={},Jt={},Zt={},mt=[],Z=[],Dt=!1,Ht,ct,kt,Et;const He=function(t,e,o){Re.parseDirective(this,t,e,o)},Ue=function(t){mt.push({name:t.text,wrap:t.wrap===void 0&&xt()||!!t.wrap,fill:t.color,actorKeys:[]}),ct=mt.slice(-1)[0]},Ut=function(t,e,o,i){let s=ct;const n=lt[t];if(n){if(ct&&n.box&&ct!==n.box)throw new Error("A same participant should only be defined in one Box: "+n.name+" can't be in '"+n.box.name+"' and in '"+ct.name+"' at the same time.");if(s=n.box?n.box:ct,n.box=s,n&&e===n.name&&o==null)return}(o==null||o.text==null)&&(o={text:e,wrap:null,type:i}),(i==null||o.text==null)&&(o={text:e,wrap:null,type:i}),lt[t]={box:s,name:e,description:o.text,wrap:o.wrap===void 0&&xt()||!!o.wrap,prevActor:_t,links:{},properties:{},actorCnt:null,rectData:null,type:i||"participant"},_t&&lt[_t]&&(lt[_t].nextActor=t),ct&&ct.actorKeys.push(t),_t=t},Ke=t=>{let e,o=0;for(e=0;e<Z.length;e++)Z[e].type===Pt.ACTIVE_START&&Z[e].from.actor===t&&o++,Z[e].type===Pt.ACTIVE_END&&Z[e].from.actor===t&&o--;return o},Ge=function(t,e,o,i){Z.push({from:t,to:e,message:o.text,wrap:o.wrap===void 0&&xt()||!!o.wrap,answer:i})},V=function(t,e,o={text:void 0,wrap:void 0},i){if(i===Pt.ACTIVE_END&&Ke(t.actor)<1){let n=new Error("Trying to inactivate an inactive participant ("+t.actor+")");throw n.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},n}return Z.push({from:t,to:e,message:o.text,wrap:o.wrap===void 0&&xt()||!!o.wrap,type:i}),!0},Xe=function(){return mt.length>0},Je=function(){return mt.some(t=>t.name)},Ze=function(){return Z},Qe=function(){return mt},je=function(){return lt},$e=function(){return Jt},t0=function(){return Zt},Lt=function(t){return lt[t]},e0=function(){return Object.keys(lt)},i0=function(){Dt=!0},s0=function(){Dt=!1},n0=()=>Dt,r0=function(t){Ht=t},xt=()=>Ht!==void 0?Ht:pt().sequence.wrap,a0=function(){lt={},Jt={},Zt={},mt=[],Z=[],Dt=!1,De()},o0=function(t){const e=t.trim(),o={text:e.replace(/^:?(?:no)?wrap:/,"").trim(),wrap:e.match(/^:?wrap:/)!==null?!0:e.match(/^:?nowrap:/)!==null?!1:void 0};return G.debug("parseMessage:",o),o},c0=function(t){const e=t.match(/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/);let o=e!=null&&e[1]?e[1].trim():"transparent",i=e!=null&&e[2]?e[2].trim():void 0;if(window&&window.CSS)window.CSS.supports("color",o)||(o="transparent",i=t.trim());else{const n=new Option().style;n.color=o,n.color!==o&&(o="transparent",i=t.trim())}return{color:o,text:i!==void 0?Mt(i.replace(/^:?(?:no)?wrap:/,""),pt()):void 0,wrap:i!==void 0?i.match(/^:?wrap:/)!==null?!0:i.match(/^:?nowrap:/)!==null?!1:void 0:void 0}},Pt={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32},l0={FILLED:0,OPEN:1},h0={LEFTOF:0,RIGHTOF:1,OVER:2},he=function(t,e,o){o.text,o.wrap===void 0&&xt()||o.wrap;const i=[].concat(t,t);Z.push({from:i[0],to:i[1],message:o.text,wrap:o.wrap===void 0&&xt()||!!o.wrap,type:Pt.NOTE,placement:e})},de=function(t,e){const o=Lt(t);try{let i=Mt(e.text,pt());i=i.replace(/&amp;/g,"&"),i=i.replace(/&equals;/g,"=");const s=JSON.parse(i);Qt(o,s)}catch(i){G.error("error while parsing actor link text",i)}},d0=function(t,e){const o=Lt(t);try{const l={};let h=Mt(e.text,pt());var i=h.indexOf("@");h=h.replace(/&amp;/g,"&"),h=h.replace(/&equals;/g,"=");var s=h.slice(0,i-1).trim(),n=h.slice(i+1).trim();l[s]=n,Qt(o,l)}catch(l){G.error("error while parsing actor link text",l)}};function Qt(t,e){if(t.links==null)t.links=e;else for(let o in e)t.links[o]=e[o]}const pe=function(t,e){const o=Lt(t);try{let i=Mt(e.text,pt());const s=JSON.parse(i);ue(o,s)}catch(i){G.error("error while parsing actor properties text",i)}};function ue(t,e){if(t.properties==null)t.properties=e;else for(let o in e)t.properties[o]=e[o]}function p0(){ct=void 0}const fe=function(t,e){const o=Lt(t),i=document.getElementById(e.text);try{const s=i.innerHTML,n=JSON.parse(s);n.properties&&ue(o,n.properties),n.links&&Qt(o,n.links)}catch(s){G.error("error while parsing actor details text",s)}},u0=function(t,e){if(t!==void 0&&t.properties!==void 0)return t.properties[e]},ge=function(t){if(Array.isArray(t))t.forEach(function(e){ge(e)});else switch(t.type){case"sequenceIndex":Z.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":Ut(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(lt[t.actor])throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");kt=t.actor,Ut(t.actor,t.actor,t.description,t.draw),Jt[t.actor]=Z.length;break;case"destroyParticipant":Et=t.actor,Zt[t.actor]=Z.length;break;case"activeStart":V(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":V(t.actor,void 0,void 0,t.signalType);break;case"addNote":he(t.actor,t.placement,t.text);break;case"addLinks":de(t.actor,t.text);break;case"addALink":d0(t.actor,t.text);break;case"addProperties":pe(t.actor,t.text);break;case"addDetails":fe(t.actor,t.text);break;case"addMessage":if(kt){if(t.to!==kt)throw new Error("The created participant "+kt+" does not have an associated creating message after its declaration. Please check the sequence diagram.");kt=void 0}else if(Et){if(t.to!==Et&&t.from!==Et)throw new Error("The destroyed participant "+Et+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");Et=void 0}V(t.from,t.to,t.msg,t.signalType);break;case"boxStart":Ue(t.boxData);break;case"boxEnd":p0();break;case"loopStart":V(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":V(void 0,void 0,void 0,t.signalType);break;case"rectStart":V(void 0,void 0,t.color,t.signalType);break;case"rectEnd":V(void 0,void 0,void 0,t.signalType);break;case"optStart":V(void 0,void 0,t.optText,t.signalType);break;case"optEnd":V(void 0,void 0,void 0,t.signalType);break;case"altStart":V(void 0,void 0,t.altText,t.signalType);break;case"else":V(void 0,void 0,t.altText,t.signalType);break;case"altEnd":V(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":oe(t.text);break;case"parStart":V(void 0,void 0,t.parText,t.signalType);break;case"and":V(void 0,void 0,t.parText,t.signalType);break;case"parEnd":V(void 0,void 0,void 0,t.signalType);break;case"criticalStart":V(void 0,void 0,t.criticalText,t.signalType);break;case"option":V(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":V(void 0,void 0,void 0,t.signalType);break;case"breakStart":V(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":V(void 0,void 0,void 0,t.signalType);break}},f0={addActor:Ut,addMessage:Ge,addSignal:V,addLinks:de,addDetails:fe,addProperties:pe,autoWrap:xt,setWrap:r0,enableSequenceNumbers:i0,disableSequenceNumbers:s0,showSequenceNumbers:n0,getMessages:Ze,getActors:je,getCreatedActors:$e,getDestroyedActors:t0,getActor:Lt,getActorKeys:e0,getActorProperty:u0,getAccTitle:Ie,getBoxes:Qe,getDiagramTitle:Ae,setDiagramTitle:Ne,parseDirective:He,getConfig:()=>pt().sequence,clear:a0,parseMessage:o0,parseBoxData:c0,LINETYPE:Pt,ARROWTYPE:l0,PLACEMENT:h0,addNote:he,setAccTitle:oe,apply:ge,setAccDescription:Se,getAccDescription:Me,hasAtLeastOneBox:Xe,hasAtLeastOneBoxWithTitle:Je},g0=t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,x0=g0,gt=18*2,jt=function(t,e){return Ye(t,e)},xe=(t,e)=>{Oe(()=>{const o=document.querySelectorAll(t);o.length!==0&&(o[0].addEventListener("mouseover",function(){b0("actor"+e+"_popup")}),o[0].addEventListener("mouseout",function(){m0("actor"+e+"_popup")}))})},T0=function(t,e,o,i,s){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0)return{height:0,width:0};const n=e.links,l=e.actorCnt,h=e.rectData;var p="none";s&&(p="block !important");const r=t.append("g");r.attr("id","actor"+l+"_popup"),r.attr("class","actorPopupMenu"),r.attr("display",p),xe("#actor"+l+"_popup",l);var g="";h.class!==void 0&&(g=" "+h.class);let y=h.width>o?h.width:o;const u=r.append("rect");if(u.attr("class","actorPopupMenuPanel"+g),u.attr("x",h.x),u.attr("y",h.height),u.attr("fill",h.fill),u.attr("stroke",h.stroke),u.attr("width",y),u.attr("height",h.height),u.attr("rx",h.rx),u.attr("ry",h.ry),n!=null){var x=20;for(let N in n){var b=r.append("a"),k=ce.sanitizeUrl(n[N]);b.attr("xlink:href",k),b.attr("target","_blank"),O0(i)(N,b,h.x+10,h.height+x,y,20,{class:"actor"},i),x+=30}}return u.attr("height",x),{height:h.height+x,width:y}},y0=function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = 'block'; }"},E0=function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = 'none'; }"},b0=function(t){var e=document.getElementById(t);e!=null&&(e.style.display="block")},m0=function(t){var e=document.getElementById(t);e!=null&&(e.style.display="none")},wt=function(t,e){let o=0,i=0;const s=e.text.split(w.lineBreakRegex),[n,l]=le(e.fontSize);let h=[],p=0,r=()=>e.y;if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0)switch(e.valign){case"top":case"start":r=()=>Math.round(e.y+e.textMargin);break;case"middle":case"center":r=()=>Math.round(e.y+(o+i+e.textMargin)/2);break;case"bottom":case"end":r=()=>Math.round(e.y+(o+i+2*e.textMargin)-e.textMargin);break}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle";break}for(let[g,y]of s.entries()){e.textMargin!==void 0&&e.textMargin===0&&n!==void 0&&(p=g*n);const u=t.append("text");u.attr("x",e.x),u.attr("y",r()),e.anchor!==void 0&&u.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),e.fontFamily!==void 0&&u.style("font-family",e.fontFamily),l!==void 0&&u.style("font-size",l),e.fontWeight!==void 0&&u.style("font-weight",e.fontWeight),e.fill!==void 0&&u.attr("fill",e.fill),e.class!==void 0&&u.attr("class",e.class),e.dy!==void 0?u.attr("dy",e.dy):p!==0&&u.attr("dy",p);const x=y||Be;if(e.tspan){const b=u.append("tspan");b.attr("x",e.x),e.fill!==void 0&&b.attr("fill",e.fill),b.text(x)}else u.text(x);e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0&&(i+=(u._groups||u)[0][0].getBBox().height,o=i),h.push(u)}return h},Te=function(t,e){function o(s,n,l,h,p){return s+","+n+" "+(s+l)+","+n+" "+(s+l)+","+(n+h-p)+" "+(s+l-p*1.2)+","+(n+h)+" "+s+","+(n+h)}const i=t.append("polygon");return i.attr("points",o(e.x,e.y,e.width,e.height,7)),i.attr("class","labelBox"),e.y=e.y+e.height/2,wt(t,e),i};let st=-1;const ye=(t,e,o,i)=>{t.select&&o.forEach(s=>{const n=e[s],l=t.select("#actor"+n.actorCnt);!i.mirrorActors&&n.stopy?l.attr("y2",n.stopy+n.height/2):i.mirrorActors&&l.attr("y2",n.stopy)})},w0=function(t,e,o,i){const s=i?e.stopy:e.starty,n=e.x+e.width/2,l=s+5,h=t.append("g").lower();var p=h;i||(st++,p.append("line").attr("id","actor"+st).attr("x1",n).attr("y1",l).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999"),p=h.append("g"),e.actorCnt=st,e.links!=null&&(p.attr("id","root-"+st),xe("#root-"+st,st)));const r=Rt();var g="actor";e.properties!=null&&e.properties.class?g=e.properties.class:r.fill="#eaeaea",r.x=e.x,r.y=s,r.width=e.width,r.height=e.height,r.class=g,r.rx=3,r.ry=3;const y=jt(p,r);if(e.rectData=r,e.properties!=null&&e.properties.icon){const x=e.properties.icon.trim();x.charAt(0)==="@"?We(p,r.x+r.width-20,r.y+10,x.substr(1)):qe(p,r.x+r.width-20,r.y+10,x)}$t(o)(e.description,p,r.x,r.y,r.width,r.height,{class:"actor"},o);let u=e.height;if(y.node){const x=y.node().getBBox();e.height=x.height,u=x.height}return u},v0=function(t,e,o,i){const s=i?e.stopy:e.starty,n=e.x+e.width/2,l=s+80;t.lower(),i||(st++,t.append("line").attr("id","actor"+st).attr("x1",n).attr("y1",l).attr("x2",n).attr("y2",2e3).attr("class","actor-line").attr("class","200").attr("stroke-width","0.5px").attr("stroke","#999"),e.actorCnt=st);const h=t.append("g");h.attr("class","actor-man");const p=Rt();p.x=e.x,p.y=s,p.fill="#eaeaea",p.width=e.width,p.height=e.height,p.class="actor",p.rx=3,p.ry=3,h.append("line").attr("id","actor-man-torso"+st).attr("x1",n).attr("y1",s+25).attr("x2",n).attr("y2",s+45),h.append("line").attr("id","actor-man-arms"+st).attr("x1",n-gt/2).attr("y1",s+33).attr("x2",n+gt/2).attr("y2",s+33),h.append("line").attr("x1",n-gt/2).attr("y1",s+60).attr("x2",n).attr("y2",s+45),h.append("line").attr("x1",n).attr("y1",s+45).attr("x2",n+gt/2-2).attr("y2",s+60);const r=h.append("circle");r.attr("cx",e.x+e.width/2),r.attr("cy",s+10),r.attr("r",15),r.attr("width",e.width),r.attr("height",e.height);const g=h.node().getBBox();return e.height=g.height,$t(o)(e.description,h,p.x,p.y+35,p.width,p.height,{class:"actor"},o),e.height},_0=function(t,e,o,i){switch(e.type){case"actor":return v0(t,e,o,i);case"participant":return w0(t,e,o,i)}},k0=function(t,e,o){const s=t.append("g");Ee(s,e),e.name&&$t(o)(e.name,s,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},o),s.lower()},P0=function(t){return t.append("g")},L0=function(t,e,o,i,s){const n=Rt(),l=e.anchored;n.x=e.startx,n.y=e.starty,n.class="activation"+s%3,n.width=e.stopx-e.startx,n.height=o-e.starty,jt(l,n)},I0=function(t,e,o,i){const{boxMargin:s,boxTextMargin:n,labelBoxHeight:l,labelBoxWidth:h,messageFontFamily:p,messageFontSize:r,messageFontWeight:g}=i,y=t.append("g"),u=function(k,N,D,S){return y.append("line").attr("x1",k).attr("y1",N).attr("x2",D).attr("y2",S).attr("class","loopLine")};u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),e.sections!==void 0&&e.sections.forEach(function(k){u(e.startx,k.y,e.stopx,k.y).style("stroke-dasharray","3, 3")});let x=Xt();x.text=o,x.x=e.startx,x.y=e.starty,x.fontFamily=p,x.fontSize=r,x.fontWeight=g,x.anchor="middle",x.valign="middle",x.tspan=!1,x.width=h||50,x.height=l||20,x.textMargin=n,x.class="labelText",Te(y,x),x=be(),x.text=e.title,x.x=e.startx+h/2+(e.stopx-e.startx)/2,x.y=e.starty+s+n,x.anchor="middle",x.valign="middle",x.textMargin=n,x.class="loopText",x.fontFamily=p,x.fontSize=r,x.fontWeight=g,x.wrap=!0;let b=wt(y,x);return e.sectionTitles!==void 0&&e.sectionTitles.forEach(function(k,N){if(k.message){x.text=k.message,x.x=e.startx+(e.stopx-e.startx)/2,x.y=e.sections[N].y+s+n,x.class="loopText",x.anchor="middle",x.valign="middle",x.tspan=!1,x.fontFamily=p,x.fontSize=r,x.fontWeight=g,x.wrap=e.wrap,b=wt(y,x);let D=Math.round(b.map(S=>(S._groups||S)[0][0].getBBox().height).reduce((S,W)=>S+W));e.sections[N].height+=D-(s+n)}}),e.height=Math.round(e.stopy-e.starty),y},Ee=function(t,e){Fe(t,e)},A0=function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},N0=function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},S0=function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},M0=function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},R0=function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},D0=function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},V0=function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},be=function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},C0=function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},$t=function(){function t(s,n,l,h,p,r,g){const y=n.append("text").attr("x",l+p/2).attr("y",h+r/2+5).style("text-anchor","middle").text(s);i(y,g)}function e(s,n,l,h,p,r,g,y){const{actorFontSize:u,actorFontFamily:x,actorFontWeight:b}=y,[k,N]=le(u),D=s.split(w.lineBreakRegex);for(let S=0;S<D.length;S++){const W=S*k-k*(D.length-1)/2,M=n.append("text").attr("x",l+p/2).attr("y",h).style("text-anchor","middle").style("font-size",N).style("font-weight",b).style("font-family",x);M.append("tspan").attr("x",l+p/2).attr("dy",W).text(D[S]),M.attr("y",h+r/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),i(M,g)}}function o(s,n,l,h,p,r,g,y){const u=n.append("switch"),b=u.append("foreignObject").attr("x",l).attr("y",h).attr("width",p).attr("height",r).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");b.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(s),e(s,u,l,h,p,r,g,y),i(b,g)}function i(s,n){for(const l in n)n.hasOwnProperty(l)&&s.attr(l,n[l])}return function(s){return s.textPlacement==="fo"?o:s.textPlacement==="old"?t:e}}(),O0=function(){function t(s,n,l,h,p,r,g){const y=n.append("text").attr("x",l).attr("y",h).style("text-anchor","start").text(s);i(y,g)}function e(s,n,l,h,p,r,g,y){const{actorFontSize:u,actorFontFamily:x,actorFontWeight:b}=y,k=s.split(w.lineBreakRegex);for(let N=0;N<k.length;N++){const D=N*u-u*(k.length-1)/2,S=n.append("text").attr("x",l).attr("y",h).style("text-anchor","start").style("font-size",u).style("font-weight",b).style("font-family",x);S.append("tspan").attr("x",l).attr("dy",D).text(k[N]),S.attr("y",h+r/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),i(S,g)}}function o(s,n,l,h,p,r,g,y){const u=n.append("switch"),b=u.append("foreignObject").attr("x",l).attr("y",h).attr("width",p).attr("height",r).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");b.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(s),e(s,u,l,h,p,r,g,y),i(b,g)}function i(s,n){for(const l in n)n.hasOwnProperty(l)&&s.attr(l,n[l])}return function(s){return s.textPlacement==="fo"?o:s.textPlacement==="old"?t:e}}(),C={drawRect:jt,drawText:wt,drawLabel:Te,drawActor:_0,drawBox:k0,drawPopup:T0,anchorElement:P0,drawActivation:L0,drawLoop:I0,drawBackgroundRect:Ee,insertArrowHead:M0,insertArrowFilledHead:R0,insertSequenceNumber:D0,insertArrowCrossHead:V0,insertDatabaseIcon:A0,insertComputerIcon:N0,insertClockIcon:S0,getTextObj:be,getNoteRect:C0,popupMenu:y0,popdownMenu:E0,fixLifeLineHeights:ye,sanitizeUrl:ce.sanitizeUrl};let a={};const f={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(t=>t.height||0))+(this.loops.length===0?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.messages.length===0?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.notes.length===0?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},clear:function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},addBox:function(t){this.boxes.push(t)},addActor:function(t){this.actors.push(t)},addLoop:function(t){this.loops.push(t)},addMessage:function(t){this.messages.push(t)},addNote:function(t){this.notes.push(t)},lastActor:function(){return this.actors[this.actors.length-1]},lastLoop:function(){return this.loops[this.loops.length-1]},lastMessage:function(){return this.messages[this.messages.length-1]},lastNote:function(){return this.notes[this.notes.length-1]},actors:[],boxes:[],loops:[],messages:[],notes:[]},init:function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,we(pt())},updateVal:function(t,e,o,i){t[e]===void 0?t[e]=o:t[e]=i(o,t[e])},updateBounds:function(t,e,o,i){const s=this;let n=0;function l(h){return function(r){n++;const g=s.sequenceItems.length-n+1;s.updateVal(r,"starty",e-g*a.boxMargin,Math.min),s.updateVal(r,"stopy",i+g*a.boxMargin,Math.max),s.updateVal(f.data,"startx",t-g*a.boxMargin,Math.min),s.updateVal(f.data,"stopx",o+g*a.boxMargin,Math.max),h!=="activation"&&(s.updateVal(r,"startx",t-g*a.boxMargin,Math.min),s.updateVal(r,"stopx",o+g*a.boxMargin,Math.max),s.updateVal(f.data,"starty",e-g*a.boxMargin,Math.min),s.updateVal(f.data,"stopy",i+g*a.boxMargin,Math.max))}}this.sequenceItems.forEach(l()),this.activations.forEach(l("activation"))},insert:function(t,e,o,i){const s=w.getMin(t,o),n=w.getMax(t,o),l=w.getMin(e,i),h=w.getMax(e,i);this.updateVal(f.data,"startx",s,Math.min),this.updateVal(f.data,"starty",l,Math.min),this.updateVal(f.data,"stopx",n,Math.max),this.updateVal(f.data,"stopy",h,Math.max),this.updateBounds(s,l,n,h)},newActivation:function(t,e,o){const i=o[t.from.actor],s=Vt(t.from.actor).length||0,n=i.x+i.width/2+(s-1)*a.activationWidth/2;this.activations.push({startx:n,starty:this.verticalPos+2,stopx:n+a.activationWidth,stopy:void 0,actor:t.from.actor,anchored:C.anchorElement(e)})},endActivation:function(t){const e=this.activations.map(function(o){return o.actor}).lastIndexOf(t.from.actor);return this.activations.splice(e,1)[0]},createLoop:function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},newLoop:function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},endLoop:function(){return this.sequenceItems.pop()},isLoopOverlap:function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},addSectionToLoop:function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:f.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},saveVerticalPos:function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},resetVerticalPos:function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},bumpVerticalPos:function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=w.getMax(this.data.stopy,this.verticalPos)},getVerticalPos:function(){return this.verticalPos},getBounds:function(){return{bounds:this.data,models:this.models}}},B0=function(t,e){f.bumpVerticalPos(a.boxMargin),e.height=a.boxMargin,e.starty=f.getVerticalPos();const o=Rt();o.x=e.startx,o.y=e.starty,o.width=e.width||a.width,o.class="note";const i=t.append("g"),s=C.drawRect(i,o),n=Xt();n.x=e.startx,n.y=e.starty,n.width=o.width,n.dy="1em",n.text=e.message,n.class="noteText",n.fontFamily=a.noteFontFamily,n.fontSize=a.noteFontSize,n.fontWeight=a.noteFontWeight,n.anchor=a.noteAlign,n.textMargin=a.noteMargin,n.valign="center";const l=wt(i,n),h=Math.round(l.map(p=>(p._groups||p)[0][0].getBBox().height).reduce((p,r)=>p+r));s.attr("height",h+2*a.noteMargin),e.height+=h+2*a.noteMargin,f.bumpVerticalPos(h+2*a.noteMargin),e.stopy=e.starty+h+2*a.noteMargin,e.stopx=e.startx+o.width,f.insert(e.startx,e.starty,e.stopx,e.stopy),f.models.addNote(e)},Tt=t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),bt=t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),Kt=t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight});function Y0(t,e){f.bumpVerticalPos(10);const{startx:o,stopx:i,message:s}=e,n=w.splitBreaks(s).length,l=B.calculateTextDimensions(s,Tt(a)),h=l.height/n;e.height+=h,f.bumpVerticalPos(h);let p,r=l.height-10;const g=l.width;if(o===i){p=f.getVerticalPos()+r,a.rightAngles||(r+=a.boxMargin,p=f.getVerticalPos()+r),r+=30;const y=w.getMax(g/2,a.width/2);f.insert(o-y,f.getVerticalPos()-10+r,i+y,f.getVerticalPos()+30+r)}else r+=a.boxMargin,p=f.getVerticalPos()+r,f.insert(o,p-10,i,p);return f.bumpVerticalPos(r),e.height+=r,e.stopy=e.starty+e.height,f.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),p}const F0=function(t,e,o,i){const{startx:s,stopx:n,starty:l,message:h,type:p,sequenceIndex:r,sequenceVisible:g}=e,y=B.calculateTextDimensions(h,Tt(a)),u=Xt();u.x=s,u.y=l+10,u.width=n-s,u.class="messageText",u.dy="1em",u.text=h,u.fontFamily=a.messageFontFamily,u.fontSize=a.messageFontSize,u.fontWeight=a.messageFontWeight,u.anchor=a.messageAlign,u.valign="center",u.textMargin=a.wrapPadding,u.tspan=!1,wt(t,u);const x=y.width;let b;s===n?a.rightAngles?b=t.append("path").attr("d",`M  ${s},${o} H ${s+w.getMax(a.width/2,x/2)} V ${o+25} H ${s}`):b=t.append("path").attr("d","M "+s+","+o+" C "+(s+60)+","+(o-10)+" "+(s+60)+","+(o+30)+" "+s+","+(o+20)):(b=t.append("line"),b.attr("x1",s),b.attr("y1",o),b.attr("x2",n),b.attr("y2",o)),p===i.db.LINETYPE.DOTTED||p===i.db.LINETYPE.DOTTED_CROSS||p===i.db.LINETYPE.DOTTED_POINT||p===i.db.LINETYPE.DOTTED_OPEN?(b.style("stroke-dasharray","3, 3"),b.attr("class","messageLine1")):b.attr("class","messageLine0");let k="";a.arrowMarkerAbsolute&&(k=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,k=k.replace(/\(/g,"\\("),k=k.replace(/\)/g,"\\)")),b.attr("stroke-width",2),b.attr("stroke","none"),b.style("fill","none"),(p===i.db.LINETYPE.SOLID||p===i.db.LINETYPE.DOTTED)&&b.attr("marker-end","url("+k+"#arrowhead)"),(p===i.db.LINETYPE.SOLID_POINT||p===i.db.LINETYPE.DOTTED_POINT)&&b.attr("marker-end","url("+k+"#filled-head)"),(p===i.db.LINETYPE.SOLID_CROSS||p===i.db.LINETYPE.DOTTED_CROSS)&&b.attr("marker-end","url("+k+"#crosshead)"),(g||a.showSequenceNumbers)&&(b.attr("marker-start","url("+k+"#sequencenumber)"),t.append("text").attr("x",s).attr("y",o+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(r))},W0=function(t,e,o,i,s,n,l){let h=0,p=0,r,g=0;for(const y of i){const u=e[y],x=u.box;r&&r!=x&&(l||f.models.addBox(r),p+=a.boxMargin+r.margin),x&&x!=r&&(l||(x.x=h+p,x.y=s),p+=x.margin),u.width=u.width||a.width,u.height=w.getMax(u.height||a.height,a.height),u.margin=u.margin||a.actorMargin,g=w.getMax(g,u.height),o[u.name]&&(p+=u.width/2),u.x=h+p,u.starty=f.getVerticalPos(),f.insert(u.x,s,u.x+u.width,u.height),h+=u.width+p,u.box&&(u.box.width=h+x.margin-u.box.x),p=u.margin,r=u.box,f.models.addActor(u)}r&&!l&&f.models.addBox(r),f.bumpVerticalPos(g)},Gt=function(t,e,o,i){if(i){let s=0;f.bumpVerticalPos(a.boxMargin*2);for(const n of o){const l=e[n];l.stopy||(l.stopy=f.getVerticalPos());const h=C.drawActor(t,l,a,!0);s=w.getMax(s,h)}f.bumpVerticalPos(s+a.boxMargin)}else for(const s of o){const n=e[s];C.drawActor(t,n,a,!1)}},me=function(t,e,o,i){let s=0,n=0;for(const l of o){const h=e[l],p=U0(h),r=C.drawPopup(t,h,p,a,a.forceMenus,i);r.height>s&&(s=r.height),r.width+h.x>n&&(n=r.width+h.x)}return{maxHeight:s,maxWidth:n}},we=function(t){Ve(a,t),t.fontFamily&&(a.actorFontFamily=a.noteFontFamily=a.messageFontFamily=t.fontFamily),t.fontSize&&(a.actorFontSize=a.noteFontSize=a.messageFontSize=t.fontSize),t.fontWeight&&(a.actorFontWeight=a.noteFontWeight=a.messageFontWeight=t.fontWeight)},Vt=function(t){return f.activations.filter(function(e){return e.actor===t})},ae=function(t,e){const o=e[t],i=Vt(t),s=i.reduce(function(l,h){return w.getMin(l,h.startx)},o.x+o.width/2),n=i.reduce(function(l,h){return w.getMax(l,h.stopx)},o.x+o.width/2);return[s,n]};function nt(t,e,o,i,s){f.bumpVerticalPos(o);let n=i;if(e.id&&e.message&&t[e.id]){const l=t[e.id].width,h=Tt(a);e.message=B.wrapLabel(`[${e.message}]`,l-2*a.wrapPadding,h),e.width=l,e.wrap=!0;const p=B.calculateTextDimensions(e.message,h),r=w.getMax(p.height,a.labelBoxHeight);n=i+r,G.debug(`${r} - ${e.message}`)}s(e),f.bumpVerticalPos(n)}function q0(t,e,o,i,s,n,l){function h(r,g){r.x<s[t.from].x?(f.insert(e.stopx-g,e.starty,e.startx,e.stopy+r.height/2+a.noteMargin),e.stopx=e.stopx+g):(f.insert(e.startx,e.starty,e.stopx+g,e.stopy+r.height/2+a.noteMargin),e.stopx=e.stopx-g)}function p(r,g){r.x<s[t.to].x?(f.insert(e.startx-g,e.starty,e.stopx,e.stopy+r.height/2+a.noteMargin),e.startx=e.startx+g):(f.insert(e.stopx,e.starty,e.startx+g,e.stopy+r.height/2+a.noteMargin),e.startx=e.startx-g)}if(n[t.to]==i){const r=s[t.to],g=r.type=="actor"?gt/2+3:r.width/2+3;h(r,g),r.starty=o-r.height/2,f.bumpVerticalPos(r.height/2)}else if(l[t.from]==i){const r=s[t.from];if(a.mirrorActors){const g=r.type=="actor"?gt/2:r.width/2;p(r,g)}r.stopy=o-r.height/2,f.bumpVerticalPos(r.height/2)}else if(l[t.to]==i){const r=s[t.to];if(a.mirrorActors){const g=r.type=="actor"?gt/2+3:r.width/2+3;h(r,g)}r.stopy=o-r.height/2,f.bumpVerticalPos(r.height/2)}}const z0=function(t,e,o,i){const{securityLevel:s,sequence:n}=pt();a=n;let l;s==="sandbox"&&(l=St("#i"+e));const h=s==="sandbox"?St(l.nodes()[0].contentDocument.body):St("body"),p=s==="sandbox"?l.nodes()[0].contentDocument:document;f.init(),G.debug(i.db);const r=s==="sandbox"?h.select(`[id="${e}"]`):St(`[id="${e}"]`),g=i.db.getActors(),y=i.db.getCreatedActors(),u=i.db.getDestroyedActors(),x=i.db.getBoxes();let b=i.db.getActorKeys();const k=i.db.getMessages(),N=i.db.getDiagramTitle(),D=i.db.hasAtLeastOneBox(),S=i.db.hasAtLeastOneBoxWithTitle(),W=H0(g,k,i);if(a.height=K0(g,W,x),C.insertComputerIcon(r),C.insertDatabaseIcon(r),C.insertClockIcon(r),D&&(f.bumpVerticalPos(a.boxMargin),S&&f.bumpVerticalPos(x[0].textMaxHeight)),a.hideUnusedParticipants===!0){const E=new Set;k.forEach(L=>{E.add(L.from),E.add(L.to)}),b=b.filter(L=>E.has(L))}W0(r,g,y,b,0,k,!1);const M=J0(k,g,W,i);C.insertArrowHead(r),C.insertArrowCrossHead(r),C.insertArrowFilledHead(r),C.insertSequenceNumber(r);function it(E,L){const I=f.endActivation(E);I.starty+18>L&&(I.starty=L-6,L+=12),C.drawActivation(r,I,L,a,Vt(E.from.actor).length),f.insert(I.startx,L-10,I.stopx,L)}let z=1,X=1;const Q=[],j=[];k.forEach(function(E,L){let I,P,U;switch(E.type){case i.db.LINETYPE.NOTE:f.resetVerticalPos(),P=E.noteModel,B0(r,P);break;case i.db.LINETYPE.ACTIVE_START:f.newActivation(E,r,g);break;case i.db.LINETYPE.ACTIVE_END:it(E,f.getVerticalPos());break;case i.db.LINETYPE.LOOP_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A));break;case i.db.LINETYPE.LOOP_END:I=f.endLoop(),C.drawLoop(r,I,"loop",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;case i.db.LINETYPE.RECT_START:nt(M,E,a.boxMargin,a.boxMargin,A=>f.newLoop(void 0,A.message));break;case i.db.LINETYPE.RECT_END:I=f.endLoop(),j.push(I),f.models.addLoop(I),f.bumpVerticalPos(I.stopy-f.getVerticalPos());break;case i.db.LINETYPE.OPT_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A));break;case i.db.LINETYPE.OPT_END:I=f.endLoop(),C.drawLoop(r,I,"opt",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;case i.db.LINETYPE.ALT_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A));break;case i.db.LINETYPE.ALT_ELSE:nt(M,E,a.boxMargin+a.boxTextMargin,a.boxMargin,A=>f.addSectionToLoop(A));break;case i.db.LINETYPE.ALT_END:I=f.endLoop(),C.drawLoop(r,I,"alt",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;case i.db.LINETYPE.PAR_START:case i.db.LINETYPE.PAR_OVER_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A)),f.saveVerticalPos();break;case i.db.LINETYPE.PAR_AND:nt(M,E,a.boxMargin+a.boxTextMargin,a.boxMargin,A=>f.addSectionToLoop(A));break;case i.db.LINETYPE.PAR_END:I=f.endLoop(),C.drawLoop(r,I,"par",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;case i.db.LINETYPE.AUTONUMBER:z=E.message.start||z,X=E.message.step||X,E.message.visible?i.db.enableSequenceNumbers():i.db.disableSequenceNumbers();break;case i.db.LINETYPE.CRITICAL_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A));break;case i.db.LINETYPE.CRITICAL_OPTION:nt(M,E,a.boxMargin+a.boxTextMargin,a.boxMargin,A=>f.addSectionToLoop(A));break;case i.db.LINETYPE.CRITICAL_END:I=f.endLoop(),C.drawLoop(r,I,"critical",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;case i.db.LINETYPE.BREAK_START:nt(M,E,a.boxMargin,a.boxMargin+a.boxTextMargin,A=>f.newLoop(A));break;case i.db.LINETYPE.BREAK_END:I=f.endLoop(),C.drawLoop(r,I,"break",a),f.bumpVerticalPos(I.stopy-f.getVerticalPos()),f.models.addLoop(I);break;default:try{U=E.msgModel,U.starty=f.getVerticalPos(),U.sequenceIndex=z,U.sequenceVisible=i.db.showSequenceNumbers();const A=Y0(r,U);q0(E,U,A,L,g,y,u),Q.push({messageModel:U,lineStartY:A}),f.models.addMessage(U)}catch(A){G.error("error while drawing message",A)}}[i.db.LINETYPE.SOLID_OPEN,i.db.LINETYPE.DOTTED_OPEN,i.db.LINETYPE.SOLID,i.db.LINETYPE.DOTTED,i.db.LINETYPE.SOLID_CROSS,i.db.LINETYPE.DOTTED_CROSS,i.db.LINETYPE.SOLID_POINT,i.db.LINETYPE.DOTTED_POINT].includes(E.type)&&(z=z+X)}),G.debug("createdActors",y),G.debug("destroyedActors",u),Gt(r,g,b,!1),Q.forEach(E=>F0(r,E.messageModel,E.lineStartY,i)),a.mirrorActors&&Gt(r,g,b,!0),j.forEach(E=>C.drawBackgroundRect(r,E)),ye(r,g,b,a),f.models.boxes.forEach(function(E){E.height=f.getVerticalPos()-E.y,f.insert(E.x,E.y,E.x+E.width,E.height),E.startx=E.x,E.starty=E.y,E.stopx=E.startx+E.width,E.stopy=E.starty+E.height,E.stroke="rgb(0,0,0, 0.5)",C.drawBox(r,E,a)}),D&&f.bumpVerticalPos(a.boxMargin);const F=me(r,g,b,p),{bounds:O}=f.getBounds();let q=O.stopy-O.starty;q<F.maxHeight&&(q=F.maxHeight);let H=q+2*a.diagramMarginY;a.mirrorActors&&(H=H-a.boxMargin+a.bottomMarginAdj);let J=O.stopx-O.startx;J<F.maxWidth&&(J=F.maxWidth);const $=J+2*a.diagramMarginX;N&&r.append("text").text(N).attr("x",(O.stopx-O.startx)/2-2*a.diagramMarginX).attr("y",-25),Ce(r,H,$,a.useMaxWidth);const tt=N?40:0;r.attr("viewBox",O.startx-a.diagramMarginX+" -"+(a.diagramMarginY+tt)+" "+$+" "+(H+tt)),G.debug("models:",f.models)};function H0(t,e,o){const i={};return e.forEach(function(s){if(t[s.to]&&t[s.from]){const n=t[s.to];if(s.placement===o.db.PLACEMENT.LEFTOF&&!n.prevActor||s.placement===o.db.PLACEMENT.RIGHTOF&&!n.nextActor)return;const l=s.placement!==void 0,h=!l,p=l?bt(a):Tt(a),r=s.wrap?B.wrapLabel(s.message,a.width-2*a.wrapPadding,p):s.message,y=B.calculateTextDimensions(r,p).width+2*a.wrapPadding;h&&s.from===n.nextActor?i[s.to]=w.getMax(i[s.to]||0,y):h&&s.from===n.prevActor?i[s.from]=w.getMax(i[s.from]||0,y):h&&s.from===s.to?(i[s.from]=w.getMax(i[s.from]||0,y/2),i[s.to]=w.getMax(i[s.to]||0,y/2)):s.placement===o.db.PLACEMENT.RIGHTOF?i[s.from]=w.getMax(i[s.from]||0,y):s.placement===o.db.PLACEMENT.LEFTOF?i[n.prevActor]=w.getMax(i[n.prevActor]||0,y):s.placement===o.db.PLACEMENT.OVER&&(n.prevActor&&(i[n.prevActor]=w.getMax(i[n.prevActor]||0,y/2)),n.nextActor&&(i[s.from]=w.getMax(i[s.from]||0,y/2)))}}),G.debug("maxMessageWidthPerActor:",i),i}const U0=function(t){let e=0;const o=Kt(a);for(const i in t.links){const n=B.calculateTextDimensions(i,o).width+2*a.wrapPadding+2*a.boxMargin;e<n&&(e=n)}return e};function K0(t,e,o){let i=0;Object.keys(t).forEach(n=>{const l=t[n];l.wrap&&(l.description=B.wrapLabel(l.description,a.width-2*a.wrapPadding,Kt(a)));const h=B.calculateTextDimensions(l.description,Kt(a));l.width=l.wrap?a.width:w.getMax(a.width,h.width+2*a.wrapPadding),l.height=l.wrap?w.getMax(h.height,a.height):a.height,i=w.getMax(i,l.height)});for(const n in e){const l=t[n];if(!l)continue;const h=t[l.nextActor];if(!h){const y=e[n]+a.actorMargin-l.width/2;l.margin=w.getMax(y,a.actorMargin);continue}const r=e[n]+a.actorMargin-l.width/2-h.width/2;l.margin=w.getMax(r,a.actorMargin)}let s=0;return o.forEach(n=>{const l=Tt(a);let h=n.actorKeys.reduce((g,y)=>g+=t[y].width+(t[y].margin||0),0);h-=2*a.boxTextMargin,n.wrap&&(n.name=B.wrapLabel(n.name,h-2*a.wrapPadding,l));const p=B.calculateTextDimensions(n.name,l);s=w.getMax(p.height,s);const r=w.getMax(h,p.width+2*a.wrapPadding);if(n.margin=a.boxTextMargin,h<r){const g=(r-h)/2;n.margin+=g}}),o.forEach(n=>n.textMaxHeight=s),w.getMax(i,a.height)}const G0=function(t,e,o){const i=e[t.from].x,s=e[t.to].x,n=t.wrap&&t.message;let l=B.calculateTextDimensions(n?B.wrapLabel(t.message,a.width,bt(a)):t.message,bt(a));const h={width:n?a.width:w.getMax(a.width,l.width+2*a.noteMargin),height:0,startx:e[t.from].x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===o.db.PLACEMENT.RIGHTOF?(h.width=n?w.getMax(a.width,l.width):w.getMax(e[t.from].width/2+e[t.to].width/2,l.width+2*a.noteMargin),h.startx=i+(e[t.from].width+a.actorMargin)/2):t.placement===o.db.PLACEMENT.LEFTOF?(h.width=n?w.getMax(a.width,l.width+2*a.noteMargin):w.getMax(e[t.from].width/2+e[t.to].width/2,l.width+2*a.noteMargin),h.startx=i-h.width+(e[t.from].width-a.actorMargin)/2):t.to===t.from?(l=B.calculateTextDimensions(n?B.wrapLabel(t.message,w.getMax(a.width,e[t.from].width),bt(a)):t.message,bt(a)),h.width=n?w.getMax(a.width,e[t.from].width):w.getMax(e[t.from].width,a.width,l.width+2*a.noteMargin),h.startx=i+(e[t.from].width-h.width)/2):(h.width=Math.abs(i+e[t.from].width/2-(s+e[t.to].width/2))+a.actorMargin,h.startx=i<s?i+e[t.from].width/2-a.actorMargin/2:s+e[t.to].width/2-a.actorMargin/2),n&&(h.message=B.wrapLabel(t.message,h.width-2*a.wrapPadding,bt(a))),G.debug(`NM:[${h.startx},${h.stopx},${h.starty},${h.stopy}:${h.width},${h.height}=${t.message}]`),h},X0=function(t,e,o){let i=!1;if([o.db.LINETYPE.SOLID_OPEN,o.db.LINETYPE.DOTTED_OPEN,o.db.LINETYPE.SOLID,o.db.LINETYPE.DOTTED,o.db.LINETYPE.SOLID_CROSS,o.db.LINETYPE.DOTTED_CROSS,o.db.LINETYPE.SOLID_POINT,o.db.LINETYPE.DOTTED_POINT].includes(t.type)&&(i=!0),!i)return{};const s=ae(t.from,e),n=ae(t.to,e),l=s[0]<=n[0]?1:0,h=s[0]<n[0]?0:1,p=[...s,...n],r=Math.abs(n[h]-s[l]);t.wrap&&t.message&&(t.message=B.wrapLabel(t.message,w.getMax(r+2*a.wrapPadding,a.width),Tt(a)));const g=B.calculateTextDimensions(t.message,Tt(a));return{width:w.getMax(t.wrap?0:g.width+2*a.wrapPadding,r+2*a.wrapPadding,a.width),height:0,startx:s[l],stopx:n[h],starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,p),toBounds:Math.max.apply(null,p)}},J0=function(t,e,o,i){const s={},n=[];let l,h,p;return t.forEach(function(r){switch(r.id=B.random({length:10}),r.type){case i.db.LINETYPE.LOOP_START:case i.db.LINETYPE.ALT_START:case i.db.LINETYPE.OPT_START:case i.db.LINETYPE.PAR_START:case i.db.LINETYPE.PAR_OVER_START:case i.db.LINETYPE.CRITICAL_START:case i.db.LINETYPE.BREAK_START:n.push({id:r.id,msg:r.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case i.db.LINETYPE.ALT_ELSE:case i.db.LINETYPE.PAR_AND:case i.db.LINETYPE.CRITICAL_OPTION:r.message&&(l=n.pop(),s[l.id]=l,s[r.id]=l,n.push(l));break;case i.db.LINETYPE.LOOP_END:case i.db.LINETYPE.ALT_END:case i.db.LINETYPE.OPT_END:case i.db.LINETYPE.PAR_END:case i.db.LINETYPE.CRITICAL_END:case i.db.LINETYPE.BREAK_END:l=n.pop(),s[l.id]=l;break;case i.db.LINETYPE.ACTIVE_START:{const y=e[r.from?r.from.actor:r.to.actor],u=Vt(r.from?r.from.actor:r.to.actor).length,x=y.x+y.width/2+(u-1)*a.activationWidth/2,b={startx:x,stopx:x+a.activationWidth,actor:r.from.actor,enabled:!0};f.activations.push(b)}break;case i.db.LINETYPE.ACTIVE_END:{const y=f.activations.map(u=>u.actor).lastIndexOf(r.from.actor);delete f.activations.splice(y,1)[0]}break}r.placement!==void 0?(h=G0(r,e,i),r.noteModel=h,n.forEach(y=>{l=y,l.from=w.getMin(l.from,h.startx),l.to=w.getMax(l.to,h.startx+h.width),l.width=w.getMax(l.width,Math.abs(l.from-l.to))-a.labelBoxWidth})):(p=X0(r,e,i),r.msgModel=p,p.startx&&p.stopx&&n.length>0&&n.forEach(y=>{if(l=y,p.startx===p.stopx){const u=e[r.from],x=e[r.to];l.from=w.getMin(u.x-p.width/2,u.x-u.width/2,l.from),l.to=w.getMax(x.x+p.width/2,x.x+u.width/2,l.to),l.width=w.getMax(l.width,Math.abs(l.to-l.from))-a.labelBoxWidth}else l.from=w.getMin(p.startx,l.from),l.to=w.getMax(p.stopx,l.to),l.width=w.getMax(l.width,p.width)-a.labelBoxWidth}))}),f.activations=[],G.debug("Loop type widths:",s),s},Z0={bounds:f,drawActors:Gt,drawActorsPopup:me,setConf:we,draw:z0},$0={parser:ze,db:f0,renderer:Z0,styles:x0};export{$0 as diagram};
