import{w as ln,c as J}from"./path-53f90ab3.js";import{bH as an,bI as N,bJ as w,bK as rn,bL as y,aP as on,bM as z,bN as _,bO as un,bP as t,bQ as sn,bR as tn,bS as fn}from"./doc-30bb18f4.js";function cn(l){return l.innerRadius}function yn(l){return l.outerRadius}function gn(l){return l.startAngle}function mn(l){return l.endAngle}function pn(l){return l&&l.padAngle}function dn(l,h,S,I,v,R,K,a){var q=S-l,i=I-h,n=K-v,m=a-R,r=m*q-n*i;if(!(r*r<y))return r=(n*(h-R)-m*(l-v))/r,[l+r*q,h+r*i]}function V(l,h,S,I,v,R,K){var a=l-S,q=h-I,i=(K?R:-R)/z(a*a+q*q),n=i*q,m=-i*a,r=l+n,s=h+m,f=S+n,c=I+m,L=(r+f)/2,o=(s+c)/2,p=f-r,g=c-s,A=p*p+g*g,b=v-R,T=r*c-f*s,D=(g<0?-1:1)*z(fn(0,b*b*A-T*T)),E=(T*g-p*D)/A,H=(-T*p-g*D)/A,P=(T*g+p*D)/A,d=(-T*p+g*D)/A,x=E-L,e=H-o,u=P-L,M=d-o;return x*x+e*e>u*u+M*M&&(E=P,H=d),{cx:E,cy:H,x01:-n,y01:-m,x11:E*(v/b-1),y11:H*(v/b-1)}}function vn(){var l=cn,h=yn,S=J(0),I=null,v=gn,R=mn,K=pn,a=null,q=ln(i);function i(){var n,m,r=+l.apply(this,arguments),s=+h.apply(this,arguments),f=v.apply(this,arguments)-rn,c=R.apply(this,arguments)-rn,L=un(c-f),o=c>f;if(a||(a=n=q()),s<r&&(m=s,s=r,r=m),!(s>y))a.moveTo(0,0);else if(L>on-y)a.moveTo(s*N(f),s*w(f)),a.arc(0,0,s,f,c,!o),r>y&&(a.moveTo(r*N(c),r*w(c)),a.arc(0,0,r,c,f,o));else{var p=f,g=c,A=f,b=c,T=L,D=L,E=K.apply(this,arguments)/2,H=E>y&&(I?+I.apply(this,arguments):z(r*r+s*s)),P=_(un(s-r)/2,+S.apply(this,arguments)),d=P,x=P,e,u;if(H>y){var M=sn(H/r*w(E)),B=sn(H/s*w(E));(T-=M*2)>y?(M*=o?1:-1,A+=M,b-=M):(T=0,A=b=(f+c)/2),(D-=B*2)>y?(B*=o?1:-1,p+=B,g-=B):(D=0,p=g=(f+c)/2)}var Q=s*N(p),j=s*w(p),C=r*N(b),F=r*w(b);if(P>y){var G=s*N(g),U=s*w(g),W=r*N(A),X=r*w(A),O;if(L<an)if(O=dn(Q,j,W,X,G,U,C,F)){var Y=Q-O[0],Z=j-O[1],$=G-O[0],k=U-O[1],nn=1/w(tn((Y*$+Z*k)/(z(Y*Y+Z*Z)*z($*$+k*k)))/2),en=z(O[0]*O[0]+O[1]*O[1]);d=_(P,(r-en)/(nn-1)),x=_(P,(s-en)/(nn+1))}else d=x=0}D>y?x>y?(e=V(W,X,Q,j,s,x,o),u=V(G,U,C,F,s,x,o),a.moveTo(e.cx+e.x01,e.cy+e.y01),x<P?a.arc(e.cx,e.cy,x,t(e.y01,e.x01),t(u.y01,u.x01),!o):(a.arc(e.cx,e.cy,x,t(e.y01,e.x01),t(e.y11,e.x11),!o),a.arc(0,0,s,t(e.cy+e.y11,e.cx+e.x11),t(u.cy+u.y11,u.cx+u.x11),!o),a.arc(u.cx,u.cy,x,t(u.y11,u.x11),t(u.y01,u.x01),!o))):(a.moveTo(Q,j),a.arc(0,0,s,p,g,!o)):a.moveTo(Q,j),!(r>y)||!(T>y)?a.lineTo(C,F):d>y?(e=V(C,F,G,U,r,-d,o),u=V(Q,j,W,X,r,-d,o),a.lineTo(e.cx+e.x01,e.cy+e.y01),d<P?a.arc(e.cx,e.cy,d,t(e.y01,e.x01),t(u.y01,u.x01),!o):(a.arc(e.cx,e.cy,d,t(e.y01,e.x01),t(e.y11,e.x11),!o),a.arc(0,0,r,t(e.cy+e.y11,e.cx+e.x11),t(u.cy+u.y11,u.cx+u.x11),o),a.arc(u.cx,u.cy,d,t(u.y11,u.x11),t(u.y01,u.x01),!o))):a.arc(0,0,r,b,A,o)}if(a.closePath(),n)return a=null,n+""||null}return i.centroid=function(){var n=(+l.apply(this,arguments)+ +h.apply(this,arguments))/2,m=(+v.apply(this,arguments)+ +R.apply(this,arguments))/2-an/2;return[N(m)*n,w(m)*n]},i.innerRadius=function(n){return arguments.length?(l=typeof n=="function"?n:J(+n),i):l},i.outerRadius=function(n){return arguments.length?(h=typeof n=="function"?n:J(+n),i):h},i.cornerRadius=function(n){return arguments.length?(S=typeof n=="function"?n:J(+n),i):S},i.padRadius=function(n){return arguments.length?(I=n==null?null:typeof n=="function"?n:J(+n),i):I},i.startAngle=function(n){return arguments.length?(v=typeof n=="function"?n:J(+n),i):v},i.endAngle=function(n){return arguments.length?(R=typeof n=="function"?n:J(+n),i):R},i.padAngle=function(n){return arguments.length?(K=typeof n=="function"?n:J(+n),i):K},i.context=function(n){return arguments.length?(a=n??null,i):a},i}export{vn as a};
