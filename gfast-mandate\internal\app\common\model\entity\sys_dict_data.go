// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT. Created at 2022-04-16 16:32:52
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysDictData is the golang structure for table sys_dict_data.
type SysDictData struct {
	DictCode  int64       `json:"dictCode"  description:"字典编码"`
	DictSort  int         `json:"dictSort"  description:"字典排序"`
	DictLabel string      `json:"dictLabel" description:"字典标签"`
	DictValue string      `json:"dictValue" description:"字典键值"`
	DictType  string      `json:"dictType"  description:"字典类型"`
	CssClass  string      `json:"cssClass"  description:"样式属性（其他样式扩展）"`
	ListClass string      `json:"listClass" description:"表格回显样式"`
	IsDefault int         `json:"isDefault" description:"是否默认（1是 0否）"`
	Status    int         `json:"status"    description:"状态（0正常 1停用）"`
	CreateBy  uint64      `json:"createBy"  description:"创建者"`
	UpdateBy  uint64      `json:"updateBy"  description:"更新者"`
	Remark    string      `json:"remark"    description:"备注"`
	CreatedAt *gtime.Time `json:"createdAt" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" description:"修改时间"`
}
