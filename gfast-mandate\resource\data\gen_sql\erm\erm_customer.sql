/*
==========================================================================
GFast自动生成菜单SQL
生成日期：2023-10-10 14:34:06
生成路径: resource/data/gen_sql/erm/erm_customer_menu.sql
生成人：gfast
==========================================================================
*/


-- 删除原有数据
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/list';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/get';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/add';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/edit';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/delete';
DELETE FROM `sys_auth_rule` WHERE `name` = 'api/v1/erm/ermCustomer/export';
-- 当前日期
select @now := now();
-- 目录 SQL
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at` )
VALUES(0,'api/v1/erm/ermCustomer','客户表管理','iconfont icon-fuwenbenkuang','','客户表管理',0,0,1,0,'/erm/ermCustomer','','layout/routerView/parent',0,'sys_admin',0,@now,@now);
-- 菜单父目录ID
SELECT @parentId := LAST_INSERT_ID();
-- 菜单 SQL
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at`)
VALUES(@parentId,'api/v1/erm/ermCustomer/list','客户表列表','ele-Fold','','客户表列表',1,0,1,0,'/erm/ermCustomer/list','','erm/ermCustomer/list/index',0,'sys_admin',0,@now,@now);
-- 按钮父目录ID
SELECT @parentId := LAST_INSERT_ID();
-- 按钮 SQL
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at`)
VALUES(@parentId,'api/v1/erm/ermCustomer/get','客户表查询','','','客户表查询',2,0,1,0,'','','',0,'sys_admin',0,@now,@now);
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at`)
VALUES(@parentId,'api/v1/erm/ermCustomer/add','客户表添加','','','客户表添加',2,0,1,0,'','','',0,'sys_admin',0,@now,@now);
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at`)
VALUES(@parentId,'api/v1/erm/ermCustomer/edit','客户表修改','','','客户表修改',2,0,1,0,'','','',0,'sys_admin',0,@now,@now);
INSERT INTO `sys_auth_rule` (`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_cached`,`is_hide`,`path`,`link_url`,`component`,`is_iframe`,`module_type`,`model_id`,`created_at`,`updated_at`)
VALUES(@parentId,'api/v1/erm/ermCustomer/delete','客户表删除','','','客户表删除',2,0,1,0,'','','',0,'sys_admin',0,@now,@now);
