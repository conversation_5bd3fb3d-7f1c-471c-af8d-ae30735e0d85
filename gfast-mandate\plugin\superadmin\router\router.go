// plugin/superadmin/router/router.go
func init() {
	gfast.RouterGroup("/superadmin", func(group *ghttp.RouterGroup) {
		group.Middleware(middleware.SuperAdmin) // 超管权限

		// 平台配置
		group.Group("/platform", func(p *ghttp.RouterGroup) {
			p.GET("/list", controller.Platform.List)
			p.POST("/create", controller.Platform.Create)
			p.PUT("/update/:id", controller.Platform.Update)
			p.DELETE("/:id", controller.Platform.Delete)
		})

		// 用户管理
		group.Group("/user", func(u *ghttp.RouterGroup) {
			u.GET("/list", controller.User.List)
			u.POST("/freeze", controller.User.Freeze)
			u.GET("/logs", controller.User.Logs)
		})

		// 推广管理
		group.Group("/promotion", func(pr *ghttp.RouterGroup) {
			pr.GET("/keywords", controller.Promotion.Keywords)
			pr.POST("/add-keyword", controller.Promotion.AddKeyword)
			pr.GET("/ads", controller.Promotion.Ads)
		})

		// 系统监控
		group.Group("/monitor", func(m *ghttp.RouterGroup) {
			m.GET("/server", controller.Monitor.ServerStatus)
			m.GET("/db", controller.Monitor.DBStatus)
			m.GET("/api", controller.Monitor.APIStats)
		})

		// 安全管理
		group.Group("/security", func(sec *ghttp.RouterGroup) {
			sec.GET("/logins", controller.Security.LoginLogs)
			sec.POST("/rule", controller.Security.SetLoginRule)
		})

		// 日志管理
		group.Group("/logs", func(l *ghttp.RouterGroup) {
			l.GET("/ops", controller.Logs.Operations)
			l.GET("/errors", controller.Logs.Errors)
		})
	})
}
