// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/system/dao/internal"
)

// internalSysRoleScopeDao is internal type for wrapping internal DAO implements.
type internalSysRoleScopeDao = *internal.SysRoleScopeDao

// sysRoleScopeDao is the data access object for table sys_role_scope.
// You can define custom methods on it to extend its functionality as you wish.
type sysRoleScopeDao struct {
	internalSysRoleScopeDao
}

var (
	// SysRoleScope is globally public accessible object for table sys_role_scope operations.
	SysRoleScope = sysRoleScopeDao{
		internal.NewSysRoleScopeDao(),
	}
)

// Fill with you ideas below.
