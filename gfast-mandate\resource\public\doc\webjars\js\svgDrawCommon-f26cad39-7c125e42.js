import{ag as a}from"./doc-30bb18f4.js";const l=function(e,t){const r=e.append("rect");if(r.attr("x",t.x),r.attr("y",t.y),r.attr("fill",t.fill),r.attr("stroke",t.stroke),r.attr("width",t.width),r.attr("height",t.height),r.attr("rx",t.rx),r.attr("ry",t.ry),t.attrs!=="undefined"&&t.attrs!==null)for(let s in t.attrs)r.attr(s,t.attrs[s]);return t.class!=="undefined"&&r.attr("class",t.class),r},c=function(e,t){l(e,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,stroke:t.stroke,class:"rect"}).lower()},d=function(e,t){const r=t.text.replace(/<br\s*\/?>/gi," "),s=e.append("text");s.attr("x",t.x),s.attr("y",t.y),s.attr("class","legend"),s.style("text-anchor",t.anchor),t.class!==void 0&&s.attr("class",t.class);const n=s.append("tspan");return n.attr("x",t.x+t.textMargin*2),n.text(r),s},x=function(e,t,r,s){const n=e.append("image");n.attr("x",t),n.attr("y",r);var i=a.sanitizeUrl(s);n.attr("xlink:href",i)},f=function(e,t,r,s){const n=e.append("use");n.attr("x",t),n.attr("y",r);const i=a.sanitizeUrl(s);n.attr("xlink:href","#"+i)},h=function(){return{x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}},g=function(){return{x:0,y:0,width:100,height:100,fill:void 0,anchor:void 0,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}};export{c as a,f as b,x as c,l as d,g as e,d as f,h as g};
