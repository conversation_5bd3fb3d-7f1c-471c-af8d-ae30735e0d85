2025-06-21T10:06:01.343+08:00 {fc14d7499cec4a180fc6d86bc6f1bc9f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 154001.100, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:06:01.343+08:00 {fc14d7499cec4a180fc6d86bc6f1bc9f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.154, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:01.516+08:00 {ec7cc65c9cec4a1811c6d86bb18e9267} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.014, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:27.621+08:00 {44189071a2ec4a1812c6d86b07ceb198} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:58.628+08:00 {7cdabaa9a9ec4a1813c6d86b840349b7} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.006, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:29.287+08:00 {9cdb90bfb0ec4a1814c6d86b30cc434f} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.329, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:30.912+08:00 {18900f2eb1ec4a1815c6d86bedc29ead} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:32.383+08:00 {c4e47b75b1ec4a1816c6d86b7c3beab5} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.272, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:37.203+08:00 {9c0bc26bb2ec4a1817c6d86b6a766b5f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 958987.700, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:07:37.203+08:00 {9c0bc26bb2ec4a1817c6d86b6a766b5f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.958, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:37.228+08:00 {608a08a6b2ec4a1819c6d86be635eacc} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.007, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:08:01.472+08:00 {3020394bb8ec4a181ac6d86be95d855b} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.005, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:08:31.483+08:00 {f8045648bfec4a181bc6d86b2cee3665} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:09:01.689+08:00 {f4f4b950c6ec4a181cc6d86b8d35ce0d} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.002, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:09:32.624+08:00 {64729b84cdec4a181dc6d86b49b62324} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
