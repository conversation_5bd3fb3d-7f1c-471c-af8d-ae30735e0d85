2025-06-21T10:06:01.343+08:00 {fc14d7499cec4a180fc6d86bc6f1bc9f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 154001.100, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:06:01.343+08:00 {fc14d7499cec4a180fc6d86bc6f1bc9f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.154, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:01.516+08:00 {ec7cc65c9cec4a1811c6d86bb18e9267} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.014, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:27.621+08:00 {44189071a2ec4a1812c6d86b07ceb198} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:06:58.628+08:00 {7cdabaa9a9ec4a1813c6d86b840349b7} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.006, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:29.287+08:00 {9cdb90bfb0ec4a1814c6d86b30cc434f} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.329, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:30.912+08:00 {18900f2eb1ec4a1815c6d86bedc29ead} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:32.383+08:00 {c4e47b75b1ec4a1816c6d86b7c3beab5} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.272, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:37.203+08:00 {9c0bc26bb2ec4a1817c6d86b6a766b5f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 958987.700, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:07:37.203+08:00 {9c0bc26bb2ec4a1817c6d86b6a766b5f} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.958, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:07:37.228+08:00 {608a08a6b2ec4a1819c6d86be635eacc} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.007, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:08:01.472+08:00 {3020394bb8ec4a181ac6d86be95d855b} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.005, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:08:31.483+08:00 {f8045648bfec4a181bc6d86b2cee3665} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:09:01.689+08:00 {f4f4b950c6ec4a181cc6d86b8d35ce0d} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.002, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:09:32.624+08:00 {64729b84cdec4a181dc6d86b49b62324} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:10:03.623+08:00 {7c5d4abcd4ec4a181ec6d86b41d3d907} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:10:34.623+08:00 {20060af4dbec4a181fc6d86b01ef25d3} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:11:05.621+08:00 {208da82be3ec4a1820c6d86b01fb3480} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:11:36.622+08:00 {d0e57463eaec4a1821c6d86bfc99c630} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:12:07.620+08:00 {dc791d9bf1ec4a1822c6d86b38038674} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:12:38.623+08:00 {4402fdd2f8ec4a1823c6d86b59b93e4f} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:13:09.623+08:00 {1494c00a00ed4a1824c6d86b3807de3e} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:13:40.621+08:00 {a4fa6c4207ed4a1825c6d86b7c9b6d33} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:14:11.620+08:00 {9020127a0eed4a1826c6d86b8385e3a5} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:14:42.620+08:00 {a846ccb115ed4a1827c6d86bc07daa01} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:15:13.620+08:00 {d88a91e91ced4a1828c6d86bc22a21fe} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:15:44.621+08:00 {7c28572124ed4a1829c6d86b7add6b45} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:16:15.622+08:00 {a01223592bed4a182ac6d86b311102c6} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:16:46.620+08:00 {5c59c89032ed4a182bc6d86bd2bdba3a} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:17:17.620+08:00 {c84b80c839ed4a182cc6d86b06fe55d2} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:17:48.620+08:00 {0cd4470041ed4a182dc6d86b497267f0} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-21T10:18:07.020+08:00 pid[31396]: all servers shutdown
