// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/system/dao/internal"
)

// internalToolsGenTableColumnDao is internal type for wrapping internal DAO implements.
type internalToolsGenTableColumnDao = *internal.ToolsGenTableColumnDao

// toolsGenTableColumnDao is the data access object for table tools_gen_table_column.
// You can define custom methods on it to extend its functionality as you wish.
type toolsGenTableColumnDao struct {
	internalToolsGenTableColumnDao
}

var (
	// ToolsGenTableColumn is globally public accessible object for table tools_gen_table_column operations.
	ToolsGenTableColumn = toolsGenTableColumnDao{
		internal.NewToolsGenTableColumnDao(),
	}
)

// Fill with you ideas below.
