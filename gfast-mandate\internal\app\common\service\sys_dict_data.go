// ================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/system"
)

type ISysDictData interface {
	GetDictWithDataByType(ctx context.Context, dictType, defaultValue string) (dict *system.GetDictRes, err error)
	List(ctx context.Context, req *system.DictDataSearchReq) (res *system.DictDataSearchRes, err error)
	Add(ctx context.Context, req *system.DictDataAddReq, userId uint64) (err error)
	Get(ctx context.Context, dictCode uint) (res *system.DictDataGetRes, err error)
	Edit(ctx context.Context, req *system.DictDataEditReq, userId uint64) (err error)
	Delete(ctx context.Context, ids []int) (err error)
}

var localSysDictData ISysDictData

func SysDictData() ISysDictData {
	if localSysDictData == nil {
		panic("implement not found for interface ISysDictData, forgot register?")
	}
	return localSysDictData
}

func RegisterSysDictData(i ISysDictData) {
	localSysDictData = i
}
