2025-06-16T19:53:19.850+08:00 [ERRO] {b04d3633bd83491850e1c941bc05cd6f} [21006 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW TABLES
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJob.(*sSysJob).GetJobs
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysJob/sys_job.go:192
2.  github.com/tiger1103/gfast/v3/task.Run
    C:/Git/COACH/gfast-mandate/task/bind_function.go:44
3.  github.com/tiger1103/gfast/v3/task.init.0.func1
    C:/Git/COACH/gfast-mandate/task/bind_function.go:22
4.  github.com/tiger1103/gfast/v3/internal/mounter.DoMount
    C:/Git/COACH/gfast-mandate/internal/mounter/mount.go:27
5.  github.com/tiger1103/gfast/v3/internal/cmd.init.func1
    C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:28
6.  main.main
    C:/Git/COACH/gfast-mandate/main.go:16

2025-06-16T19:53:40.853+08:00 [ERRO] {b04d3633bd83491850e1c941bc05cd6f} [21001 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_job`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJob.(*sSysJob).GetJobs
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysJob/sys_job.go:192
2.  github.com/tiger1103/gfast/v3/task.Run
    C:/Git/COACH/gfast-mandate/task/bind_function.go:44
3.  github.com/tiger1103/gfast/v3/task.init.0.func1
    C:/Git/COACH/gfast-mandate/task/bind_function.go:22
4.  github.com/tiger1103/gfast/v3/internal/mounter.DoMount
    C:/Git/COACH/gfast-mandate/internal/mounter/mount.go:27
5.  github.com/tiger1103/gfast/v3/internal/cmd.init.func1
    C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:28
6.  main.main
    C:/Git/COACH/gfast-mandate/main.go:16

2025-06-16T19:54:01.857+08:00 [ERRO] {b04d3633bd83491850e1c941bc05cd6f} [21003 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_job`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJob.(*sSysJob).GetJobs
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysJob/sys_job.go:192
2.  github.com/tiger1103/gfast/v3/task.Run
    C:/Git/COACH/gfast-mandate/task/bind_function.go:44
3.  github.com/tiger1103/gfast/v3/task.init.0.func1
    C:/Git/COACH/gfast-mandate/task/bind_function.go:22
4.  github.com/tiger1103/gfast/v3/internal/mounter.DoMount
    C:/Git/COACH/gfast-mandate/internal/mounter/mount.go:27
5.  github.com/tiger1103/gfast/v3/internal/cmd.init.func1
    C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:28
6.  main.main
    C:/Git/COACH/gfast-mandate/main.go:16

2025-06-16T19:54:22.863+08:00 [ERRO] {b04d3633bd83491850e1c941bc05cd6f} [21005 ms] [default] [gfast-v32mandate] [rows:0  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at`,`CreatedUser`,`UpdatedUser` FROM `sys_job` WHERE `status`=0
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysJob.(*sSysJob).GetJobs
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysJob/sys_job.go:192
2.  github.com/tiger1103/gfast/v3/task.Run
    C:/Git/COACH/gfast-mandate/task/bind_function.go:44
3.  github.com/tiger1103/gfast/v3/task.init.0.func1
    C:/Git/COACH/gfast-mandate/task/bind_function.go:22
4.  github.com/tiger1103/gfast/v3/internal/mounter.DoMount
    C:/Git/COACH/gfast-mandate/internal/mounter/mount.go:27
5.  github.com/tiger1103/gfast/v3/internal/cmd.init.func1
    C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:28
6.  main.main
    C:/Git/COACH/gfast-mandate/main.go:16

