import{_ as a,$ as o,V as l,r as d,o as c,j as h,l as m,I as g}from"./doc-30bb18f4.js";import{m as u,a as p,t as f,e as _}from"./ext-language_tools-602acc1a.js";o.config.setModuleUrl("ace/mode/json",u);o.config.setModuleUrl("ace/mode/xml",p);o.config.setModuleUrl("ace/theme/eclipse",f);o.config.setModuleUrl("ace/ext-language/tools",_);const x={name:"EditorShow",components:{editor:l},props:{value:{type:[String,Object],required:!0,default:""},xmlMode:{type:Boolean,default:!1,required:!1}},emits:["showDescription","change"],data(){return{lang:"json",editor:null,editorHeight:200}},methods:{change(e){this.$emit("change",e)},resetEditorHeight(){const e=this;setTimeout(()=>{let t=e.editor.session.getLength();t==1&&(t=10),e.editorHeight=t*16},300)},editorInit(e){const t=this;this.editor=e,this.xmlMode&&(this.lang="xml"),this.resetEditorHeight(),this.editor.renderer.on("afterRender",function(){t.$emit("showDescription","123")})}}};function E(e,t,s,I,i,n){const r=d("editor");return c(),h("div",null,[m(r,{value:s.value,onInit:n.editorInit,lang:i.lang,theme:"eclipse",width:"100%",style:g({height:i.editorHeight+"px"}),onInput:n.change},null,8,["value","onInit","lang","style","onInput"])])}const w=a(x,[["render",E]]);export{w as default};
