// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CasbinRule is the golang structure of table casbin_rule for DAO operations like Where/Data.
type CasbinRule struct {
	g.Meta `orm:"table:casbin_rule, do:true"`
	Ptype  interface{} //
	V0     interface{} //
	V1     interface{} //
	V2     interface{} //
	V3     interface{} //
	V4     interface{} //
	V5     interface{} //
}
