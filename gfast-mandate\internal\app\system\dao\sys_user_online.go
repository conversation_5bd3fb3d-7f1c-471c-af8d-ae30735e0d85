// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/system/dao/internal"
)

// internalSysUserOnlineDao is internal type for wrapping internal DAO implements.
type internalSysUserOnlineDao = *internal.SysUserOnlineDao

// sysUserOnlineDao is the data access object for table sys_user_online.
// You can define custom methods on it to extend its functionality as you wish.
type sysUserOnlineDao struct {
	internalSysUserOnlineDao
}

var (
	// SysUserOnline is globally public accessible object for table sys_user_online operations.
	SysUserOnline = sysUserOnlineDao{
		internal.NewSysUserOnlineDao(),
	}
)

// Fill with you ideas below.
