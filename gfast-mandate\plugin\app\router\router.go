// plugin/app/router/router.go
func init() {
	gfast.RouterGroup("/app", func(group *ghttp.RouterGroup) {
		group.Middleware(middleware.Auth) // JWT 鉴权

		// 动态系统
		group.Group("/feed", func(feed *ghttp.RouterGroup) {
			feed.GET("/list", controller.Feed.List)
			feed.POST("/publish", controller.Feed.Publish)
			feed.POST("/like", controller.Feed.Like)
			feed.POST("/comment", controller.Feed.Comment)
			feed.DELETE("/:id", controller.Feed.Delete)
		})

		// 聊天系统（WebSocket）
		group.Group("/chat", func(chat *ghttp.RouterGroup) {
			chat.GET("/ws", controller.Chat.WebSocket) // WS连接
			chat.GET("/history/:targetId", controller.Chat.History)
		})

		// 群管理
		group.Group("/group", func(g *ghttp.RouterGroup) {
			g.GET("/list", controller.Group.List)
			g.POST("/create", controller.Group.Create)
			g.POST("/join", controller.Group.Join)
			g.POST("/kick", controller.Group.Kick)
		})

		// 计划任务
		group.Group("/plan", func(plan *ghttp.RouterGroup) {
			plan.GET("/list", controller.Plan.List)
			plan.POST("/create", controller.Plan.Create)
			plan.PUT("/update/:id", controller.Plan.Update)
			plan.POST("/complete", controller.Plan.Complete)
		})

		// 人脉关系
		group.Group("/network", func(n *ghttp.RouterGroup) {
			n.GET("/search", controller.Network.Search)
			n.GET("/recommend", controller.Network.Recommend)
		})

		// 个人中心
		group.Group("/user", func(u *ghttp.RouterGroup) {
			u.GET("/profile", controller.User.Profile)
			u.PUT("/update", controller.User.Update)
			u.POST("/sign-in", controller.User.SignIn)
			u.GET("/points", controller.User.Points)
		})
	})
}
