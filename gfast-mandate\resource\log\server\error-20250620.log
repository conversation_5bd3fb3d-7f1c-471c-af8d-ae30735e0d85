2025-06-20T16:41:34.328+08:00 {04ae52df91b34a1854e1c94146d84d58} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 50042371.300, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", 1003, "账号密码错误", ""
Stack:
1. 账号密码错误
   1).  github.com/tiger1103/gfast/v3/library/liberr.NewCode
        C:/Git/COACH/gfast-mandate/library/liberr/err.go:36
   2).  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
        C:/Git/COACH/gfast-mandate/library/liberr/err.go:24
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
        C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:79
   4).  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
        C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
   5).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:34.330+08:00 {04ae52df91b34a1854e1c94146d84d58} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 50.042, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:41:41.634+08:00 {c46f96399fb34a1858e1c9415470ce11} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:42:12.631+08:00 {9c542c71a6b34a1859e1c941e4061048} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:42:43.632+08:00 {d077f5a8adb34a185ae1c941162ba0d1} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:43:14.642+08:00 {0cb41fe1b4b34a185be1c941f9bd68af} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.006, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:43:45.632+08:00 {4c737318bcb34a185ce1c9418bc59cb7} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:44:16.634+08:00 {30034d50c3b34a185de1c94122d57e24} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:44:47.631+08:00 {c040eb87cab34a185ee1c941d995bc7e} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:45:18.634+08:00 {14f5c6bfd1b34a185fe1c941a37e2b15} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:45:49.632+08:00 {3c4164f7d8b34a1860e1c941616bd1d8} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:46:20.631+08:00 {2400242fe0b34a1861e1c9418cfdb894} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:46:51.633+08:00 {54def966e7b34a1862e1c9416781ecaa} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.002, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:47:22.631+08:00 {88f0979eeeb34a1863e1c941a7ba40d4} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:47:53.632+08:00 {20065bd6f5b34a1864e1c941d7719986} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:48:24.632+08:00 {ac2d2c0efdb34a1865e1c941750c0dbe} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:48:55.632+08:00 {7804e64504b44a1866e1c941750bce61} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:49:26.633+08:00 {94d4b07d0bb44a1867e1c9414bd6844a} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:49:57.633+08:00 {a8c364b512b44a1868e1c941c8f4c741} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:50:28.633+08:00 {9cd227ed19b44a1869e1c9414477dd50} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:50:59.632+08:00 {28cede2421b44a186ae1c94158ace1bc} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:51:30.632+08:00 {0464a15c28b44a186be1c9414b0593fd} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:52:01.633+08:00 {001768942fb44a186ce1c941a46878ac} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:52:32.631+08:00 {043a11cc36b44a186de1c941b8d47ea1} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:53:03.633+08:00 {18f7dd033eb44a186ee1c94105c87392} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:53:34.634+08:00 {704daa3b45b44a186fe1c941f0be77ed} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.002, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:54:05.636+08:00 {dced90734cb44a1870e1c9418b53ed3e} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:54:36.632+08:00 {14ea13ab53b44a1871e1c9416b89e2e1} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:55:07.633+08:00 {0c48d9e25ab44a1872e1c9413b04ed8a} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:55:38.632+08:00 {b886961a62b44a1873e1c9412bd9c3f6} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:56:09.631+08:00 {b8a8495269b44a1874e1c94194bc1b89} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:56:40.632+08:00 {88cf098a70b44a1875e1c941c06afbb0} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:57:11.632+08:00 {d801c6c177b44a1876e1c941fee31384} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:57:42.634+08:00 {7c6ca0f97eb44a1877e1c941ce8457dd} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:58:13.631+08:00 {50f13a3186b44a1878e1c9417424d9a9} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:58:44.632+08:00 {ec93fe688db44a1879e1c941aa1ce0bf} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:59:15.631+08:00 {f41bb7a094b44a187ae1c941e73ae909} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T16:59:46.631+08:00 {84957dd89bb44a187be1c94152c50504} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:00:17.634+08:00 {08a65910a3b44a187ce1c94184608b7a} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:00:48.633+08:00 {64bb0d48aab44a187de1c9412dc9ca29} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:01:19.631+08:00 {c8c2b87fb1b44a187ee1c941d7f373f0} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:01:52.203+08:00 {e0971a15b9b44a187fe1c941c9f0ce2f} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:02:18.936+08:00 {5867b1d7bdb44a1880e1c94108c607ae} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 6289404.500, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", 1003, "账号密码错误", ""
Stack:
1. 账号密码错误
   1).  github.com/tiger1103/gfast/v3/library/liberr.NewCode
        C:/Git/COACH/gfast-mandate/library/liberr/err.go:36
   2).  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
        C:/Git/COACH/gfast-mandate/library/liberr/err.go:24
   3).  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
        C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:79
   4).  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
        C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
   5).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
   6).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:02:18.936+08:00 {5867b1d7bdb44a1880e1c94108c607ae} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 6.289, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:02:19.888+08:00 {30b74687bfb44a1882e1c9419d1ec9b9} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.001, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:02:21.164+08:00 {207d19d1bfb44a1883e1c941af8d4f2c} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.038, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:02:32.584+08:00 pid[7712]: all servers shutdown
2025-06-20T17:03:46.156+08:00 {34bf369ad3b44a18611637784e546989} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 52516.100, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:03:46.158+08:00 {34bf369ad3b44a18611637784e546989} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.052, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:03:46.178+08:00 {2840409ed3b44a18631637789a256d4e} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.006, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:03:54.407+08:00 {7898ab88d5b44a186416377892d73d82} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 6749.600, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:03:54.407+08:00 {7898ab88d5b44a186416377892d73d82} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.006, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:03:54.426+08:00 {b00cb889d5b44a1866163778edc3b3c5} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.008, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:13.852+08:00 {34a41610dab44a18671637785f4da533} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:16.758+08:00 {608551bddab44a1868163778cf6ac2ad} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:17.612+08:00 {1cb91befdab44a18691637781b254f93} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.019, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:19.663+08:00 {0c013467dbb44a186a1637784ce3de46} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 54848.400, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:04:19.664+08:00 {0c013467dbb44a186a1637784ce3de46} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.054, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:19.685+08:00 {3c456b6bdbb44a186c163778de322f58} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.006, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:04:29.088+08:00 pid[32876]: all servers shutdown
2025-06-20T17:05:18.012+08:00 {88848ffee8b44a188aa1550e30b40d5b} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 29725.900, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:18.013+08:00 {88848ffee8b44a188aa1550e30b40d5b} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.029, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:18.035+08:00 {648a3201e9b44a188ca1550e09c90c70} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.007, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:38.151+08:00 {146233b0edb44a188da1550e5c3d0bee} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 8455.200, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:38.151+08:00 {146233b0edb44a188da1550e5c3d0bee} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.008, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:38.173+08:00 {98277ab1edb44a188fa1550eb298c6cb} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.009, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:39.858+08:00 {8089d213eeb44a1890a1550e4f6b8a87} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 44635.300, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", -1, "", ""
Stack:
1. 登录失败，后端服务出现错误
   1).  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
        C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:99
   2).  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
        C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:39.858+08:00 {8089d213eeb44a1890a1550e4f6b8a87} 200 "POST http localhost:8808 /api/v1/system/login HTTP/1.1" 0.044, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:39.880+08:00 {30fd6917eeb44a1892a1550e2aff2afd} 200 "GET http localhost:8808 /api/v1/pub/captcha/get HTTP/1.1" 0.006, ::1, "http://localhost:8889/", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:05:47.252+08:00 {9ccc29cfefb44a1893a1550e3a2c07ba} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:06:17.631+08:00 {c49ee8e1f6b44a1894a1550eba7431e7} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:06:48.632+08:00 {c0a8ad19feb44a1895a1550e76279e62} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:07:19.632+08:00 {b4cd645105b54a1896a1550e949824a9} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:07:50.632+08:00 {b0e42b890cb54a1897a1550e67bcba6e} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:08:21.632+08:00 {2c7ce8c013b54a1898a1550e9965a04f} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:08:52.634+08:00 {0ce9c0f81ab54a1899a1550e9725e403} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:09:23.632+08:00 {60a3693022b54a189aa1550e783f7430} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:09:54.631+08:00 {dc850d6829b54a189ba1550e0633a840} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:10:25.632+08:00 {84bbe69f30b54a189ca1550ef6022fd0} 200 "GET http localhost:8808 //api/v1/websocket?token=undefined HTTP/1.1" 0.000, ::1, "", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
2025-06-20T17:10:28.106+08:00 pid[6156]: all servers shutdown
