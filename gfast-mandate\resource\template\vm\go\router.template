// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：{{date "Y-m-d H:i:s"}}
// 生成路径: {{.table.PackageName}}/router/{{.table.TableName}}.go
// 生成人：{{.table.FunctionAuthor}}
// desc:{{.table.FunctionName}}
// company:云南奇讯科技有限公司
// ==========================================================================
////
package router
////
import (
	"context"
	"github.com/gogf/gf/v2/net/ghttp"
	"{{.goModName}}/internal/app/system/service"
	"{{.goModName}}/library/libRouter"
)
////

var R = new(Router)
////
type Router struct{}
////
func (router *Router) BindController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/{{.modulePath}}", func(group *ghttp.RouterGroup) {
		//context拦截器
		group.Middleware(service.Middleware().Ctx)
		//自动绑定定义的控制器
		if err := libRouter.RouterAutoBindBefore(ctx, router, group); err != nil {
			panic(err)
		}
		//登录验证拦截
		service.GfToken().Middleware(group)
		group.Middleware(service.Middleware().Auth)
		//后台操作日志记录
		group.Hook("/*", ghttp.HookAfterOutput, service.OperateLog().OperationLog)
		//自动绑定定义的控制器
		if err := libRouter.RouterAutoBind(ctx, router, group); err != nil {
			panic(err)
		}
	})
}