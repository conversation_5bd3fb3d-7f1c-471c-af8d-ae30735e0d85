var J={exports:{}};(function(z,Y){ace.define("ace/mode/jsdoc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(f,S,F){var E=f("../lib/oop"),w=f("./text_highlight_rules").TextHighlightRules,_=function(){this.$rules={start:[{token:["comment.doc.tag","text","lparen.doc"],regex:"(@(?:param|member|typedef|property|namespace|var|const|callback))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:["rparen.doc","text","variable.parameter.doc","lparen.doc","variable.parameter.doc","rparen.doc"],regex:/(})(\s*)(?:([\w=:\/\.]+)|(?:(\[)([\w=:\/\.]+)(\])))/,next:"pop"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text"}]},{token:["comment.doc.tag","text","lparen.doc"],regex:"(@(?:returns?|yields|type|this|suppress|public|protected|private|package|modifies|implements|external|exception|throws|enum|define|extends))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text"}]},{token:["comment.doc.tag","text","variable.parameter.doc"],regex:'(@(?:alias|memberof|instance|module|name|lends|namespace|external|this|template|requires|param|implements|function|extends|typedef|mixes|constructor|var|memberof\\!|event|listens|exports|class|constructs|interface|emits|fires|throws|const|callback|borrows|augments))(\\s+)(\\w[\\w#.:/~"\\-]*)?'},{token:["comment.doc.tag","text","variable.parameter.doc"],regex:"(@method)(\\s+)(\\w[\\w.\\(\\)]*)"},{token:"comment.doc.tag",regex:"@access\\s+(?:private|public|protected)"},{token:"comment.doc.tag",regex:"@kind\\s+(?:class|constant|event|external|file|function|member|mixin|module|namespace|typedef)"},{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},_.getTagRule(),{defaultToken:"comment.doc",caseInsensitive:!0}],"doc-syntax":[{token:"operator.doc",regex:/[|:]/},{token:"paren.doc",regex:/[\[\]]/}]},this.normalizeRules()};E.inherits(_,w),_.getTagRule=function(v){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},_.getStartRule=function(v){return{token:"comment.doc",regex:"\\/\\*(?=\\*)",next:v}},_.getEndRule=function(v){return{token:"comment.doc",regex:"\\*\\/",next:v}},S.JsDocCommentHighlightRules=_}),ace.define("ace/mode/javascript_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/jsdoc_comment_highlight_rules","ace/mode/text_highlight_rules"],function(f,S,F){var E=f("../lib/oop"),w=f("./jsdoc_comment_highlight_rules").JsDocCommentHighlightRules,_=f("./text_highlight_rules").TextHighlightRules,v="[a-zA-Z\\$_¡-￿][a-zA-Z\\d\\$_¡-￿]*",g=function(M){var C=this.createKeywordMapper({"variable.language":"Array|Boolean|Date|Function|Iterator|Number|Object|RegExp|String|Proxy|Symbol|Namespace|QName|XML|XMLList|ArrayBuffer|Float32Array|Float64Array|Int16Array|Int32Array|Int8Array|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray|Error|EvalError|InternalError|RangeError|ReferenceError|StopIteration|SyntaxError|TypeError|URIError|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|eval|isFinite|isNaN|parseFloat|parseInt|JSON|Math|this|arguments|prototype|window|document",keyword:"const|yield|import|get|set|async|await|break|case|catch|continue|default|delete|do|else|finally|for|function|if|in|of|instanceof|new|return|switch|throw|try|typeof|let|var|while|with|debugger|__parent__|__count__|escape|unescape|with|__proto__|class|enum|extends|super|export|implements|private|public|interface|package|protected|static|constructor","storage.type":"const|let|var|function","constant.language":"null|Infinity|NaN|undefined","support.function":"alert","constant.language.boolean":"true|false"},"identifier"),A="case|do|else|finally|in|instanceof|return|throw|try|typeof|yield|void",m="\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|u{[0-9a-fA-F]{1,6}}|[0-2][0-7]{0,2}|3[0-7][0-7]?|[4-7][0-7]?|.)";this.$rules={no_regex:[w.getStartRule("doc-start"),b("no_regex"),{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:["storage.type","punctuation.operator","support.function","punctuation.operator","entity.name.function","text","keyword.operator"],regex:"("+v+")(\\.)(prototype)(\\.)("+v+")(\\s*)(=)",next:"function_arguments"},{token:["storage.type","punctuation.operator","entity.name.function","text","keyword.operator","text","storage.type","text","paren.lparen"],regex:"("+v+")(\\.)("+v+")(\\s*)(=)(\\s*)(function\\*?)(\\s*)(\\()",next:"function_arguments"},{token:["entity.name.function","text","keyword.operator","text","storage.type","text","paren.lparen"],regex:"("+v+")(\\s*)(=)(\\s*)(function\\*?)(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","punctuation.operator","entity.name.function","text","keyword.operator","text","storage.type","text","entity.name.function","text","paren.lparen"],regex:"("+v+")(\\.)("+v+")(\\s*)(=)(\\s*)(function\\*?)(\\s+)(\\w+)(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","entity.name.function","text","paren.lparen"],regex:"(function\\*?)(\\s+)("+v+")(\\s*)(\\()",next:"function_arguments"},{token:["entity.name.function","text","punctuation.operator","text","storage.type","text","paren.lparen"],regex:"("+v+")(\\s*)(:)(\\s*)(function\\*?)(\\s*)(\\()",next:"function_arguments"},{token:["text","text","storage.type","text","paren.lparen"],regex:"(:)(\\s*)(function\\*?)(\\s*)(\\()",next:"function_arguments"},{token:"keyword",regex:`from(?=\\s*('|"))`},{token:"keyword",regex:"(?:"+A+")\\b",next:"start"},{token:"support.constant",regex:/that\b/},{token:["storage.type","punctuation.operator","support.function.firebug"],regex:/(console)(\.)(warn|info|log|error|time|trace|timeEnd|assert)\b/},{token:C,regex:v},{token:"punctuation.operator",regex:/[.](?![.])/,next:"property"},{token:"storage.type",regex:/=>/,next:"start"},{token:"keyword.operator",regex:/--|\+\+|\.{3}|===|==|=|!=|!==|<+=?|>+=?|!|&&|\|\||\?:|[!$%&*+\-~\/^]=?/,next:"start"},{token:"punctuation.operator",regex:/[?:,;.]/,next:"start"},{token:"paren.lparen",regex:/[\[({]/,next:"start"},{token:"paren.rparen",regex:/[\])}]/},{token:"comment",regex:/^#!.*$/}],property:[{token:"text",regex:"\\s+"},{token:["storage.type","punctuation.operator","entity.name.function","text","keyword.operator","text","storage.type","text","entity.name.function","text","paren.lparen"],regex:"("+v+")(\\.)("+v+")(\\s*)(=)(\\s*)(function\\*?)(?:(\\s+)(\\w+))?(\\s*)(\\()",next:"function_arguments"},{token:"punctuation.operator",regex:/[.](?![.])/},{token:"support.function",regex:/(s(?:h(?:ift|ow(?:Mod(?:elessDialog|alDialog)|Help))|croll(?:X|By(?:Pages|Lines)?|Y|To)?|t(?:op|rike)|i(?:n|zeToContent|debar|gnText)|ort|u(?:p|b(?:str(?:ing)?)?)|pli(?:ce|t)|e(?:nd|t(?:Re(?:sizable|questHeader)|M(?:i(?:nutes|lliseconds)|onth)|Seconds|Ho(?:tKeys|urs)|Year|Cursor|Time(?:out)?|Interval|ZOptions|Date|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Date|FullYear)|FullYear|Active)|arch)|qrt|lice|avePreferences|mall)|h(?:ome|andleEvent)|navigate|c(?:har(?:CodeAt|At)|o(?:s|n(?:cat|textual|firm)|mpile)|eil|lear(?:Timeout|Interval)?|a(?:ptureEvents|ll)|reate(?:StyleSheet|Popup|EventObject))|t(?:o(?:GMTString|S(?:tring|ource)|U(?:TCString|pperCase)|Lo(?:caleString|werCase))|est|a(?:n|int(?:Enabled)?))|i(?:s(?:NaN|Finite)|ndexOf|talics)|d(?:isableExternalCapture|ump|etachEvent)|u(?:n(?:shift|taint|escape|watch)|pdateCommands)|j(?:oin|avaEnabled)|p(?:o(?:p|w)|ush|lugins.refresh|a(?:ddings|rse(?:Int|Float)?)|r(?:int|ompt|eference))|e(?:scape|nableExternalCapture|val|lementFromPoint|x(?:p|ec(?:Script|Command)?))|valueOf|UTC|queryCommand(?:State|Indeterm|Enabled|Value)|f(?:i(?:nd|lter|le(?:ModifiedDate|Size|CreatedDate|UpdatedDate)|xed)|o(?:nt(?:size|color)|rward|rEach)|loor|romCharCode)|watch|l(?:ink|o(?:ad|g)|astIndexOf)|a(?:sin|nchor|cos|t(?:tachEvent|ob|an(?:2)?)|pply|lert|b(?:s|ort))|r(?:ou(?:nd|teEvents)|e(?:size(?:By|To)|calc|turnValue|place|verse|l(?:oad|ease(?:Capture|Events)))|andom)|g(?:o|et(?:ResponseHeader|M(?:i(?:nutes|lliseconds)|onth)|Se(?:conds|lection)|Hours|Year|Time(?:zoneOffset)?|Da(?:y|te)|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Da(?:y|te)|FullYear)|FullYear|A(?:ttention|llResponseHeaders)))|m(?:in|ove(?:B(?:y|elow)|To(?:Absolute)?|Above)|ergeAttributes|a(?:tch|rgins|x))|b(?:toa|ig|o(?:ld|rderWidths)|link|ack))\b(?=\()/},{token:"support.function.dom",regex:/(s(?:ub(?:stringData|mit)|plitText|e(?:t(?:NamedItem|Attribute(?:Node)?)|lect))|has(?:ChildNodes|Feature)|namedItem|c(?:l(?:ick|o(?:se|neNode))|reate(?:C(?:omment|DATASection|aption)|T(?:Head|extNode|Foot)|DocumentFragment|ProcessingInstruction|E(?:ntityReference|lement)|Attribute))|tabIndex|i(?:nsert(?:Row|Before|Cell|Data)|tem)|open|delete(?:Row|C(?:ell|aption)|T(?:Head|Foot)|Data)|focus|write(?:ln)?|a(?:dd|ppend(?:Child|Data))|re(?:set|place(?:Child|Data)|move(?:NamedItem|Child|Attribute(?:Node)?)?)|get(?:NamedItem|Element(?:sBy(?:Name|TagName|ClassName)|ById)|Attribute(?:Node)?)|blur)\b(?=\()/},{token:"support.constant",regex:/(s(?:ystemLanguage|cr(?:ipts|ollbars|een(?:X|Y|Top|Left))|t(?:yle(?:Sheets)?|atus(?:Text|bar)?)|ibling(?:Below|Above)|ource|uffixes|e(?:curity(?:Policy)?|l(?:ection|f)))|h(?:istory|ost(?:name)?|as(?:h|Focus))|y|X(?:MLDocument|SLDocument)|n(?:ext|ame(?:space(?:s|URI)|Prop))|M(?:IN_VALUE|AX_VALUE)|c(?:haracterSet|o(?:n(?:structor|trollers)|okieEnabled|lorDepth|mp(?:onents|lete))|urrent|puClass|l(?:i(?:p(?:boardData)?|entInformation)|osed|asses)|alle(?:e|r)|rypto)|t(?:o(?:olbar|p)|ext(?:Transform|Indent|Decoration|Align)|ags)|SQRT(?:1_2|2)|i(?:n(?:ner(?:Height|Width)|put)|ds|gnoreCase)|zIndex|o(?:scpu|n(?:readystatechange|Line)|uter(?:Height|Width)|p(?:sProfile|ener)|ffscreenBuffering)|NEGATIVE_INFINITY|d(?:i(?:splay|alog(?:Height|Top|Width|Left|Arguments)|rectories)|e(?:scription|fault(?:Status|Ch(?:ecked|arset)|View)))|u(?:ser(?:Profile|Language|Agent)|n(?:iqueID|defined)|pdateInterval)|_content|p(?:ixelDepth|ort|ersonalbar|kcs11|l(?:ugins|atform)|a(?:thname|dding(?:Right|Bottom|Top|Left)|rent(?:Window|Layer)?|ge(?:X(?:Offset)?|Y(?:Offset)?))|r(?:o(?:to(?:col|type)|duct(?:Sub)?|mpter)|e(?:vious|fix)))|e(?:n(?:coding|abledPlugin)|x(?:ternal|pando)|mbeds)|v(?:isibility|endor(?:Sub)?|Linkcolor)|URLUnencoded|P(?:I|OSITIVE_INFINITY)|f(?:ilename|o(?:nt(?:Size|Family|Weight)|rmName)|rame(?:s|Element)|gColor)|E|whiteSpace|l(?:i(?:stStyleType|n(?:eHeight|kColor))|o(?:ca(?:tion(?:bar)?|lName)|wsrc)|e(?:ngth|ft(?:Context)?)|a(?:st(?:M(?:odified|atch)|Index|Paren)|yer(?:s|X)|nguage))|a(?:pp(?:MinorVersion|Name|Co(?:deName|re)|Version)|vail(?:Height|Top|Width|Left)|ll|r(?:ity|guments)|Linkcolor|bove)|r(?:ight(?:Context)?|e(?:sponse(?:XML|Text)|adyState))|global|x|m(?:imeTypes|ultiline|enubar|argin(?:Right|Bottom|Top|Left))|L(?:N(?:10|2)|OG(?:10E|2E))|b(?:o(?:ttom|rder(?:Width|RightWidth|BottomWidth|Style|Color|TopWidth|LeftWidth))|ufferDepth|elow|ackground(?:Color|Image)))\b/},{token:"identifier",regex:v},{regex:"",token:"empty",next:"no_regex"}],start:[w.getStartRule("doc-start"),b("start"),{token:"string.regexp",regex:"\\/",next:"regex"},{token:"text",regex:"\\s+|^$",next:"start"},{token:"empty",regex:"",next:"no_regex"}],regex:[{token:"regexp.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"string.regexp",regex:"/[sxngimy]*",next:"no_regex"},{token:"invalid",regex:/\{\d+\b,?\d*\}[+*]|[+*$^?][+*]|[$^][?]|\?{3,}/},{token:"constant.language.escape",regex:/\(\?[:=!]|\)|\{\d+\b,?\d*\}|[+*]\?|[()$^+*?.]/},{token:"constant.language.delimiter",regex:/\|/},{token:"constant.language.escape",regex:/\[\^?/,next:"regex_character_class"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp"}],regex_character_class:[{token:"regexp.charclass.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"constant.language.escape",regex:"]",next:"regex"},{token:"constant.language.escape",regex:"-"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp.charachterclass"}],default_parameter:[{token:"string",regex:"'(?=.)",push:[{token:"string",regex:"'|$",next:"pop"},{include:"qstring"}]},{token:"string",regex:'"(?=.)',push:[{token:"string",regex:'"|$',next:"pop"},{include:"qqstring"}]},{token:"constant.language",regex:"null|Infinity|NaN|undefined"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:"punctuation.operator",regex:",",next:"function_arguments"},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],function_arguments:[b("function_arguments"),{token:"variable.parameter",regex:v},{token:"punctuation.operator",regex:","},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],qqstring:[{token:"constant.language.escape",regex:m},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:'"|$',next:"no_regex"},{defaultToken:"string"}],qstring:[{token:"constant.language.escape",regex:m},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:"'|$",next:"no_regex"},{defaultToken:"string"}]},(!M||!M.noES6)&&(this.$rules.no_regex.unshift({regex:"[{}]",onMatch:function(y,k,n){if(this.next=y=="{"?this.nextState:"",y=="{"&&n.length)n.unshift("start",k);else if(y=="}"&&n.length&&(n.shift(),this.next=n.shift(),this.next.indexOf("string")!=-1||this.next.indexOf("jsx")!=-1))return"paren.quasi.end";return y=="{"?"paren.lparen":"paren.rparen"},nextState:"start"},{token:"string.quasi.start",regex:/`/,push:[{token:"constant.language.escape",regex:m},{token:"paren.quasi.start",regex:/\${/,push:"start"},{token:"string.quasi.end",regex:/`/,next:"pop"},{defaultToken:"string.quasi"}]},{token:["variable.parameter","text"],regex:"("+v+")(\\s*)(?=\\=>)"},{token:"paren.lparen",regex:"(\\()(?=.+\\s*=>)",next:"function_arguments"},{token:"variable.language",regex:"(?:(?:(?:Weak)?(?:Set|Map))|Promise)\\b"}),this.$rules.function_arguments.unshift({token:"keyword.operator",regex:"=",next:"default_parameter"},{token:"keyword.operator",regex:"\\.{3}"}),this.$rules.property.unshift({token:"support.function",regex:"(findIndex|repeat|startsWith|endsWith|includes|isSafeInteger|trunc|cbrt|log2|log10|sign|then|catch|finally|resolve|reject|race|any|all|allSettled|keys|entries|isInteger)\\b(?=\\()"},{token:"constant.language",regex:"(?:MAX_SAFE_INTEGER|MIN_SAFE_INTEGER|EPSILON)\\b"}),(!M||M.jsx!=!1)&&x.call(this)),this.embedRules(w,"doc-",[w.getEndRule("no_regex")]),this.normalizeRules()};E.inherits(g,_);function x(){var M=v.replace("\\d","\\d\\-"),C={onMatch:function(m,y,k){var n=m.charAt(1)=="/"?2:1;return n==1?(y!=this.nextState?k.unshift(this.next,this.nextState,0):k.unshift(this.next),k[2]++):n==2&&y==this.nextState&&(k[1]--,(!k[1]||k[1]<0)&&(k.shift(),k.shift())),[{type:"meta.tag.punctuation."+(n==1?"":"end-")+"tag-open.xml",value:m.slice(0,n)},{type:"meta.tag.tag-name.xml",value:m.substr(n)}]},regex:"</?"+M,next:"jsxAttributes",nextState:"jsx"};this.$rules.start.unshift(C);var A={regex:"{",token:"paren.quasi.start",push:"start"};this.$rules.jsx=[A,C,{include:"reference"},{defaultToken:"string"}],this.$rules.jsxAttributes=[{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",onMatch:function(m,y,k){return y==k[0]&&k.shift(),m.length==2&&(k[0]==this.nextState&&k[1]--,(!k[1]||k[1]<0)&&k.splice(0,2)),this.next=k[0]||"start",[{type:this.token,value:m}]},nextState:"jsx"},A,b("jsxAttributes"),{token:"entity.other.attribute-name.xml",regex:M},{token:"keyword.operator.attribute-equals.xml",regex:"="},{token:"text.tag-whitespace.xml",regex:"\\s+"},{token:"string.attribute-value.xml",regex:"'",stateName:"jsx_attr_q",push:[{token:"string.attribute-value.xml",regex:"'",next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},{token:"string.attribute-value.xml",regex:'"',stateName:"jsx_attr_qq",push:[{token:"string.attribute-value.xml",regex:'"',next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},C],this.$rules.reference=[{token:"constant.language.escape.reference.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}]}function b(M){return[{token:"comment",regex:/\/\*/,next:[w.getTagRule(),{token:"comment",regex:"\\*\\/",next:M||"pop"},{defaultToken:"comment",caseInsensitive:!0}]},{token:"comment",regex:"\\/\\/",next:[w.getTagRule(),{token:"comment",regex:"$|^",next:M||"pop"},{defaultToken:"comment",caseInsensitive:!0}]}]}S.JavaScriptHighlightRules=g}),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],function(f,S,F){var E=f("../range").Range,w=function(){};(function(){this.checkOutdent=function(_,v){return/^\s+$/.test(_)?/^\s*\}/.test(v):!1},this.autoOutdent=function(_,v){var g=_.getLine(v),x=g.match(/^(\s*\})/);if(!x)return 0;var b=x[1].length,M=_.findMatchingBracket({row:v,column:b});if(!M||M.row==v)return 0;var C=this.$getIndent(_.getLine(M.row));_.replace(new E(v,0,v,b-1),C)},this.$getIndent=function(_){return _.match(/^\s*/)[0]}}).call(w.prototype),S.MatchingBraceOutdent=w}),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],function(f,S,F){var E=f("../../lib/oop"),w=f("../../range").Range,_=f("./fold_mode").FoldMode,v=S.FoldMode=function(g){g&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+g.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+g.end)))};E.inherits(v,_),(function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(g,x,b){var M=g.getLine(b);if(this.singleLineBlockCommentRe.test(M)&&!this.startRegionRe.test(M)&&!this.tripleStarBlockCommentRe.test(M))return"";var C=this._getFoldWidgetBase(g,x,b);return!C&&this.startRegionRe.test(M)?"start":C},this.getFoldWidgetRange=function(g,x,b,M){var C=g.getLine(b);if(this.startRegionRe.test(C))return this.getCommentRegionBlock(g,C,b);var y=C.match(this.foldingStartMarker);if(y){var A=y.index;if(y[1])return this.openingBracketBlock(g,y[1],b,A);var m=g.getCommentFoldRange(b,A+y[0].length,1);return m&&!m.isMultiLine()&&(M?m=this.getSectionRange(g,b):x!="all"&&(m=null)),m}if(x!=="markbegin"){var y=C.match(this.foldingStopMarker);if(y){var A=y.index+y[0].length;return y[1]?this.closingBracketBlock(g,y[1],b,A):g.getCommentFoldRange(b,A,-1)}}},this.getSectionRange=function(g,x){var b=g.getLine(x),M=b.search(/\S/),C=x,A=b.length;x=x+1;for(var m=x,y=g.getLength();++x<y;){b=g.getLine(x);var k=b.search(/\S/);if(k!==-1){if(M>k)break;var n=this.getFoldWidgetRange(g,"all",x);if(n){if(n.start.row<=C)break;if(n.isMultiLine())x=n.end.row;else if(M==k)break}m=x}}return new w(C,A,m,g.getLine(m).length)},this.getCommentRegionBlock=function(g,x,b){for(var M=x.search(/\s*$/),C=g.getLength(),A=b,m=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,y=1;++b<C;){x=g.getLine(b);var k=m.exec(x);if(k&&(k[1]?y--:y++,!y))break}var n=b;if(n>A)return new w(A,M,n,x.length)}}).call(v.prototype)}),ace.define("ace/mode/javascript",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/javascript_highlight_rules","ace/mode/matching_brace_outdent","ace/worker/worker_client","ace/mode/behaviour/cstyle","ace/mode/folding/cstyle"],function(f,S,F){var E=f("../lib/oop"),w=f("./text").Mode,_=f("./javascript_highlight_rules").JavaScriptHighlightRules,v=f("./matching_brace_outdent").MatchingBraceOutdent,g=f("../worker/worker_client").WorkerClient,x=f("./behaviour/cstyle").CstyleBehaviour,b=f("./folding/cstyle").FoldMode,M=function(){this.HighlightRules=_,this.$outdent=new v,this.$behaviour=new x,this.foldingRules=new b};E.inherits(M,w),(function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.$quotes={'"':'"',"'":"'","`":"`"},this.$pairQuotesAfter={"`":/\w/},this.getNextLineIndent=function(C,A,m){var y=this.$getIndent(A),k=this.getTokenizer().getLineTokens(A,C),n=k.tokens,l=k.state;if(n.length&&n[n.length-1].type=="comment")return y;if(C=="start"||C=="no_regex"){var u=A.match(/^.*(?:\bcase\b.*:|[\{\(\[])\s*$/);u&&(y+=m)}else if(C=="doc-start"){if(l=="start"||l=="no_regex")return"";var u=A.match(/^\s*(\/?)\*/);u&&(u[1]&&(y+=" "),y+="* ")}return y},this.checkOutdent=function(C,A,m){return this.$outdent.checkOutdent(A,m)},this.autoOutdent=function(C,A,m){this.$outdent.autoOutdent(A,m)},this.createWorker=function(C){var A=new g(["ace"],"ace/mode/javascript_worker","JavaScriptWorker");return A.attachToDocument(C.getDocument()),A.on("annotate",function(m){C.setAnnotations(m.data)}),A.on("terminate",function(){C.clearAnnotations()}),A},this.$id="ace/mode/javascript",this.snippetFileId="ace/snippets/javascript"}).call(M.prototype),S.Mode=M}),function(){ace.require(["ace/mode/javascript"],function(f){z&&(z.exports=f)})}()})(J);var Z={exports:{}};(function(z,Y){ace.define("ace/theme/eclipse.css",["require","exports","module"],function(f,S,F){F.exports=`.ace-eclipse .ace_gutter {
  background: #ebebeb;
  border-right: 1px solid rgb(159, 159, 159);
  color: rgb(136, 136, 136);
}

.ace-eclipse .ace_print-margin {
  width: 1px;
  background: #ebebeb;
}

.ace-eclipse {
  background-color: #FFFFFF;
  color: black;
}

.ace-eclipse .ace_fold {
    background-color: rgb(60, 76, 114);
}

.ace-eclipse .ace_cursor {
  color: black;
}

.ace-eclipse .ace_storage,
.ace-eclipse .ace_keyword,
.ace-eclipse .ace_variable {
  color: rgb(127, 0, 85);
}

.ace-eclipse .ace_constant.ace_buildin {
  color: rgb(88, 72, 246);
}

.ace-eclipse .ace_constant.ace_library {
  color: rgb(6, 150, 14);
}

.ace-eclipse .ace_function {
  color: rgb(60, 76, 114);
}

.ace-eclipse .ace_string {
  color: rgb(42, 0, 255);
}

.ace-eclipse .ace_comment {
  color: rgb(113, 150, 130);
}

.ace-eclipse .ace_comment.ace_doc {
  color: rgb(63, 95, 191);
}

.ace-eclipse .ace_comment.ace_doc.ace_tag {
  color: rgb(127, 159, 191);
}

.ace-eclipse .ace_constant.ace_numeric {
  color: darkblue;
}

.ace-eclipse .ace_tag {
  color: rgb(25, 118, 116);
}

.ace-eclipse .ace_type {
  color: rgb(127, 0, 127);
}

.ace-eclipse .ace_xml-pe {
  color: rgb(104, 104, 91);
}

.ace-eclipse .ace_marker-layer .ace_selection {
  background: rgb(181, 213, 255);
}

.ace-eclipse .ace_marker-layer .ace_bracket {
  margin: -1px 0 0 -1px;
  border: 1px solid rgb(192, 192, 192);
}

.ace-eclipse .ace_meta.ace_tag {
  color:rgb(25, 118, 116);
}

.ace-eclipse .ace_invisible {
  color: #ddd;
}

.ace-eclipse .ace_entity.ace_other.ace_attribute-name {
  color:rgb(127, 0, 127);
}
.ace-eclipse .ace_marker-layer .ace_step {
  background: rgb(255, 255, 0);
}

.ace-eclipse .ace_active-line {
  background: rgb(232, 242, 254);
}

.ace-eclipse .ace_gutter-active-line {
  background-color : #DADADA;
}

.ace-eclipse .ace_marker-layer .ace_selected-word {
  border: 1px solid rgb(181, 213, 255);
}

.ace-eclipse .ace_indent-guide {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;
}

.ace-eclipse .ace_indent-guide-active {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAZSURBVHjaYvj///9/hivKyv8BAAAA//8DACLqBhbvk+/eAAAAAElFTkSuQmCC") right repeat-y;
} 
`}),ace.define("ace/theme/eclipse",["require","exports","module","ace/theme/eclipse.css","ace/lib/dom"],function(f,S,F){S.isDark=!1,S.cssText=f("./eclipse.css"),S.cssClass="ace-eclipse";var E=f("../lib/dom");E.importCssString(S.cssText,S.cssClass,!1)}),function(){ace.require(["ace/theme/eclipse"],function(f){z&&(z.exports=f)})}()})(Z);var Q={exports:{}};(function(z,Y){ace.define("ace/snippets",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter","ace/lib/lang","ace/range","ace/range_list","ace/keyboard/hash_handler","ace/tokenizer","ace/clipboard","ace/editor"],function(f,S,F){var E=f("./lib/dom"),w=f("./lib/oop"),_=f("./lib/event_emitter").EventEmitter,v=f("./lib/lang"),g=f("./range").Range,x=f("./range_list").RangeList,b=f("./keyboard/hash_handler").HashHandler,M=f("./tokenizer").Tokenizer,C=f("./clipboard"),A={CURRENT_WORD:function(t){return t.session.getTextRange(t.session.getWordRange())},SELECTION:function(t,e,i){var c=t.session.getTextRange();return i?c.replace(/\n\r?([ \t]*\S)/g,`
`+i+"$1"):c},CURRENT_LINE:function(t){return t.session.getLine(t.getCursorPosition().row)},PREV_LINE:function(t){return t.session.getLine(t.getCursorPosition().row-1)},LINE_INDEX:function(t){return t.getCursorPosition().row},LINE_NUMBER:function(t){return t.getCursorPosition().row+1},SOFT_TABS:function(t){return t.session.getUseSoftTabs()?"YES":"NO"},TAB_SIZE:function(t){return t.session.getTabSize()},CLIPBOARD:function(t){return C.getText&&C.getText()},FILENAME:function(t){return/[^/\\]*$/.exec(this.FILEPATH(t))[0]},FILENAME_BASE:function(t){return/[^/\\]*$/.exec(this.FILEPATH(t))[0].replace(/\.[^.]*$/,"")},DIRECTORY:function(t){return this.FILEPATH(t).replace(/[^/\\]*$/,"")},FILEPATH:function(t){return"/not implemented.txt"},WORKSPACE_NAME:function(){return"Unknown"},FULLNAME:function(){return"Unknown"},BLOCK_COMMENT_START:function(t){var e=t.session.$mode||{};return e.blockComment&&e.blockComment.start||""},BLOCK_COMMENT_END:function(t){var e=t.session.$mode||{};return e.blockComment&&e.blockComment.end||""},LINE_COMMENT:function(t){var e=t.session.$mode||{};return e.lineCommentStart||""},CURRENT_YEAR:m.bind(null,{year:"numeric"}),CURRENT_YEAR_SHORT:m.bind(null,{year:"2-digit"}),CURRENT_MONTH:m.bind(null,{month:"numeric"}),CURRENT_MONTH_NAME:m.bind(null,{month:"long"}),CURRENT_MONTH_NAME_SHORT:m.bind(null,{month:"short"}),CURRENT_DATE:m.bind(null,{day:"2-digit"}),CURRENT_DAY_NAME:m.bind(null,{weekday:"long"}),CURRENT_DAY_NAME_SHORT:m.bind(null,{weekday:"short"}),CURRENT_HOUR:m.bind(null,{hour:"2-digit",hour12:!1}),CURRENT_MINUTE:m.bind(null,{minute:"2-digit"}),CURRENT_SECOND:m.bind(null,{second:"2-digit"})};A.SELECTED_TEXT=A.SELECTION;function m(t){var e=new Date().toLocaleString("en-us",t);return e.length==1?"0"+e:e}var y=function(){this.snippetMap={},this.snippetNameMap={}};(function(){w.implement(this,_),this.getTokenizer=function(){return y.$tokenizer||this.createTokenizer()},this.createTokenizer=function(){function e(s){return s=s.substr(1),/^\d+$/.test(s)?[{tabstopId:parseInt(s,10)}]:[{text:s}]}function i(s){return"(?:[^\\\\"+s+"]|\\\\.)"}var c={regex:"/("+i("/")+"+)/",onMatch:function(s,o,r){var a=r[0];return a.fmtString=!0,a.guard=s.slice(1,-1),a.flag="",""},next:"formatString"};return y.$tokenizer=new M({start:[{regex:/\\./,onMatch:function(s,o,r){var a=s[1];return(a=="}"&&r.length||"`$\\".indexOf(a)!=-1)&&(s=a),[s]}},{regex:/}/,onMatch:function(s,o,r){return[r.length?r.shift():s]}},{regex:/\$(?:\d+|\w+)/,onMatch:e},{regex:/\$\{[\dA-Z_a-z]+/,onMatch:function(s,o,r){var a=e(s.substr(1));return r.unshift(a[0]),a},next:"snippetVar"},{regex:/\n/,token:"newline",merge:!1}],snippetVar:[{regex:"\\|"+i("\\|")+"*\\|",onMatch:function(s,o,r){var a=s.slice(1,-1).replace(/\\[,|\\]|,/g,function(h){return h.length==2?h[1]:"\0"}).split("\0").map(function(h){return{value:h}});return r[0].choices=a,[a[0]]},next:"start"},c,{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"start"}],formatString:[{regex:/:/,onMatch:function(s,o,r){return r.length&&r[0].expectElse?(r[0].expectElse=!1,r[0].ifEnd={elseEnd:r[0]},[r[0].ifEnd]):":"}},{regex:/\\./,onMatch:function(s,o,r){var a=s[1];return a=="}"&&r.length||"`$\\".indexOf(a)!=-1?s=a:a=="n"?s=`
`:a=="t"?s="	":"ulULE".indexOf(a)!=-1&&(s={changeCase:a,local:a>"a"}),[s]}},{regex:"/\\w*}",onMatch:function(s,o,r){var a=r.shift();return a&&(a.flag=s.slice(1,-1)),this.next=a&&a.tabstopId?"start":"",[a||s]},next:"start"},{regex:/\$(?:\d+|\w+)/,onMatch:function(s,o,r){return[{text:s.slice(1)}]}},{regex:/\${\w+/,onMatch:function(s,o,r){var a={text:s.slice(2)};return r.unshift(a),[a]},next:"formatStringVar"},{regex:/\n/,token:"newline",merge:!1},{regex:/}/,onMatch:function(s,o,r){var a=r.shift();return this.next=a&&a.tabstopId?"start":"",[a||s]},next:"start"}],formatStringVar:[{regex:/:\/\w+}/,onMatch:function(s,o,r){var a=r[0];return a.formatFunction=s.slice(2,-1),[r.shift()]},next:"formatString"},c,{regex:/:[\?\-+]?/,onMatch:function(s,o,r){s[1]=="+"&&(r[0].ifEnd=r[0]),s[1]=="?"&&(r[0].expectElse=!0)},next:"formatString"},{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"formatString"}]}),y.$tokenizer},this.tokenizeTmSnippet=function(e,i){return this.getTokenizer().getLineTokens(e,i).tokens.map(function(c){return c.value||c})},this.getVariableValue=function(e,i,c){if(/^\d+$/.test(i))return(this.variables.__||{})[i]||"";if(/^[A-Z]\d+$/.test(i))return(this.variables[i[0]+"__"]||{})[i.substr(1)]||"";if(i=i.replace(/^TM_/,""),!this.variables.hasOwnProperty(i))return"";var s=this.variables[i];return typeof s=="function"&&(s=this.variables[i](e,i,c)),s??""},this.variables=A,this.tmStrFormat=function(e,i,c){if(!i.fmt)return e;var s=i.flag||"",o=i.guard;o=new RegExp(o,s.replace(/[^gim]/g,""));var r=typeof i.fmt=="string"?this.tokenizeTmSnippet(i.fmt,"formatString"):i.fmt,a=this,h=e.replace(o,function(){var p=a.variables.__;a.variables.__=[].slice.call(arguments);for(var d=a.resolveVariables(r,c),L="E",T=0;T<d.length;T++){var R=d[T];if(typeof R=="object")if(d[T]="",R.changeCase&&R.local){var $=d[T+1];$&&typeof $=="string"&&(R.changeCase=="u"?d[T]=$[0].toUpperCase():d[T]=$[0].toLowerCase(),d[T+1]=$.substr(1))}else R.changeCase&&(L=R.changeCase);else L=="U"?d[T]=R.toUpperCase():L=="L"&&(d[T]=R.toLowerCase())}return a.variables.__=p,d.join("")});return h},this.tmFormatFunction=function(e,i,c){return i.formatFunction=="upcase"?e.toUpperCase():i.formatFunction=="downcase"?e.toLowerCase():e},this.resolveVariables=function(e,i){for(var c=[],s="",o=!0,r=0;r<e.length;r++){var a=e[r];if(typeof a=="string"){c.push(a),a==`
`?(o=!0,s=""):o&&(s=/^\t*/.exec(a)[0],o=/\S/.test(a));continue}if(a){if(o=!1,a.fmtString){var h=e.indexOf(a,r+1);h==-1&&(h=e.length),a.fmt=e.slice(r+1,h),r=h}if(a.text){var p=this.getVariableValue(i,a.text,s)+"";a.fmtString&&(p=this.tmStrFormat(p,a,i)),a.formatFunction&&(p=this.tmFormatFunction(p,a,i)),p&&!a.ifEnd?(c.push(p),d(a)):!p&&a.ifEnd&&d(a.ifEnd)}else a.elseEnd?d(a.elseEnd):(a.tabstopId!=null||a.changeCase!=null)&&c.push(a)}}function d(L){var T=e.indexOf(L,r+1);T!=-1&&(r=T)}return c};var t=function(e,i,c){c===void 0&&(c={});var s=e.getCursorPosition(),o=e.session.getLine(s.row),r=e.session.getTabString(),a=o.match(/^\s*/)[0];s.column<a.length&&(a=a.slice(0,s.column)),i=i.replace(/\r/g,"");var h=this.tokenizeTmSnippet(i);h=this.resolveVariables(h,e),h=h.map(function(N){return N==`
`&&!c.excludeExtraIndent?N+a:typeof N=="string"?N.replace(/\t/g,r):N});var p=[];h.forEach(function(N,H){if(typeof N=="object"){var U=N.tabstopId,I=p[U];if(I||(I=p[U]=[],I.index=U,I.value="",I.parents={}),I.indexOf(N)===-1){N.choices&&!I.choices&&(I.choices=N.choices),I.push(N);var V=h.indexOf(N,H+1);if(V!==-1){var j=h.slice(H+1,V),G=j.some(function(K){return typeof K=="object"});G&&!I.value?I.value=j:j.length&&(!I.value||typeof I.value!="string")&&(I.value=j.join(""))}}}}),p.forEach(function(N){N.length=0});var d={};function L(N){for(var H=[],U=0;U<N.length;U++){var I=N[U];if(typeof I=="object"){if(d[I.tabstopId])continue;var V=N.lastIndexOf(I,U-1);I=H[V]||{tabstopId:I.tabstopId}}H[U]=I}return H}for(var T=0;T<h.length;T++){var R=h[T];if(typeof R=="object"){var $=R.tabstopId,P=p[$],D=h.indexOf(R,T+1);if(d[$]){d[$]===R&&(delete d[$],Object.keys(d).forEach(function(N){P.parents[N]=!0}));continue}d[$]=R;var O=P.value;typeof O!="string"?O=L(O):R.fmt&&(O=this.tmStrFormat(O,R,e)),h.splice.apply(h,[T+1,Math.max(0,D-T)].concat(O,R)),P.indexOf(R)===-1&&P.push(R)}}var B=0,W=0,X="";return h.forEach(function(N){if(typeof N=="string"){var H=N.split(`
`);H.length>1?(W=H[H.length-1].length,B+=H.length-1):W+=N.length,X+=N}else N&&(N.start?N.end={row:B,column:W}:N.start={row:B,column:W})}),{text:X,tabstops:p,tokens:h}};this.getDisplayTextForSnippet=function(e,i){var c=t.call(this,e,i);return c.text},this.insertSnippetForSelection=function(e,i,c){c===void 0&&(c={});var s=t.call(this,e,i,c),o=e.getSelectionRange();c.range&&c.range.compareRange(o)===0&&(o=c.range);var r=e.session.replace(o,s.text),a=new k(e),h=e.inVirtualSelectionMode&&e.selection.index;a.addTabstops(s.tabstops,o.start,r,h)},this.insertSnippet=function(e,i,c){c===void 0&&(c={});var s=this;if(c.range&&!(c.range instanceof g)&&(c.range=g.fromPoints(c.range.start,c.range.end)),e.inVirtualSelectionMode)return s.insertSnippetForSelection(e,i,c);e.forEachSelection(function(){s.insertSnippetForSelection(e,i,c)},null,{keepOrder:!0}),e.tabstopManager&&e.tabstopManager.tabNext()},this.$getScope=function(e){var i=e.session.$mode.$id||"";if(i=i.split("/").pop(),i==="html"||i==="php"){i==="php"&&!e.session.$mode.inlinePhp&&(i="html");var c=e.getCursorPosition(),s=e.session.getState(c.row);typeof s=="object"&&(s=s[0]),s.substring&&(s.substring(0,3)=="js-"?i="javascript":s.substring(0,4)=="css-"?i="css":s.substring(0,4)=="php-"&&(i="php"))}return i},this.getActiveScopes=function(e){var i=this.$getScope(e),c=[i],s=this.snippetMap;return s[i]&&s[i].includeScopes&&c.push.apply(c,s[i].includeScopes),c.push("_"),c},this.expandWithTab=function(e,i){var c=this,s=e.forEachSelection(function(){return c.expandSnippetForSelection(e,i)},null,{keepOrder:!0});return s&&e.tabstopManager&&e.tabstopManager.tabNext(),s},this.expandSnippetForSelection=function(e,i){var c=e.getCursorPosition(),s=e.session.getLine(c.row),o=s.substring(0,c.column),r=s.substr(c.column),a=this.snippetMap,h;return this.getActiveScopes(e).some(function(p){var d=a[p];return d&&(h=this.findMatchingSnippet(d,o,r)),!!h},this),h?(i&&i.dryRun||(e.session.doc.removeInLine(c.row,c.column-h.replaceBefore.length,c.column+h.replaceAfter.length),this.variables.M__=h.matchBefore,this.variables.T__=h.matchAfter,this.insertSnippetForSelection(e,h.content),this.variables.M__=this.variables.T__=null),!0):!1},this.findMatchingSnippet=function(e,i,c){for(var s=e.length;s--;){var o=e[s];if(!(o.startRe&&!o.startRe.test(i))&&!(o.endRe&&!o.endRe.test(c))&&!(!o.startRe&&!o.endRe))return o.matchBefore=o.startRe?o.startRe.exec(i):[""],o.matchAfter=o.endRe?o.endRe.exec(c):[""],o.replaceBefore=o.triggerRe?o.triggerRe.exec(i)[0]:"",o.replaceAfter=o.endTriggerRe?o.endTriggerRe.exec(c)[0]:"",o}},this.snippetMap={},this.snippetNameMap={},this.register=function(e,i){var c=this.snippetMap,s=this.snippetNameMap,o=this;e||(e=[]);function r(p){return p&&!/^\^?\(.*\)\$?$|^\\b$/.test(p)&&(p="(?:"+p+")"),p||""}function a(p,d,L){return p=r(p),d=r(d),L?(p=d+p,p&&p[p.length-1]!="$"&&(p=p+"$")):(p=p+d,p&&p[0]!="^"&&(p="^"+p)),new RegExp(p)}function h(p){p.scope||(p.scope=i||"_"),i=p.scope,c[i]||(c[i]=[],s[i]={});var d=s[i];if(p.name){var L=d[p.name];L&&o.unregister(L),d[p.name]=p}c[i].push(p),p.prefix&&(p.tabTrigger=p.prefix),!p.content&&p.body&&(p.content=Array.isArray(p.body)?p.body.join(`
`):p.body),p.tabTrigger&&!p.trigger&&(!p.guard&&/^\w/.test(p.tabTrigger)&&(p.guard="\\b"),p.trigger=v.escapeRegExp(p.tabTrigger)),!(!p.trigger&&!p.guard&&!p.endTrigger&&!p.endGuard)&&(p.startRe=a(p.trigger,p.guard,!0),p.triggerRe=new RegExp(p.trigger),p.endRe=a(p.endTrigger,p.endGuard,!0),p.endTriggerRe=new RegExp(p.endTrigger))}Array.isArray(e)?e.forEach(h):Object.keys(e).forEach(function(p){h(e[p])}),this._signal("registerSnippets",{scope:i})},this.unregister=function(e,i){var c=this.snippetMap,s=this.snippetNameMap;function o(r){var a=s[r.scope||i];if(a&&a[r.name]){delete a[r.name];var h=c[r.scope||i],p=h&&h.indexOf(r);p>=0&&h.splice(p,1)}}e.content?o(e):Array.isArray(e)&&e.forEach(o)},this.parseSnippetFile=function(e){e=e.replace(/\r/g,"");for(var i=[],c={},s=/^#.*|^({[\s\S]*})\s*$|^(\S+) (.*)$|^((?:\n*\t.*)+)/gm,o;o=s.exec(e);){if(o[1])try{c=JSON.parse(o[1]),i.push(c)}catch{}if(o[4])c.content=o[4].replace(/^\t/gm,""),i.push(c),c={};else{var r=o[2],a=o[3];if(r=="regex"){var h=/\/((?:[^\/\\]|\\.)*)|$/g;c.guard=h.exec(a)[1],c.trigger=h.exec(a)[1],c.endTrigger=h.exec(a)[1],c.endGuard=h.exec(a)[1]}else r=="snippet"?(c.tabTrigger=a.match(/^\S*/)[0],c.name||(c.name=a)):r&&(c[r]=a)}}return i},this.getSnippetByName=function(e,i){var c=this.snippetNameMap,s;return this.getActiveScopes(i).some(function(o){var r=c[o];return r&&(s=r[e]),!!s},this),s}}).call(y.prototype);var k=function(t){if(t.tabstopManager)return t.tabstopManager;t.tabstopManager=this,this.$onChange=this.onChange.bind(this),this.$onChangeSelection=v.delayedCall(this.onChangeSelection.bind(this)).schedule,this.$onChangeSession=this.onChangeSession.bind(this),this.$onAfterExec=this.onAfterExec.bind(this),this.attach(t)};(function(){this.attach=function(t){this.index=0,this.ranges=[],this.tabstops=[],this.$openTabstops=null,this.selectedTabstop=null,this.editor=t,this.editor.on("change",this.$onChange),this.editor.on("changeSelection",this.$onChangeSelection),this.editor.on("changeSession",this.$onChangeSession),this.editor.commands.on("afterExec",this.$onAfterExec),this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler)},this.detach=function(){this.tabstops.forEach(this.removeTabstopMarkers,this),this.ranges=null,this.tabstops=null,this.selectedTabstop=null,this.editor.removeListener("change",this.$onChange),this.editor.removeListener("changeSelection",this.$onChangeSelection),this.editor.removeListener("changeSession",this.$onChangeSession),this.editor.commands.removeListener("afterExec",this.$onAfterExec),this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.tabstopManager=null,this.editor=null},this.onChange=function(t){for(var e=t.action[0]=="r",i=this.selectedTabstop||{},c=i.parents||{},s=(this.tabstops||[]).slice(),o=0;o<s.length;o++){var r=s[o],a=r==i||c[r.index];if(r.rangeList.$bias=a?0:1,t.action=="remove"&&r!==i){var h=r.parents&&r.parents[i.index],p=r.rangeList.pointIndex(t.start,h);p=p<0?-p-1:p+1;var d=r.rangeList.pointIndex(t.end,h);d=d<0?-d-1:d-1;for(var L=r.rangeList.ranges.slice(p,d),T=0;T<L.length;T++)this.removeRange(L[T])}r.rangeList.$onChange(t)}var R=this.editor.session;!this.$inChange&&e&&R.getLength()==1&&!R.getValue()&&this.detach()},this.updateLinkedFields=function(){var t=this.selectedTabstop;if(!(!t||!t.hasLinkedRanges||!t.firstNonLinked)){this.$inChange=!0;for(var e=this.editor.session,i=e.getTextRange(t.firstNonLinked),c=0;c<t.length;c++){var s=t[c];if(s.linked){var o=s.original,r=S.snippetManager.tmStrFormat(i,o,this.editor);e.replace(s,r)}}this.$inChange=!1}},this.onAfterExec=function(t){t.command&&!t.command.readOnly&&this.updateLinkedFields()},this.onChangeSelection=function(){if(this.editor){for(var t=this.editor.selection.lead,e=this.editor.selection.anchor,i=this.editor.selection.isEmpty(),c=0;c<this.ranges.length;c++)if(!this.ranges[c].linked){var s=this.ranges[c].contains(t.row,t.column),o=i||this.ranges[c].contains(e.row,e.column);if(s&&o)return}this.detach()}},this.onChangeSession=function(){this.detach()},this.tabNext=function(t){var e=this.tabstops.length,i=this.index+(t||1);i=Math.min(Math.max(i,1),e),i==e&&(i=0),this.selectTabstop(i),i===0&&this.detach()},this.selectTabstop=function(t){this.$openTabstops=null;var e=this.tabstops[this.index];if(e&&this.addTabstopMarkers(e),this.index=t,e=this.tabstops[this.index],!(!e||!e.length)){this.selectedTabstop=e;var i=e.firstNonLinked||e;if(e.choices&&(i.cursor=i.start),this.editor.inVirtualSelectionMode)this.editor.selection.fromOrientedRange(i);else{var c=this.editor.multiSelect;c.toSingleRange(i);for(var s=0;s<e.length;s++)e.hasLinkedRanges&&e[s].linked||c.addRange(e[s].clone(),!0)}this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler),this.selectedTabstop&&this.selectedTabstop.choices&&this.editor.execCommand("startAutocomplete",{matches:this.selectedTabstop.choices})}},this.addTabstops=function(t,e,i){var c=this.useLink||!this.editor.getOption("enableMultiselect");if(this.$openTabstops||(this.$openTabstops=[]),!t[0]){var s=g.fromPoints(i,i);l(s.start,e),l(s.end,e),t[0]=[s],t[0].index=0}var o=this.index,r=[o+1,0],a=this.ranges;t.forEach(function(h,p){for(var d=this.$openTabstops[p]||h,L=0;L<h.length;L++){var T=h[L],R=g.fromPoints(T.start,T.end||T.start);n(R.start,e),n(R.end,e),R.original=T,R.tabstop=d,a.push(R),d!=h?d.unshift(R):d[L]=R,T.fmtString||d.firstNonLinked&&c?(R.linked=!0,d.hasLinkedRanges=!0):d.firstNonLinked||(d.firstNonLinked=R)}d.firstNonLinked||(d.hasLinkedRanges=!1),d===h&&(r.push(d),this.$openTabstops[p]=d),this.addTabstopMarkers(d),d.rangeList=d.rangeList||new x,d.rangeList.$bias=0,d.rangeList.addList(d)},this),r.length>2&&(this.tabstops.length&&r.push(r.splice(2,1)[0]),this.tabstops.splice.apply(this.tabstops,r))},this.addTabstopMarkers=function(t){var e=this.editor.session;t.forEach(function(i){i.markerId||(i.markerId=e.addMarker(i,"ace_snippet-marker","text"))})},this.removeTabstopMarkers=function(t){var e=this.editor.session;t.forEach(function(i){e.removeMarker(i.markerId),i.markerId=null})},this.removeRange=function(t){var e=t.tabstop.indexOf(t);e!=-1&&t.tabstop.splice(e,1),e=this.ranges.indexOf(t),e!=-1&&this.ranges.splice(e,1),e=t.tabstop.rangeList.ranges.indexOf(t),e!=-1&&t.tabstop.splice(e,1),this.editor.session.removeMarker(t.markerId),t.tabstop.length||(e=this.tabstops.indexOf(t.tabstop),e!=-1&&this.tabstops.splice(e,1),this.tabstops.length||this.detach())},this.keyboardHandler=new b,this.keyboardHandler.bindKeys({Tab:function(t){S.snippetManager&&S.snippetManager.expandWithTab(t)||(t.tabstopManager.tabNext(1),t.renderer.scrollCursorIntoView())},"Shift-Tab":function(t){t.tabstopManager.tabNext(-1),t.renderer.scrollCursorIntoView()},Esc:function(t){t.tabstopManager.detach()}})}).call(k.prototype);var n=function(t,e){t.row==0&&(t.column+=e.column),t.row+=e.row},l=function(t,e){t.row==e.row&&(t.column-=e.column),t.row-=e.row};E.importCssString(`
.ace_snippet-marker {
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: rgba(194, 193, 208, 0.09);
    border: 1px dotted rgba(211, 208, 235, 0.62);
    position: absolute;
}`,"snippets.css",!1),S.snippetManager=new y;var u=f("./editor").Editor;(function(){this.insertSnippet=function(t,e){return S.snippetManager.insertSnippet(this,t,e)},this.expandSnippet=function(t){return S.snippetManager.expandWithTab(this,t)}}).call(u.prototype)}),ace.define("ace/autocomplete/popup",["require","exports","module","ace/virtual_renderer","ace/editor","ace/range","ace/lib/event","ace/lib/lang","ace/lib/dom","ace/config"],function(f,S,F){var E=f("../virtual_renderer").VirtualRenderer,w=f("../editor").Editor,_=f("../range").Range,v=f("../lib/event"),g=f("../lib/lang"),x=f("../lib/dom"),b=f("../config").nls,M=function(m){return"suggest-aria-id:".concat(m)},C=function(m){var y=new E(m);y.$maxLines=4;var k=new w(y);return k.setHighlightActiveLine(!1),k.setShowPrintMargin(!1),k.renderer.setShowGutter(!1),k.renderer.setHighlightGutterLine(!1),k.$mouseHandler.$focusTimeout=0,k.$highlightTagPending=!0,k},A=function(){function m(y){var k=x.createElement("div"),n=new C(k);y&&y.appendChild(k),k.style.display="none",n.renderer.content.style.cursor="default",n.renderer.setStyle("ace_autocomplete"),n.renderer.container.setAttribute("role","listbox"),n.renderer.container.setAttribute("aria-label",b("Autocomplete suggestions")),n.setOption("displayIndentGuides",!1),n.setOption("dragDelay",150);var l=function(){};n.focus=l,n.$isFocused=!0,n.renderer.$cursorLayer.restartTimer=l,n.renderer.$cursorLayer.element.style.opacity=0,n.renderer.$maxLines=8,n.renderer.$keepTextAreaAtCursor=!1,n.setHighlightActiveLine(!1),n.session.highlight(""),n.session.$searchHighlight.clazz="ace_highlight-marker",n.on("mousedown",function(o){var r=o.getDocumentPosition();n.selection.moveToPosition(r),e.start.row=e.end.row=r.row,o.stop()});var u,t=new _(-1,0,-1,1/0),e=new _(-1,0,-1,1/0);e.id=n.session.addMarker(e,"ace_active-line","fullLine"),n.setSelectOnHover=function(o){o?t.id&&(n.session.removeMarker(t.id),t.id=null):t.id=n.session.addMarker(t,"ace_line-hover","fullLine")},n.setSelectOnHover(!1),n.on("mousemove",function(o){if(!u){u=o;return}if(!(u.x==o.x&&u.y==o.y)){u=o,u.scrollTop=n.renderer.scrollTop;var r=u.getDocumentPosition().row;t.start.row!=r&&(t.id||n.setRow(r),c(r))}}),n.renderer.on("beforeRender",function(){if(u&&t.start.row!=-1){u.$pos=null;var o=u.getDocumentPosition().row;t.id||n.setRow(o),c(o,!0)}}),n.renderer.on("afterRender",function(){var o=n.getRow(),r=n.renderer.$textLayer,a=r.element.childNodes[o-r.config.firstRow],h=document.activeElement;if(a!==r.selectedNode&&r.selectedNode&&(x.removeCssClass(r.selectedNode,"ace_selected"),h.removeAttribute("aria-activedescendant"),r.selectedNode.removeAttribute("id")),r.selectedNode=a,a){x.addCssClass(a,"ace_selected");var p=M(o);a.id=p,n.renderer.container.setAttribute("aria-activedescendant",p),h.setAttribute("aria-activedescendant",p),a.setAttribute("role","option"),a.setAttribute("aria-label",n.getData(o).value),a.setAttribute("aria-setsize",n.data.length),a.setAttribute("aria-posinset",o)}});var i=function(){c(-1)},c=function(o,r){o!==t.start.row&&(t.start.row=t.end.row=o,r||n.session._emit("changeBackMarker"),n._emit("changeHoverMarker"))};n.getHoveredRow=function(){return t.start.row},v.addListener(n.container,"mouseout",i),n.on("hide",i),n.on("changeSelection",i),n.session.doc.getLength=function(){return n.data.length},n.session.doc.getLine=function(o){var r=n.data[o];return typeof r=="string"?r:r&&r.value||""};var s=n.session.bgTokenizer;return s.$tokenizeRow=function(o){var r=n.data[o],a=[];if(!r)return a;typeof r=="string"&&(r={value:r});var h=r.caption||r.value||r.name;function p(O,B){O&&a.push({type:(r.className||"")+(B||""),value:O})}for(var d=h.toLowerCase(),L=(n.filterText||"").toLowerCase(),T=0,R=0,$=0;$<=L.length;$++)if($!=R&&(r.matchMask&1<<$||$==L.length)){var P=L.slice(R,$);R=$;var D=d.indexOf(P,T);if(D==-1)continue;p(h.slice(T,D),""),T=D+P.length,p(h.slice(D,T),"completion-highlight")}return p(h.slice(T,h.length),""),a.push({type:"completion-spacer",value:" "}),r.meta&&a.push({type:"completion-meta",value:r.meta}),r.message&&a.push({type:"completion-message",value:r.message}),a},s.$updateOnChange=l,s.start=l,n.session.$computeWidth=function(){return this.screenWidth=0},n.isOpen=!1,n.isTopdown=!1,n.autoSelect=!0,n.filterText="",n.data=[],n.setData=function(o,r){n.filterText=r||"",n.setValue(g.stringRepeat(`
`,o.length),-1),n.data=o||[],n.setRow(0)},n.getData=function(o){return n.data[o]},n.getRow=function(){return e.start.row},n.setRow=function(o){o=Math.max(this.autoSelect?0:-1,Math.min(this.data.length-1,o)),e.start.row!=o&&(n.selection.clearSelection(),e.start.row=e.end.row=o||0,n.session._emit("changeBackMarker"),n.moveCursorTo(o||0,0),n.isOpen&&n._signal("select"))},n.on("changeSelection",function(){n.isOpen&&n.setRow(n.selection.lead.row),n.renderer.scrollCursorIntoView()}),n.hide=function(){this.container.style.display="none",n.anchorPos=null,n.anchor=null,n.isOpen&&(n.isOpen=!1,this._signal("hide"))},n.tryShow=function(o,r,a,h){if(!h&&n.isOpen&&n.anchorPos&&n.anchor&&n.anchorPos.top===o.top&&n.anchorPos.left===o.left&&n.anchor===a)return!0;var p=this.container,d=window.innerHeight,L=window.innerWidth,T=this.renderer,R=T.$maxLines*r*1.4,$={top:0,bottom:0,left:0},P=d-o.top-3*this.$borderSize-r,D=o.top-3*this.$borderSize;a||(D<=P||P>=R?a="bottom":a="top"),a==="top"?($.bottom=o.top-this.$borderSize,$.top=$.bottom-R):a==="bottom"&&($.top=o.top+r+this.$borderSize,$.bottom=$.top+R);var O=$.top>=0&&$.bottom<=d;if(!h&&!O)return!1;O?T.$maxPixelHeight=null:a==="top"?T.$maxPixelHeight=D:T.$maxPixelHeight=P,a==="top"?(p.style.top="",p.style.bottom=d-$.bottom+"px",n.isTopdown=!1):(p.style.top=$.top+"px",p.style.bottom="",n.isTopdown=!0),p.style.display="";var B=o.left;return B+p.offsetWidth>L&&(B=L-p.offsetWidth),p.style.left=B+"px",p.style.right="",n.isOpen||(n.isOpen=!0,this._signal("show"),u=null),n.anchorPos=o,n.anchor=a,!0},n.show=function(o,r,a){this.tryShow(o,r,a?"bottom":void 0,!0)},n.goTo=function(o){var r=this.getRow(),a=this.session.getLength()-1;switch(o){case"up":r=r<=0?a:r-1;break;case"down":r=r>=a?-1:r+1;break;case"start":r=0;break;case"end":r=a;break}this.setRow(r)},n.getTextLeftOffset=function(){return this.$borderSize+this.renderer.$padding+this.$imageSize},n.$imageSize=0,n.$borderSize=1,n}return m}();x.importCssString(`
.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {
    background-color: #CAD6FA;
    z-index: 1;
}
.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {
    background-color: #3a674e;
}
.ace_editor.ace_autocomplete .ace_line-hover {
    border: 1px solid #abbffe;
    margin-top: -1px;
    background: rgba(233,233,253,0.4);
    position: absolute;
    z-index: 2;
}
.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {
    border: 1px solid rgba(109, 150, 13, 0.8);
    background: rgba(58, 103, 78, 0.62);
}
.ace_completion-meta {
    opacity: 0.5;
    margin: 0 0.9em;
}
.ace_completion-message {
    color: blue;
}
.ace_editor.ace_autocomplete .ace_completion-highlight{
    color: #2d69c7;
}
.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{
    color: #93ca12;
}
.ace_editor.ace_autocomplete {
    width: 300px;
    z-index: 200000;
    border: 1px lightgray solid;
    position: fixed;
    box-shadow: 2px 3px 5px rgba(0,0,0,.2);
    line-height: 1.4;
    background: #fefefe;
    color: #111;
}
.ace_dark.ace_editor.ace_autocomplete {
    border: 1px #484747 solid;
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);
    line-height: 1.4;
    background: #25282c;
    color: #c1c1c1;
}
.ace_autocomplete_right .ace_text-layer  {
    width: calc(100% - 8px);
}
.ace_autocomplete_right .ace_line {
    display: flex;
}
.ace_autocomplete_right .ace_completion-spacer {
    flex: 1;
}
`,"autocompletion.css",!1),S.AcePopup=A,S.$singleLineEditor=C,S.getAriaId=M}),ace.define("ace/autocomplete/inline",["require","exports","module","ace/snippets"],function(f,S,F){var E=f("../snippets").snippetManager,w=function(){function _(){this.editor=null}return _.prototype.show=function(v,g,x){if(x=x||"",v&&this.editor&&this.editor!==v&&(this.hide(),this.editor=null),!v||!g)return!1;var b=g.snippet?E.getDisplayTextForSnippet(v,g.snippet):g.value;return!b||!b.startsWith(x)?!1:(this.editor=v,b=b.slice(x.length),b===""?v.removeGhostText():v.setGhostText(b),!0)},_.prototype.isOpen=function(){return this.editor?!!this.editor.renderer.$ghostText:!1},_.prototype.hide=function(){return this.editor?(this.editor.removeGhostText(),!0):!1},_.prototype.destroy=function(){this.hide(),this.editor=null},_}();S.AceInline=w}),ace.define("ace/autocomplete/util",["require","exports","module"],function(f,S,F){S.parForEach=function(w,_,v){var g=0,x=w.length;x===0&&v();for(var b=0;b<x;b++)_(w[b],function(M,C){g++,g===x&&v(M,C)})};var E=/[a-zA-Z_0-9\$\-\u00A2-\u2000\u2070-\uFFFF]/;S.retrievePrecedingIdentifier=function(w,_,v){v=v||E;for(var g=[],x=_-1;x>=0&&v.test(w[x]);x--)g.push(w[x]);return g.reverse().join("")},S.retrieveFollowingIdentifier=function(w,_,v){v=v||E;for(var g=[],x=_;x<w.length&&v.test(w[x]);x++)g.push(w[x]);return g},S.getCompletionPrefix=function(w){var _=w.getCursorPosition(),v=w.session.getLine(_.row),g;return w.completers.forEach((function(x){x.identifierRegexps&&x.identifierRegexps.forEach((function(b){!g&&b&&(g=this.retrievePrecedingIdentifier(v,_.column,b))}).bind(this))}).bind(this)),g||this.retrievePrecedingIdentifier(v,_.column)},S.triggerAutocomplete=function(w){var _=w.getCursorPosition(),v=w.session.getLine(_.row),g=_.column===0?0:_.column-1,x=v[g];return w.completers.some(function(b){if(b.triggerCharacters&&Array.isArray(b.triggerCharacters))return b.triggerCharacters.includes(x)})}}),ace.define("ace/autocomplete",["require","exports","module","ace/keyboard/hash_handler","ace/autocomplete/popup","ace/autocomplete/inline","ace/autocomplete/popup","ace/autocomplete/util","ace/lib/lang","ace/lib/dom","ace/snippets","ace/config"],function(f,S,F){var E=f("./keyboard/hash_handler").HashHandler,w=f("./autocomplete/popup").AcePopup,_=f("./autocomplete/inline").AceInline,v=f("./autocomplete/popup").getAriaId,g=f("./autocomplete/util"),x=f("./lib/lang"),b=f("./lib/dom"),M=f("./snippets").snippetManager,C=f("./config"),A=function(n,l){l.completer&&l.completer.destroy()},m=function(){function n(){this.autoInsert=!1,this.autoSelect=!0,this.autoShown=!1,this.exactMatch=!1,this.inlineEnabled=!1,this.keyboardHandler=new E,this.keyboardHandler.bindKeys(this.commands),this.parentNode=null,this.blurListener=this.blurListener.bind(this),this.changeListener=this.changeListener.bind(this),this.mousedownListener=this.mousedownListener.bind(this),this.mousewheelListener=this.mousewheelListener.bind(this),this.changeTimer=x.delayedCall((function(){this.updateCompletions(!0)}).bind(this)),this.tooltipTimer=x.delayedCall(this.updateDocTooltip.bind(this),50)}return n.prototype.$init=function(){return this.popup=new w(this.parentNode||document.body||document.documentElement),this.popup.on("click",(function(l){this.insertMatch(),l.stop()}).bind(this)),this.popup.focus=this.editor.focus.bind(this.editor),this.popup.on("show",this.$onPopupChange.bind(this)),this.popup.on("hide",this.$onHidePopup.bind(this)),this.popup.on("select",this.$onPopupChange.bind(this)),this.popup.on("changeHoverMarker",this.tooltipTimer.bind(null,null)),this.popup},n.prototype.$initInline=function(){if(!(!this.inlineEnabled||this.inlineRenderer))return this.inlineRenderer=new _,this.inlineRenderer},n.prototype.getPopup=function(){return this.popup||this.$init()},n.prototype.$onHidePopup=function(){this.inlineRenderer&&this.inlineRenderer.hide(),this.hideDocTooltip()},n.prototype.$onPopupChange=function(l){if(this.inlineRenderer&&this.inlineEnabled){var u=l?null:this.popup.getData(this.popup.getRow()),t=g.getCompletionPrefix(this.editor);this.inlineRenderer.show(this.editor,u,t)||this.inlineRenderer.hide(),this.$updatePopupPosition()}this.tooltipTimer.call(null,null)},n.prototype.$updatePopupPosition=function(){var l=this.editor,u=l.renderer,t=u.layerConfig.lineHeight,e=u.$cursorLayer.getPixelPosition(this.base,!0);e.left-=this.popup.getTextLeftOffset();var i=l.container.getBoundingClientRect();e.top+=i.top-u.layerConfig.offset,e.left+=i.left-l.renderer.scrollLeft,e.left+=u.gutterWidth;var c={top:e.top,left:e.left};u.$ghostText&&u.$ghostTextWidget&&this.base.row===u.$ghostText.position.row&&(c.top+=u.$ghostTextWidget.el.offsetHeight),!this.popup.tryShow(c,t,"bottom")&&(this.popup.tryShow(e,t,"top")||this.popup.show(e,t))},n.prototype.openPopup=function(l,u,t){this.popup||this.$init(),this.inlineEnabled&&!this.inlineRenderer&&this.$initInline(),this.popup.autoSelect=this.autoSelect,this.popup.setData(this.completions.filtered,this.completions.filterText),this.editor.textInput.setAriaOptions&&this.editor.textInput.setAriaOptions({activeDescendant:v(this.popup.getRow()),inline:this.inlineEnabled}),l.keyBinding.addKeyboardHandler(this.keyboardHandler),this.popup.setRow(this.autoSelect?0:-1),t?t&&!u&&this.detach():(this.popup.setTheme(l.getTheme()),this.popup.setFontSize(l.getFontSize()),this.$updatePopupPosition(),this.tooltipNode&&this.updateDocTooltip()),this.changeTimer.cancel()},n.prototype.detach=function(){this.editor&&(this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.off("changeSelection",this.changeListener),this.editor.off("blur",this.blurListener),this.editor.off("mousedown",this.mousedownListener),this.editor.off("mousewheel",this.mousewheelListener)),this.changeTimer.cancel(),this.hideDocTooltip(),this.completionProvider&&this.completionProvider.detach(),this.popup&&this.popup.isOpen&&this.popup.hide(),this.base&&this.base.detach(),this.activated=!1,this.completionProvider=this.completions=this.base=null},n.prototype.changeListener=function(l){var u=this.editor.selection.lead;(u.row!=this.base.row||u.column<this.base.column)&&this.detach(),this.activated?this.changeTimer.schedule():this.detach()},n.prototype.blurListener=function(l){var u=document.activeElement,t=this.editor.textInput.getElement(),e=l.relatedTarget&&this.tooltipNode&&this.tooltipNode.contains(l.relatedTarget),i=this.popup&&this.popup.container;u!=t&&u.parentNode!=i&&!e&&u!=this.tooltipNode&&l.relatedTarget!=t&&this.detach()},n.prototype.mousedownListener=function(l){this.detach()},n.prototype.mousewheelListener=function(l){this.detach()},n.prototype.goTo=function(l){this.popup.goTo(l)},n.prototype.insertMatch=function(l,u){if(l||(l=this.popup.getData(this.popup.getRow())),!l)return!1;if(l.value==="")return this.detach();var t=this.completions,e=this.getCompletionProvider().insertMatch(this.editor,l,t.filterText,u);return this.completions==t&&this.detach(),e},n.prototype.showPopup=function(l,u){this.editor&&this.detach(),this.activated=!0,this.editor=l,l.completer!=this&&(l.completer&&l.completer.detach(),l.completer=this),l.on("changeSelection",this.changeListener),l.on("blur",this.blurListener),l.on("mousedown",this.mousedownListener),l.on("mousewheel",this.mousewheelListener),this.updateCompletions(!1,u)},n.prototype.getCompletionProvider=function(){return this.completionProvider||(this.completionProvider=new y),this.completionProvider},n.prototype.gatherCompletions=function(l,u){return this.getCompletionProvider().gatherCompletions(l,u)},n.prototype.updateCompletions=function(l,u){if(l&&this.base&&this.completions){var e=this.editor.getCursorPosition(),i=this.editor.session.getTextRange({start:this.base,end:e});if(i==this.completions.filterText)return;if(this.completions.setFilter(i),!this.completions.filtered.length)return this.detach();if(this.completions.filtered.length==1&&this.completions.filtered[0].value==i&&!this.completions.filtered[0].snippet)return this.detach();this.openPopup(this.editor,i,l);return}if(u&&u.matches){var e=this.editor.getSelectionRange().start;return this.base=this.editor.session.doc.createAnchor(e.row,e.column),this.base.$insertRight=!0,this.completions=new k(u.matches),this.openPopup(this.editor,"",l)}var t=this.editor.getSession(),e=this.editor.getCursorPosition(),i=g.getCompletionPrefix(this.editor);this.base=t.doc.createAnchor(e.row,e.column-i.length),this.base.$insertRight=!0;var c={exactMatch:this.exactMatch};this.getCompletionProvider().provideCompletions(this.editor,c,(function(s,o,r){var a=o.filtered,h=g.getCompletionPrefix(this.editor);if(r){if(!a.length){var p=!this.autoShown&&this.emptyMessage;if(typeof p=="function"&&(p=this.emptyMessage(h)),p){var d=[{caption:this.emptyMessage(h),value:""}];this.completions=new k(d),this.openPopup(this.editor,h,l);return}return this.detach()}if(a.length==1&&a[0].value==h&&!a[0].snippet)return this.detach();if(this.autoInsert&&!this.autoShown&&a.length==1)return this.insertMatch(a[0])}this.completions=o,this.openPopup(this.editor,h,l)}).bind(this))},n.prototype.cancelContextMenu=function(){this.editor.$mouseHandler.cancelContextMenu()},n.prototype.updateDocTooltip=function(){var l=this.popup,u=l.data,t=u&&(u[l.getHoveredRow()]||u[l.getRow()]),e=null;if(!t||!this.editor||!this.popup.isOpen)return this.hideDocTooltip();for(var i=this.editor.completers.length,c=0;c<i;c++){var s=this.editor.completers[c];if(s.getDocTooltip&&t.completerId===s.id){e=s.getDocTooltip(t);break}}if(!e&&typeof t!="string"&&(e=t),typeof e=="string"&&(e={docText:e}),!e||!(e.docHTML||e.docText))return this.hideDocTooltip();this.showDocTooltip(e)},n.prototype.showDocTooltip=function(l){this.tooltipNode||(this.tooltipNode=b.createElement("div"),this.tooltipNode.style.margin=0,this.tooltipNode.style.pointerEvents="auto",this.tooltipNode.tabIndex=-1,this.tooltipNode.onblur=this.blurListener.bind(this),this.tooltipNode.onclick=this.onTooltipClick.bind(this));var u=this.editor.renderer.theme;this.tooltipNode.className="ace_tooltip ace_doc-tooltip "+(u.isDark?"ace_dark ":"")+(u.cssClass||"");var t=this.tooltipNode;l.docHTML?t.innerHTML=l.docHTML:l.docText&&(t.textContent=l.docText),t.parentNode||this.popup.container.appendChild(this.tooltipNode);var e=this.popup,i=e.container.getBoundingClientRect();t.style.top=e.container.style.top,t.style.bottom=e.container.style.bottom,t.style.display="block",window.innerWidth-i.right<320?i.left<320?e.isTopdown?(t.style.top=i.bottom+"px",t.style.left=i.left+"px",t.style.right="",t.style.bottom=""):(t.style.top=e.container.offsetTop-t.offsetHeight+"px",t.style.left=i.left+"px",t.style.right="",t.style.bottom=""):(t.style.right=window.innerWidth-i.left+"px",t.style.left=""):(t.style.left=i.right+1+"px",t.style.right="")},n.prototype.hideDocTooltip=function(){if(this.tooltipTimer.cancel(),!!this.tooltipNode){var l=this.tooltipNode;!this.editor.isFocused()&&document.activeElement==l&&this.editor.focus(),this.tooltipNode=null,l.parentNode&&l.parentNode.removeChild(l)}},n.prototype.onTooltipClick=function(l){for(var u=l.target;u&&u!=this.tooltipNode;){if(u.nodeName=="A"&&u.href){u.rel="noreferrer",u.target="_blank";break}u=u.parentNode}},n.prototype.destroy=function(){if(this.detach(),this.popup){this.popup.destroy();var l=this.popup.container;l&&l.parentNode&&l.parentNode.removeChild(l)}this.editor&&this.editor.completer==this&&(this.editor.off("destroy",A),this.editor.completer=null),this.inlineRenderer=this.popup=this.editor=null},n}();m.prototype.commands={Up:function(n){n.completer.goTo("up")},Down:function(n){n.completer.goTo("down")},"Ctrl-Up|Ctrl-Home":function(n){n.completer.goTo("start")},"Ctrl-Down|Ctrl-End":function(n){n.completer.goTo("end")},Esc:function(n){n.completer.detach()},Return:function(n){return n.completer.insertMatch()},"Shift-Return":function(n){n.completer.insertMatch(null,{deleteSuffix:!0})},Tab:function(n){var l=n.completer.insertMatch();if(!l&&!n.tabstopManager)n.completer.goTo("down");else return l},PageUp:function(n){n.completer.popup.gotoPageUp()},PageDown:function(n){n.completer.popup.gotoPageDown()}},m.for=function(n){return n.completer instanceof m||(n.completer&&(n.completer.destroy(),n.completer=null),C.get("sharedPopups")?(m.$sharedInstance||(m.$sharedInstance=new m),n.completer=m.$sharedInstance):(n.completer=new m,n.once("destroy",A))),n.completer},m.startCommand={name:"startAutocomplete",exec:function(n,l){var u=m.for(n);u.autoInsert=!1,u.autoSelect=!0,u.autoShown=!1,u.showPopup(n,l),u.cancelContextMenu()},bindKey:"Ctrl-Space|Ctrl-Shift-Space|Alt-Space"};var y=function(){function n(){this.active=!0}return n.prototype.insertByIndex=function(l,u,t){return!this.completions||!this.completions.filtered?!1:this.insertMatch(l,this.completions.filtered[u],t)},n.prototype.insertMatch=function(l,u,t){if(!u)return!1;if(l.startOperation({command:{name:"insertMatch"}}),u.completer&&u.completer.insertMatch)u.completer.insertMatch(l,u);else{if(!this.completions)return!1;if(this.completions.filterText){var e;l.selection.getAllRanges?e=l.selection.getAllRanges():e=[l.getSelectionRange()];for(var i=0,c;c=e[i];i++)c.start.column-=this.completions.filterText.length,l.session.remove(c)}u.snippet?M.insertSnippet(l,u.snippet,{range:u.range}):this.$insertString(l,u),u.command&&u.command==="startAutocomplete"&&l.execCommand(u.command)}return l.endOperation(),!0},n.prototype.$insertString=function(l,u){var t=u.value||u;if(u.range){if(l.inVirtualSelectionMode)return l.session.replace(u.range,t);l.forEachSelection(function(){var e=l.getSelectionRange();u.range.compareRange(e)===0?l.session.replace(u.range,t):l.insert(t)},null,{keepOrder:!0})}else l.execCommand("insertstring",t)},n.prototype.gatherCompletions=function(l,u){var t=l.getSession(),e=l.getCursorPosition(),i=g.getCompletionPrefix(l),c=[],s=l.completers.length;return l.completers.forEach(function(o,r){o.getCompletions(l,t,e,i,function(a,h){!a&&h&&(c=c.concat(h)),u(null,{prefix:g.getCompletionPrefix(l),matches:c,finished:--s===0})})}),!0},n.prototype.provideCompletions=function(l,u,t){var e=(function(o){var r=o.prefix,a=o.matches;this.completions=new k(a),u.exactMatch&&(this.completions.exactMatch=!0),u.ignoreCaption&&(this.completions.ignoreCaption=!0),this.completions.setFilter(r),(o.finished||this.completions.filtered.length)&&t(null,this.completions,o.finished)}).bind(this),i=!0,c=null;if(this.gatherCompletions(l,(function(o,r){if(this.active){o&&(t(o,[],!0),this.detach());var a=r.prefix;if(a.indexOf(r.prefix)===0){if(i){c=r;return}e(r)}}}).bind(this)),i=!1,c){var s=c;c=null,e(s)}},n.prototype.detach=function(){this.active=!1},n}(),k=function(){function n(l,u){this.all=l,this.filtered=l,this.filterText=u||"",this.exactMatch=!1,this.ignoreCaption=!1}return n.prototype.setFilter=function(l){if(l.length>this.filterText&&l.lastIndexOf(this.filterText,0)===0)var u=this.filtered;else var u=this.all;this.filterText=l,u=this.filterCompletions(u,this.filterText),u=u.sort(function(e,i){return i.exactMatch-e.exactMatch||i.$score-e.$score||(e.caption||e.value).localeCompare(i.caption||i.value)});var t=null;u=u.filter(function(e){var i=e.snippet||e.caption||e.value;return i===t?!1:(t=i,!0)}),this.filtered=u},n.prototype.filterCompletions=function(l,u){var t=[],e=u.toUpperCase(),i=u.toLowerCase();e:for(var c=0,s;s=l[c];c++){var o=!this.ignoreCaption&&s.caption||s.value||s.snippet;if(o){var r=-1,a=0,h=0,p,d;if(this.exactMatch){if(u!==o.substr(0,u.length))continue e}else{var L=o.toLowerCase().indexOf(i);if(L>-1)h=L;else for(var T=0;T<u.length;T++){var R=o.indexOf(i[T],r+1),$=o.indexOf(e[T],r+1);if(p=R>=0&&($<0||R<$)?R:$,p<0)continue e;d=p-r-1,d>0&&(r===-1&&(h+=10),h+=d,a=a|1<<T),r=p}}s.matchMask=a,s.exactMatch=h?0:1,s.$score=(s.score||0)-h,t.push(s)}}return t},n}();S.Autocomplete=m,S.CompletionProvider=y,S.FilteredList=k}),ace.define("ace/autocomplete/text_completer",["require","exports","module","ace/range"],function(f,S,F){var E=f("../range").Range,w=/[^a-zA-Z_0-9\$\-\u00C0-\u1FFF\u2C00-\uD7FF\w]+/;function _(g,x){var b=g.getTextRange(E.fromPoints({row:0,column:0},x));return b.split(w).length-1}function v(g,x){var b=_(g,x),M=g.getValue().split(w),C=Object.create(null),A=M[b];return M.forEach(function(m,y){if(!(!m||m===A)){var k=Math.abs(b-y),n=M.length-k;C[m]?C[m]=Math.max(n,C[m]):C[m]=n}}),C}S.getCompletions=function(g,x,b,M,C){var A=v(x,b),m=Object.keys(A);C(null,m.map(function(y){return{caption:y,value:y,score:A[y],meta:"local"}}))}}),ace.define("ace/ext/language_tools",["require","exports","module","ace/snippets","ace/autocomplete","ace/config","ace/lib/lang","ace/autocomplete/util","ace/autocomplete/text_completer","ace/editor","ace/config"],function(f,S,F){var E=f("../snippets").snippetManager,w=f("../autocomplete").Autocomplete,_=f("../config"),v=f("../lib/lang"),g=f("../autocomplete/util"),x=f("../autocomplete/text_completer"),b={getCompletions:function(t,e,i,c,s){if(e.$mode.completer)return e.$mode.completer.getCompletions(t,e,i,c,s);var o=t.session.getState(i.row),r=e.$mode.getCompletions(o,e,i,c);r=r.map(function(a){return a.completerId=b.id,a}),s(null,r)},id:"keywordCompleter"},M=function(t){var e={};return t.replace(/\${(\d+)(:(.*?))?}/g,function(i,c,s,o){return e[c]=o||""}).replace(/\$(\d+?)/g,function(i,c){return e[c]})},C={getCompletions:function(t,e,i,c,s){var o=[],r=e.getTokenAt(i.row,i.column);r&&r.type.match(/(tag-name|tag-open|tag-whitespace|attribute-name|attribute-value)\.xml$/)?o.push("html-tag"):o=E.getActiveScopes(t);var a=E.snippetMap,h=[];o.forEach(function(p){for(var d=a[p]||[],L=d.length;L--;){var T=d[L],R=T.name||T.tabTrigger;R&&h.push({caption:R,snippet:T.content,meta:T.tabTrigger&&!T.name?T.tabTrigger+"⇥ ":"snippet",completerId:C.id})}},this),s(null,h)},getDocTooltip:function(t){t.snippet&&!t.docHTML&&(t.docHTML=["<b>",v.escapeHTML(t.caption),"</b>","<hr></hr>",v.escapeHTML(M(t.snippet))].join(""))},id:"snippetCompleter"},A=[C,x,b];S.setCompleters=function(t){A.length=0,t&&A.push.apply(A,t)},S.addCompleter=function(t){A.push(t)},S.textCompleter=x,S.keyWordCompleter=b,S.snippetCompleter=C;var m={name:"expandSnippet",exec:function(t){return E.expandWithTab(t)},bindKey:"Tab"},y=function(t,e){k(e.session.$mode)},k=function(t){typeof t=="string"&&(t=_.$modes[t]),t&&(E.files||(E.files={}),n(t.$id,t.snippetFileId),t.modes&&t.modes.forEach(k))},n=function(t,e){!e||!t||E.files[t]||(E.files[t]={},_.loadModule(e,function(i){i&&(E.files[t]=i,!i.snippets&&i.snippetText&&(i.snippets=E.parseSnippetFile(i.snippetText)),E.register(i.snippets||[],i.scope),i.includeScopes&&(E.snippetMap[i.scope].includeScopes=i.includeScopes,i.includeScopes.forEach(function(c){k("ace/mode/"+c)})))}))},l=function(t){var e=t.editor,i=e.completer&&e.completer.activated;if(t.command.name==="backspace")i&&!g.getCompletionPrefix(e)&&e.completer.detach();else if(t.command.name==="insertstring"){var c=g.getCompletionPrefix(e),s=g.triggerAutocomplete(e);if((c||s)&&!i){var o=w.for(e);o.autoShown=!0,o.showPopup(e)}}},u=f("../editor").Editor;f("../config").defineOptions(u.prototype,"editor",{enableBasicAutocompletion:{set:function(t){t?(this.completers||(this.completers=Array.isArray(t)?t:A),this.commands.addCommand(w.startCommand)):this.commands.removeCommand(w.startCommand)},value:!1},enableLiveAutocompletion:{set:function(t){t?(this.completers||(this.completers=Array.isArray(t)?t:A),this.commands.on("afterExec",l)):this.commands.removeListener("afterExec",l)},value:!1},enableSnippets:{set:function(t){t?(this.commands.addCommand(m),this.on("changeMode",y),y(null,this)):(this.commands.removeCommand(m),this.off("changeMode",y))},value:!1}})}),function(){ace.require(["ace/ext/language_tools"],function(f){z&&(z.exports=f)})}()})(Q);
