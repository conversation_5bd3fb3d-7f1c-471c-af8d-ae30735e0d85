// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SysJobLog is the golang structure of table sys_job_log for DAO operations like Where/Data.
type SysJobLog struct {
	g.Meta     `orm:"table:sys_job_log, do:true"`
	Id         interface{} // 主键
	TargetName interface{} // 方法名
	CreatedAt  *gtime.Time // 执行日期
	Result     interface{} // 执行结果
}
