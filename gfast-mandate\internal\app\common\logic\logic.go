// ==========================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package logic

import (
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/cache"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/captcha"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/eventBus"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/snowIDGen"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/sysAttachment"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/sysConfig"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/sysDictData"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/sysDictType"
	_ "github.com/tiger1103/gfast/v3/internal/app/common/logic/upload"
)
