// ================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"github.com/gogf/gf/v2/container/garray"

	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/api/v1/system"
	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/entity"
)

type (
	ISysUser interface {
		GetCasBinUserPrefix() string
		IsSupperAdmin(ctx context.Context, userId uint64) bool
		NotCheckAuthAdminIds(ctx context.Context) *gset.Set
		GetAdminUserByUsernamePassword(ctx context.Context, req *system.UserLoginReq) (user *model.LoginUserRes, err error)
		GetUserByUsername(ctx context.Context, userName string) (user *model.LoginUserRes, err error)
		GetUserByPhone(ctx context.Context, phone string) (user *model.LoginUserRes, err error)
		GetUserById(ctx context.Context, id uint64) (user *model.LoginUserRes, err error)
		LoginLog(ctx context.Context, params *model.LoginLogParams)
		UpdateLoginInfo(ctx context.Context, id uint64, ip string, openId ...string) (err error)
		GetAdminRules(ctx context.Context, userId uint64) (menuList []*model.UserMenus, permissions []string, err error)
		GetAdminRole(ctx context.Context, userId uint64, allRoleList []*entity.SysRole) (roles []*entity.SysRole, err error)
		GetAdminRoleIds(ctx context.Context, userId uint64, includeChildren ...bool) (roleIds []uint, err error)
		GetAllMenus(ctx context.Context) (menus []*model.UserMenus, err error)
		GetAdminMenusIdsByRoleIds(ctx context.Context, roleIds []uint) (menuIds *garray.Array, err error)
		GetAdminMenusByRoleIds(ctx context.Context, roleIds []uint) (menus []*model.UserMenus, err error)
		GetMenusTree(menus []*model.UserMenus, pid uint) []*model.UserMenus
		GetPermissions(ctx context.Context, roleIds []uint) (userButtons []string, err error)
		List(ctx context.Context, req *system.UserSearchReq) (total interface{}, userList []*entity.SysUser, err error)
		GetByIdsUser(ctx context.Context, req *system.UserByIdsReq) (total interface{}, userList []*entity.SysUser, err error)
		GetUsersRoleDept(ctx context.Context, userList []*entity.SysUser) (users []*model.SysUserRoleDeptRes, err error)
		Add(ctx context.Context, req *system.UserAddReq) (err error)
		Edit(ctx context.Context, req *system.UserEditReq) (err error)
		AddUserPost(ctx context.Context, tx gdb.TX, postIds []int64, userId int64) (err error)
		EditUserRole(ctx context.Context, roleIds []uint, userId int64) (err error)
		SetUserRole(ctx context.Context, roleId uint, userIds []uint64) (err error)
		UserNameOrMobileExists(ctx context.Context, userName, mobile string, id ...int64) error
		GetEditUser(ctx context.Context, id uint64) (res *system.UserGetEditRes, err error)
		GetUserInfoById(ctx context.Context, id uint64, withPwd ...bool) (user *entity.SysUser, err error)
		GetUserPostIds(ctx context.Context, userId uint64) (postIds []int64, err error)
		ResetUserPwd(ctx context.Context, req *system.UserResetPwdReq) (err error)
		ChangeUserStatus(ctx context.Context, req *system.UserStatusReq) (err error)
		Delete(ctx context.Context, ids []int) (err error)
		GetUsers(ctx context.Context, ids []interface{}) (users []*model.SysUserSimpleRes, err error)
		// Deprecated : 此方法已废弃，请使用更简单的GetAuthWhere方法或GetAuthDeptWhere方法
		GetDataWhere(ctx context.Context, userInfo *model.ContextUser, entityData interface{}, menuId uint) (where g.Map, err error)
		HasAccessByDataWhere(ctx context.Context, where g.Map, uid interface{}) bool
		AccessRule(ctx context.Context, userId uint64, rule string) bool
		GetUserSelector(ctx context.Context, req *system.UserSelectorReq) (total interface{}, userList []*model.SysUserSimpleRes, err error)
		GetUsersByRoleId(ctx context.Context, roleId uint) (users []*model.SysUserRoleDeptRes, err error)
		GetAuthWhere(ctx context.Context, userInfo *model.ContextUser, field ...string) g.Map
		GetAuthDeptWhere(ctx context.Context, userInfo *model.ContextUser, field ...string) g.Map
	}
)

var (
	localSysUser ISysUser
)

func SysUser() ISysUser {
	if localSysUser == nil {
		panic("implement not found for interface ISysUser, forgot register?")
	}
	return localSysUser
}

func RegisterSysUser(i ISysUser) {
	localSysUser = i
}
