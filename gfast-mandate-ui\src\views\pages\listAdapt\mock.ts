// 列表数据
export const filterList = [
	{
		img: 'https://news.sznews.com/pic/2021-03/09/e37326cc-4583-48f3-aa00-ecc2392d319d.jpg',
		title: '36分钟，深圳平均通勤时间出炉！GDP10强城市中仅输杭州',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 1,
	},
	{
		img: 'http://news.sznews.com/pic/2021-03/09/78cf72b6-e2d9-459d-a368-470414a027f4679cf4ea-26fa-48c8-9fee-c2d092a91400.png',
		title: '为爱而动，“红色鹊桥”三八妇女节交友联谊活动助力深圳女孩脱单',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 2,
	},
	{
		img: 'http://news.sznews.com/pic/2021-03/09/1faf3c6e-1250-4e6b-b072-4a331553e027.jpg',
		title: '粤桂协作“背水一战” 解决广西大化县3.7万人饮水难题',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 3,
	},
	{
		img: 'https://news.sznews.com/pic/2021-03/09/9fcf6dd4-1e80-4497-bdc9-83dc7246d170.jpg.2',
		title: '城镇就业女性平均薪酬6847元 女性职场渗透率提升',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 4,
	},
	{
		img: 'https://news.sznews.com/pic/2021-03/09/1bd78227-4126-4a43-bdf6-48ead6edd1bf.jpg.2',
		title: '深圳：实现“从0到1”源头创新，推进大湾区综合性国家科学中心建设！',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 5,
	},
	{
		img: 'http://news.sznews.com/pic/2021-03/08/9ea943a3-3ae8-4f49-8296-711ec36ef8c6_watermark.png',
		title: '煖声音第126期|愿你有诗酒趁年华的洒脱，也有岁月沉淀后的坚定从容',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 6,
	},
	{
		img: 'https://news.sznews.com/pic/2021-03/08/a95ba232-1422-4f7e-b85f-c61d486c8659.jpg.2',
		title: '姐妹们一起来吐槽，最不能接受男人的缺点！',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 7,
	},
	{
		img: 'http://news.sznews.com/pic/2021-03/08/76816bf0-3899-4c7e-bc6e-079b5ba8725e.jpg',
		title: '民生小事 | 手机遗落出租车 热心民警帮找回',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 8,
	},
	{
		img: 'https://news.sznews.com/pic/2021-03/08/28ed70d4-71f5-4abb-bf7b-0294bece9e43.jpg.2',
		title: '“十三五”：深圳交上靓丽答卷 发展动力加快转换',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 9,
	},
	{
		img: 'http://news.sznews.com/pic/2021-03/05/d13ae31f-fd45-431a-b48e-c5895bbc193e.png',
		title: '深圳湾公园一女子落水，三名男子接力及时施救',
		evaluate: (Math.random() * 10).toFixed(2),
		collection: (Math.random() * 100).toFixed(2),
		price: (Math.random() * 10).toFixed(2),
		monSales: (Math.random() * 20).toFixed(2),
		id: 10,
	},
];
