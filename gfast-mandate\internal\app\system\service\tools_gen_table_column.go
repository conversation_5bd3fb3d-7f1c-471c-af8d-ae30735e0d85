// ================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/system/model"
	"github.com/tiger1103/gfast/v3/internal/app/system/model/entity"
)

type (
	IToolsGenTableColumn interface {
		SelectDbTableColumnsByName(ctx context.Context, tableName string) ([]*entity.ToolsGenTableColumn, error)
		InitColumnField(column *entity.ToolsGenTableColumn, table *entity.ToolsGenTable)
		GetDbType(columnType string) string
		IsExistInArray(value string, array []string) bool
		IsStringObject(dataType string) bool
		IsTimeObject(dataType string) bool
		IsNumberObject(dataType string) bool
		IsNotEdit(name string) bool
		IsNotList(name string) bool
		IsNotDetail(name string) bool
		IsNotQuery(name string) bool
		CheckNameColumn(columnName string) bool
		CheckStatusColumn(columnName string) bool
		CheckTypeColumn(columnName string) bool
		CheckSexColumn(columnName string) bool
		SelectGenTableColumnListByTableId(ctx context.Context, tableId int64) (list []*entity.ToolsGenTableColumn, err error)
		GetAllTableColumns(ctx context.Context) (list []*entity.ToolsGenTableColumn, err error)
		SetPkColumn(table *model.ToolsGenTableEx, columns []*model.ToolsGenTableColumnEx)
		GetColumnLength(columnType string) int
		SelectDbTableColumnMapByName(ctx context.Context, tableName string) (res map[string]*entity.ToolsGenTableColumn, err error)
	}
)

var (
	localToolsGenTableColumn IToolsGenTableColumn
)

func ToolsGenTableColumn() IToolsGenTableColumn {
	if localToolsGenTableColumn == nil {
		panic("implement not found for interface IToolsGenTableColumn, forgot register?")
	}
	return localToolsGenTableColumn
}

func RegisterToolsGenTableColumn(i IToolsGenTableColumn) {
	localToolsGenTableColumn = i
}
