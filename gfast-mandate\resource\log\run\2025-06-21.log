2025-06-21T10:04:29.761+08:00 [INFO] {88028f0087ec4a1808c6d86b84f3198c} C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.5
2025-06-21T10:04:30.128+08:00 [DEBU] C:/Git/COACH/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-06-21T10:05:43.432+08:00 [INFO] {5887ac2798ec4a180bc6d86bfd8dde43} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:05:43.432+08:00 [INFO] {5887ac2798ec4a180bc6d86bfd8dde43} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:05:57.008+08:00 [INFO] {5495ee509bec4a180cc6d86b106cb796} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:05:57.008+08:00 [INFO] {5495ee509bec4a180cc6d86b106cb796} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:06:01.338+08:00 [ERRO] {fc14d7499cec4a180fc6d86bc6f1bc9f} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62BXVaTaBBGPgOxeHB [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 52 78 122 99 50 77 83 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 99 120 78 84 85 120 102 81 46 121 104 52 67 111 88 50 122 86 56 114 82 76 84 106 88 68 66 120 116 75 54 80 111 71 67 97 115 99 103 80 110 70 122 106 67 68 83 74 95 99 116 65 34 44 34 117 117 73 100 34 58 34 97 97 50 49 102 55 48 100 50 57 52 102 57 97 55 100 49 52 57 54 51 50 55 102 100 98 49 50 50 52 54 49 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:06:27.620+08:00 [INFO] {44189071a2ec4a1812c6d86b07ceb198} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:06:27.620+08:00 [INFO] {44189071a2ec4a1812c6d86b07ceb198} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:06:58.628+08:00 [INFO] {7cdabaa9a9ec4a1813c6d86b840349b7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:06:58.628+08:00 [INFO] {7cdabaa9a9ec4a1813c6d86b840349b7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:07:29.182+08:00 [INFO] {9cdb90bfb0ec4a1814c6d86b30cc434f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:07:29.222+08:00 [INFO] {9cdb90bfb0ec4a1814c6d86b30cc434f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:07:30.912+08:00 [INFO] {18900f2eb1ec4a1815c6d86bedc29ead} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:07:30.912+08:00 [INFO] {18900f2eb1ec4a1815c6d86bedc29ead} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:07:37.199+08:00 [ERRO] {9c0bc26bb2ec4a1817c6d86b6a766b5f} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62KfmveFUABQSFCmeF [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 52 78 122 103 49 78 121 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 99 120 78 106 81 51 102 81 46 88 122 121 85 89 95 48 52 115 89 50 97 65 112 69 117 76 121 86 107 77 97 67 111 51 105 121 66 85 111 102 119 97 111 97 80 107 87 69 105 85 65 48 34 44 34 117 117 73 100 34 58 34 102 102 52 53 53 98 52 57 48 102 102 98 97 102 100 55 99 102 55 99 54 99 56 57 57 57 53 100 48 54 102 98 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-21T10:08:01.470+08:00 [INFO] {3020394bb8ec4a181ac6d86be95d855b} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:08:01.470+08:00 [INFO] {3020394bb8ec4a181ac6d86be95d855b} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:08:31.483+08:00 [INFO] {f8045648bfec4a181bc6d86b2cee3665} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:08:31.483+08:00 [INFO] {f8045648bfec4a181bc6d86b2cee3665} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:09:01.688+08:00 [INFO] {f4f4b950c6ec4a181cc6d86b8d35ce0d} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:09:01.688+08:00 [INFO] {f4f4b950c6ec4a181cc6d86b8d35ce0d} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-21T10:09:32.624+08:00 [INFO] {64729b84cdec4a181dc6d86b49b62324} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-21T10:09:32.624+08:00 [INFO] {64729b84cdec4a181dc6d86b49b62324} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
