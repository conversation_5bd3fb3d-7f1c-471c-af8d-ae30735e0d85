2025-06-20T16:40:39.106+08:00 [INFO] {4c4de9a890b34a1851e1c941485c236c} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:40:39.119+08:00 [INFO] {4c4de9a890b34a1851e1c941485c236c} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:41:10.631+08:00 [INFO] {e092b40198b34a1855e1c9410b7451ff} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:41:10.631+08:00 [INFO] {e092b40198b34a1855e1c9410b7451ff} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:41:34.303+08:00 [ERRO] {04ae52df91b34a1854e1c94146d84d58} C:/Git/COACH/gfast-mandate/library/liberr/err.go:21: SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE `user_name`='demo' LIMIT 1: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
    C:/Git/COACH/gfast-mandate/library/liberr/err.go:21
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:98
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
5.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
6.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:41.634+08:00 [INFO] {c46f96399fb34a1858e1c9415470ce11} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:41:41.634+08:00 [INFO] {c46f96399fb34a1858e1c9415470ce11} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:42:12.631+08:00 [INFO] {9c542c71a6b34a1859e1c941e4061048} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:42:12.631+08:00 [INFO] {9c542c71a6b34a1859e1c941e4061048} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:42:43.632+08:00 [INFO] {d077f5a8adb34a185ae1c941162ba0d1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:42:43.632+08:00 [INFO] {d077f5a8adb34a185ae1c941162ba0d1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:42:58.406+08:00 [ERRO] {f41db0849db34a1857e1c941a9982833} C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:138: SHOW FULL COLUMNS FROM `sys_login_log`: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).LoginLog
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:138
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog.(*sSysLoginLog).Invoke.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysLoginLog/sys_login_log.go:42

2025-06-20T16:43:14.642+08:00 [INFO] {0cb41fe1b4b34a185be1c941f9bd68af} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:43:14.642+08:00 [INFO] {0cb41fe1b4b34a185be1c941f9bd68af} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:43:45.632+08:00 [INFO] {4c737318bcb34a185ce1c9418bc59cb7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:43:45.632+08:00 [INFO] {4c737318bcb34a185ce1c9418bc59cb7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:44:16.634+08:00 [INFO] {30034d50c3b34a185de1c94122d57e24} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:44:16.634+08:00 [INFO] {30034d50c3b34a185de1c94122d57e24} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:44:47.631+08:00 [INFO] {c040eb87cab34a185ee1c941d995bc7e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:44:47.631+08:00 [INFO] {c040eb87cab34a185ee1c941d995bc7e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:45:18.633+08:00 [INFO] {14f5c6bfd1b34a185fe1c941a37e2b15} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:45:18.634+08:00 [INFO] {14f5c6bfd1b34a185fe1c941a37e2b15} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:45:49.632+08:00 [INFO] {3c4164f7d8b34a1860e1c941616bd1d8} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:45:49.632+08:00 [INFO] {3c4164f7d8b34a1860e1c941616bd1d8} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:46:20.631+08:00 [INFO] {2400242fe0b34a1861e1c9418cfdb894} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:46:20.631+08:00 [INFO] {2400242fe0b34a1861e1c9418cfdb894} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:46:51.633+08:00 [INFO] {54def966e7b34a1862e1c9416781ecaa} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:46:51.633+08:00 [INFO] {54def966e7b34a1862e1c9416781ecaa} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:47:22.631+08:00 [INFO] {88f0979eeeb34a1863e1c941a7ba40d4} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:47:22.631+08:00 [INFO] {88f0979eeeb34a1863e1c941a7ba40d4} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:47:53.632+08:00 [INFO] {20065bd6f5b34a1864e1c941d7719986} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:47:53.632+08:00 [INFO] {20065bd6f5b34a1864e1c941d7719986} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:48:24.632+08:00 [INFO] {ac2d2c0efdb34a1865e1c941750c0dbe} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:48:24.632+08:00 [INFO] {ac2d2c0efdb34a1865e1c941750c0dbe} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:48:55.632+08:00 [INFO] {7804e64504b44a1866e1c941750bce61} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:48:55.632+08:00 [INFO] {7804e64504b44a1866e1c941750bce61} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:49:26.633+08:00 [INFO] {94d4b07d0bb44a1867e1c9414bd6844a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:49:26.633+08:00 [INFO] {94d4b07d0bb44a1867e1c9414bd6844a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:49:57.632+08:00 [INFO] {a8c364b512b44a1868e1c941c8f4c741} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:49:57.632+08:00 [INFO] {a8c364b512b44a1868e1c941c8f4c741} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:50:28.632+08:00 [INFO] {9cd227ed19b44a1869e1c9414477dd50} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:50:28.632+08:00 [INFO] {9cd227ed19b44a1869e1c9414477dd50} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:50:59.632+08:00 [INFO] {28cede2421b44a186ae1c94158ace1bc} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:50:59.632+08:00 [INFO] {28cede2421b44a186ae1c94158ace1bc} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:51:30.632+08:00 [INFO] {0464a15c28b44a186be1c9414b0593fd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:51:30.632+08:00 [INFO] {0464a15c28b44a186be1c9414b0593fd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:52:01.633+08:00 [INFO] {001768942fb44a186ce1c941a46878ac} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:52:01.633+08:00 [INFO] {001768942fb44a186ce1c941a46878ac} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:52:32.631+08:00 [INFO] {043a11cc36b44a186de1c941b8d47ea1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:52:32.631+08:00 [INFO] {043a11cc36b44a186de1c941b8d47ea1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:53:03.632+08:00 [INFO] {18f7dd033eb44a186ee1c94105c87392} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:53:03.632+08:00 [INFO] {18f7dd033eb44a186ee1c94105c87392} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:53:34.633+08:00 [INFO] {704daa3b45b44a186fe1c941f0be77ed} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:53:34.634+08:00 [INFO] {704daa3b45b44a186fe1c941f0be77ed} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:54:05.636+08:00 [INFO] {dced90734cb44a1870e1c9418b53ed3e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:54:05.636+08:00 [INFO] {dced90734cb44a1870e1c9418b53ed3e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:54:36.632+08:00 [INFO] {14ea13ab53b44a1871e1c9416b89e2e1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:54:36.632+08:00 [INFO] {14ea13ab53b44a1871e1c9416b89e2e1} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:55:07.632+08:00 [INFO] {0c48d9e25ab44a1872e1c9413b04ed8a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:55:07.632+08:00 [INFO] {0c48d9e25ab44a1872e1c9413b04ed8a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:55:38.632+08:00 [INFO] {b886961a62b44a1873e1c9412bd9c3f6} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:55:38.632+08:00 [INFO] {b886961a62b44a1873e1c9412bd9c3f6} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:56:09.631+08:00 [INFO] {b8a8495269b44a1874e1c94194bc1b89} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:56:09.631+08:00 [INFO] {b8a8495269b44a1874e1c94194bc1b89} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:56:40.632+08:00 [INFO] {88cf098a70b44a1875e1c941c06afbb0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:56:40.632+08:00 [INFO] {88cf098a70b44a1875e1c941c06afbb0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:57:11.631+08:00 [INFO] {d801c6c177b44a1876e1c941fee31384} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:57:11.632+08:00 [INFO] {d801c6c177b44a1876e1c941fee31384} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:57:42.633+08:00 [INFO] {7c6ca0f97eb44a1877e1c941ce8457dd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:57:42.633+08:00 [INFO] {7c6ca0f97eb44a1877e1c941ce8457dd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:58:13.631+08:00 [INFO] {50f13a3186b44a1878e1c9417424d9a9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:58:13.631+08:00 [INFO] {50f13a3186b44a1878e1c9417424d9a9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:58:44.632+08:00 [INFO] {ec93fe688db44a1879e1c941aa1ce0bf} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:58:44.632+08:00 [INFO] {ec93fe688db44a1879e1c941aa1ce0bf} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:59:15.631+08:00 [INFO] {f41bb7a094b44a187ae1c941e73ae909} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:59:15.631+08:00 [INFO] {f41bb7a094b44a187ae1c941e73ae909} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T16:59:46.631+08:00 [INFO] {84957dd89bb44a187be1c94152c50504} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T16:59:46.631+08:00 [INFO] {84957dd89bb44a187be1c94152c50504} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:00:17.633+08:00 [INFO] {08a65910a3b44a187ce1c94184608b7a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:00:17.633+08:00 [INFO] {08a65910a3b44a187ce1c94184608b7a} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:00:48.633+08:00 [INFO] {64bb0d48aab44a187de1c9412dc9ca29} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:00:48.633+08:00 [INFO] {64bb0d48aab44a187de1c9412dc9ca29} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:01:19.631+08:00 [INFO] {c8c2b87fb1b44a187ee1c941d7f373f0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:01:19.631+08:00 [INFO] {c8c2b87fb1b44a187ee1c941d7f373f0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:01:52.202+08:00 [INFO] {e0971a15b9b44a187fe1c941c9f0ce2f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:01:52.202+08:00 [INFO] {e0971a15b9b44a187fe1c941c9f0ce2f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:02:18.936+08:00 [ERRO] {5867b1d7bdb44a1880e1c94108c607ae} C:/Git/COACH/gfast-mandate/library/liberr/err.go:21: SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE `user_name`='demo' LIMIT 1: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/library/liberr.ErrIsNil
    C:/Git/COACH/gfast-mandate/library/liberr/err.go:21
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:98
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
5.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
6.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
7.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:02:19.888+08:00 [INFO] {30b74687bfb44a1882e1c9419d1ec9b9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:02:19.888+08:00 [INFO] {30b74687bfb44a1882e1c9419d1ec9b9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:03:28.119+08:00 [INFO] {442a226acfb44a185c1637780299ee22} C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.5
2025-06-20T17:03:28.279+08:00 [DEBU] C:/Git/COACH/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-06-20T17:03:43.813+08:00 [INFO] {70d49611d3b44a185f163778f5cd20cd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:03:43.813+08:00 [INFO] {70d49611d3b44a185f163778f5cd20cd} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:03:46.156+08:00 [ERRO] {34bf369ad3b44a18611637784e546989} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62nbNKqDoYLSYWTNzH [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 81 121 78 105 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 106 69 50 102 81 46 101 80 118 75 74 88 79 103 114 87 119 55 87 116 83 109 73 54 57 84 107 70 48 74 118 102 74 107 113 51 119 117 119 67 76 78 119 73 81 69 120 84 85 34 44 34 117 117 73 100 34 58 34 55 101 56 101 101 57 50 55 55 99 52 102 51 50 98 56 48 49 98 99 101 53 48 99 51 53 55 99 100 99 98 49 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:03:54.406+08:00 [ERRO] {7898ab88d5b44a186416377892d73d82} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62FIrJhYHGlxHNgXXh [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 81 122 78 67 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 106 73 48 102 81 46 48 117 73 97 53 56 74 104 71 101 107 114 119 82 75 119 106 103 81 108 84 81 78 84 74 119 99 98 85 79 103 102 105 77 53 116 76 114 107 112 55 105 103 34 44 34 117 117 73 100 34 58 34 97 48 51 57 55 49 98 49 99 97 54 50 49 55 101 48 98 48 49 102 102 55 100 53 52 52 98 48 98 51 51 102 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:04:13.852+08:00 [INFO] {34a41610dab44a18671637785f4da533} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:04:13.852+08:00 [INFO] {34a41610dab44a18671637785f4da533} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:04:16.758+08:00 [INFO] {608551bddab44a1868163778cf6ac2ad} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:04:16.758+08:00 [INFO] {608551bddab44a1868163778cf6ac2ad} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:04:19.663+08:00 [ERRO] {0c013467dbb44a186a1637784ce3de46} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62IhoCYGAyrCCqeDbB [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 81 49 79 83 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 106 81 53 102 81 46 45 101 119 69 119 103 89 95 119 52 109 112 79 84 117 108 105 82 85 54 95 50 89 119 119 101 78 77 49 120 69 103 53 54 85 99 51 73 102 74 108 66 103 34 44 34 117 117 73 100 34 58 34 54 51 97 99 57 49 52 49 97 57 97 56 51 101 57 100 50 97 50 51 101 99 102 50 55 51 55 98 99 101 102 57 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:04:39.354+08:00 [INFO] {24a21900e0b44a1885a1550e9e880605} C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.5
2025-06-20T17:04:39.405+08:00 [DEBU] C:/Git/COACH/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
2025-06-20T17:04:47.242+08:00 [INFO] {747442d6e1b44a1888a1550e44603646} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:04:47.242+08:00 [INFO] {747442d6e1b44a1888a1550e44603646} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:05:17.248+08:00 [INFO] {44ffbbd2e8b44a1889a1550e7d0500d4} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:05:17.248+08:00 [INFO] {44ffbbd2e8b44a1889a1550e7d0500d4} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:05:18.012+08:00 [ERRO] {88848ffee8b44a188aa1550e30b40d5b} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62AdczLAjZbSYSrczl [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 85 120 79 67 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 122 65 52 102 81 46 68 75 113 54 98 116 90 69 78 50 68 95 99 52 102 106 54 54 81 52 97 45 99 85 83 100 122 77 79 103 74 68 86 104 102 52 73 116 99 119 81 121 103 34 44 34 117 117 73 100 34 58 34 53 54 100 97 97 101 102 54 99 100 56 53 57 99 100 98 101 56 101 49 48 49 52 97 49 51 56 101 98 55 97 101 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:38.150+08:00 [ERRO] {146233b0edb44a188da1550e5c3d0bee} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62dakAMmwmeyVUiEea [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 85 122 79 67 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 122 73 52 102 81 46 51 74 66 83 119 52 111 104 85 101 115 112 73 51 98 45 90 115 99 75 104 98 45 85 50 114 80 86 83 78 55 78 56 97 81 109 86 117 69 73 116 101 77 34 44 34 117 117 73 100 34 58 34 99 102 55 97 100 102 100 101 99 97 97 56 55 49 51 99 99 50 101 99 49 57 57 52 57 99 48 53 56 97 51 53 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:39.857+08:00 [ERRO] {8089d213eeb44a1890a1550e4f6b8a87} C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98: Redis Client Do failed with arguments "[Set gfToken:31-fe01ce2a7fbac8fafaed7c982a04e2294f448b1eb2227c62sbkRXBbGZmdRqFMc [123 34 106 119 116 84 111 107 101 110 34 58 34 101 121 74 104 98 71 99 105 79 105 74 73 85 122 73 49 78 105 73 115 73 110 82 53 99 67 73 54 73 107 112 88 86 67 74 57 46 101 121 74 69 89 88 82 104 73 106 112 55 73 109 108 107 73 106 111 122 77 83 119 105 100 88 78 108 99 107 53 104 98 87 85 105 79 105 74 107 90 87 49 118 73 105 119 105 98 87 57 105 97 87 120 108 73 106 111 105 77 84 85 122 77 122 81 48 78 84 85 51 79 68 107 105 76 67 74 49 99 50 86 121 84 109 108 106 97 50 53 104 98 87 85 105 79 105 76 109 110 89 55 108 109 53 115 105 76 67 74 49 99 50 86 121 85 51 82 104 100 72 86 122 73 106 111 120 76 67 74 112 99 48 70 107 98 87 108 117 73 106 111 120 76 67 74 104 100 109 70 48 89 88 73 105 79 105 74 49 99 71 120 118 89 87 82 102 90 109 108 115 90 83 56 121 77 68 73 48 76 84 65 51 76 84 73 53 76 50 81 122 77 87 120 122 99 109 73 50 98 122 78 121 90 50 49 121 90 71 108 112 97 67 53 113 99 71 99 105 76 67 74 107 90 88 66 48 83 87 81 105 79 106 69 119 79 88 48 115 73 109 86 52 99 67 73 54 77 84 99 49 77 68 81 121 78 106 85 122 79 83 119 105 98 109 74 109 73 106 111 120 78 122 85 119 78 68 69 119 77 122 73 53 102 81 46 50 121 66 113 89 72 100 102 69 109 53 102 87 121 98 102 103 86 122 122 52 87 49 98 81 76 102 110 78 108 113 82 106 85 117 45 50 87 121 119 111 103 48 34 44 34 117 117 73 100 34 58 34 100 99 53 102 54 53 55 99 49 55 50 54 50 97 101 99 97 51 100 98 52 57 50 53 54 48 97 50 52 54 97 49 34 125] PX 16200000]": dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:98
2.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:05:47.252+08:00 [INFO] {9ccc29cfefb44a1893a1550e3a2c07ba} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:05:47.252+08:00 [INFO] {9ccc29cfefb44a1893a1550e3a2c07ba} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:06:17.631+08:00 [INFO] {c49ee8e1f6b44a1894a1550eba7431e7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:06:17.631+08:00 [INFO] {c49ee8e1f6b44a1894a1550eba7431e7} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:06:48.632+08:00 [INFO] {c0a8ad19feb44a1895a1550e76279e62} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:06:48.632+08:00 [INFO] {c0a8ad19feb44a1895a1550e76279e62} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:07:19.631+08:00 [INFO] {b4cd645105b54a1896a1550e949824a9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:07:19.632+08:00 [INFO] {b4cd645105b54a1896a1550e949824a9} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:07:50.632+08:00 [INFO] {b0e42b890cb54a1897a1550e67bcba6e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:07:50.632+08:00 [INFO] {b0e42b890cb54a1897a1550e67bcba6e} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:08:21.632+08:00 [INFO] {2c7ce8c013b54a1898a1550e9965a04f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:08:21.632+08:00 [INFO] {2c7ce8c013b54a1898a1550e9965a04f} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:08:52.634+08:00 [INFO] {0ce9c0f81ab54a1899a1550e9725e403} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:08:52.634+08:00 [INFO] {0ce9c0f81ab54a1899a1550e9725e403} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:09:23.632+08:00 [INFO] {60a3693022b54a189aa1550e783f7430} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:09:23.632+08:00 [INFO] {60a3693022b54a189aa1550e783f7430} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:09:54.631+08:00 [INFO] {dc850d6829b54a189ba1550e0633a840} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:09:54.631+08:00 [INFO] {dc850d6829b54a189ba1550e0633a840} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
2025-06-20T17:10:25.632+08:00 [INFO] {84bbe69f30b54a189ca1550ef6022fd0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:215: [GFToken]decode error Token: undefined base64.StdEncoding.Decode failed: illegal base64 data at input byte 8
2025-06-20T17:10:25.632+08:00 [INFO] {84bbe69f30b54a189ca1550ef6022fd0} C:/Users/<USER>/go/pkg/mod/github.com/tiger1103/gfast-token@v1.0.9/gftoken/gftoken.go:108: decode error
