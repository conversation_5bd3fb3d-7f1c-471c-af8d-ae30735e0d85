import{aF as Da,D as _u,a6 as Ep,a5 as sn,ab as qr,aa as ti,am as wp,bW as xp,bX as Tp,bY as Cp}from"./doc-30bb18f4.js";import{c as Dp}from"./createText-b670c180-011fa2f3.js";var Uu={exports:{}};(function(pe,le){(function(ee,ce){pe.exports=ce()})(Da,function(){function ee(t){"@babel/helpers - typeof";return ee=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ee(t)}function ce(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function H(t,e){for(var r=0;r<e.length;r++){var a=e[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,a.key,a)}}function O(t,e,r){return e&&H(t.prototype,e),r&&H(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function T(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t,e){return S(t)||G(t,e)||U(t,e)||K()}function S(t){if(Array.isArray(t))return t}function G(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var a=[],n=!0,i=!1,s,o;try{for(r=r.call(t);!(n=(s=r.next()).done)&&(a.push(s.value),!(e&&a.length===e));n=!0);}catch(l){i=!0,o=l}finally{try{!n&&r.return!=null&&r.return()}finally{if(i)throw o}}return a}}function U(t,e){if(t){if(typeof t=="string")return P(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return P(t,e)}}function P(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,a=new Array(e);r<e;r++)a[r]=t[r];return a}function K(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var D=typeof window>"u"?null:window,V=D?D.navigator:null;D&&D.document;var _=ee(""),Q=ee({}),ne=ee(function(){}),oe=typeof HTMLElement>"u"?"undefined":ee(HTMLElement),J=function(e){return e&&e.instanceString&&Y(e.instanceString)?e.instanceString():null},j=function(e){return e!=null&&ee(e)==_},Y=function(e){return e!=null&&ee(e)===ne},te=function(e){return!de(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},L=function(e){return e!=null&&ee(e)===Q&&!te(e)&&e.constructor===Object},$=function(e){return e!=null&&ee(e)===Q},R=function(e){return e!=null&&ee(e)===ee(1)&&!isNaN(e)},W=function(e){return R(e)&&Math.floor(e)===e},fe=function(e){if(oe!=="undefined")return e!=null&&e instanceof HTMLElement},de=function(e){return Ae(e)||Ne(e)},Ae=function(e){return J(e)==="collection"&&e._private.single},Ne=function(e){return J(e)==="collection"&&!e._private.single},_e=function(e){return J(e)==="core"},tt=function(e){return J(e)==="stylesheet"},vt=function(e){return J(e)==="event"},Pe=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},$e=function(e){return typeof HTMLElement>"u"?!1:e instanceof HTMLElement},Xe=function(e){return L(e)&&R(e.x1)&&R(e.x2)&&R(e.y1)&&R(e.y2)},rt=function(e){return $(e)&&Y(e.then)},lt=function(){return V&&V.userAgent.match(/msie|trident|edge/i)},at=function(e,r){r||(r=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function n(){var i=this,s=arguments,o,l=r.apply(i,s),u=n.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},et=at(function(t){return t.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),yt=at(function(t){return t.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),wt=at(function(t,e){return t+e[0].toUpperCase()+e.substring(1)},function(t,e){return t+"$"+e}),Pt=function(e){return Pe(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},dt="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",dr="rgb[a]?\\(("+dt+"[%]?)\\s*,\\s*("+dt+"[%]?)\\s*,\\s*("+dt+"[%]?)(?:\\s*,\\s*("+dt+"))?\\)",qt="rgb[a]?\\((?:"+dt+"[%]?)\\s*,\\s*(?:"+dt+"[%]?)\\s*,\\s*(?:"+dt+"[%]?)(?:\\s*,\\s*(?:"+dt+"))?\\)",un="hsl[a]?\\(("+dt+")\\s*,\\s*("+dt+"[%])\\s*,\\s*("+dt+"[%])(?:\\s*,\\s*("+dt+"))?\\)",Zr="hsl[a]?\\((?:"+dt+")\\s*,\\s*(?:"+dt+"[%])\\s*,\\s*(?:"+dt+"[%])(?:\\s*,\\s*(?:"+dt+"))?\\)",Sa="\\#[0-9a-fA-F]{3}",er="\\#[0-9a-fA-F]{6}",ji=function(e,r){return e<r?-1:e>r?1:0},ju=function(e,r){return-1*ji(e,r)},Ue=Object.assign!=null?Object.assign.bind(Object):function(t){for(var e=arguments,r=1;r<e.length;r++){var a=e[r];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];t[s]=a[s]}}return t},el=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var r=e.length===4,a,n,i,s=16;return r?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},tl=function(e){var r,a,n,i,s,o,l,u;function f(v,p,y){return y<0&&(y+=1),y>1&&(y-=1),y<1/6?v+(p-v)*6*y:y<1/2?p:y<2/3?v+(p-v)*(2/3-y)*6:v}var h=new RegExp("^"+un+"$").exec(e);if(h){if(a=parseInt(h[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(h[2]),n<0||n>100||(n=n/100,i=parseFloat(h[3]),i<0||i>100)||(i=i/100,s=h[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,d=2*i-c;o=Math.round(255*f(d,c,a+1/3)),l=Math.round(255*f(d,c,a)),u=Math.round(255*f(d,c,a-1/3))}r=[o,l,u,s]}return r},rl=function(e){var r,a=new RegExp("^"+dr+"$").exec(e);if(a){r=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;r.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;r.push(u)}}return r},al=function(e){return il[e.toLowerCase()]},nl=function(e){return(te(e)?e:null)||al(e)||el(e)||rl(e)||tl(e)},il={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},es=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(L(s))throw Error("Tried to set map with object key");i<a.length-1?(r[s]==null&&(r[s]={}),r=r[s]):r[s]=e.value}},ts=function(e){for(var r=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(L(s))throw Error("Tried to get map with object key");if(r=r[s],r==null)return r}return r};function sl(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}var kr=sl,La=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof Da<"u"?Da:typeof self<"u"?self:{};function ol(t,e){return e={exports:{}},t(e,e.exports),e.exports}var ul=typeof La=="object"&&La&&La.Object===Object&&La,ll=ul,fl=typeof self=="object"&&self&&self.Object===Object&&self,hl=ll||fl||Function("return this")(),ln=hl,cl=function(){return ln.Date.now()},ri=cl,vl=/\s/;function dl(t){for(var e=t.length;e--&&vl.test(t.charAt(e)););return e}var gl=dl,pl=/^\s+/;function yl(t){return t&&t.slice(0,gl(t)+1).replace(pl,"")}var ml=yl,bl=ln.Symbol,Qr=bl,rs=Object.prototype,El=rs.hasOwnProperty,wl=rs.toString,Aa=Qr?Qr.toStringTag:void 0;function xl(t){var e=El.call(t,Aa),r=t[Aa];try{t[Aa]=void 0;var a=!0}catch{}var n=wl.call(t);return a&&(e?t[Aa]=r:delete t[Aa]),n}var Tl=xl,Cl=Object.prototype,Dl=Cl.toString;function Sl(t){return Dl.call(t)}var Ll=Sl,Al="[object Null]",Ol="[object Undefined]",as=Qr?Qr.toStringTag:void 0;function Nl(t){return t==null?t===void 0?Ol:Al:as&&as in Object(t)?Tl(t):Ll(t)}var ns=Nl;function Ml(t){return t!=null&&typeof t=="object"}var Il=Ml,Rl="[object Symbol]";function kl(t){return typeof t=="symbol"||Il(t)&&ns(t)==Rl}var Oa=kl,is=0/0,Pl=/^[-+]0x[0-9a-f]+$/i,Bl=/^0b[01]+$/i,Fl=/^0o[0-7]+$/i,zl=parseInt;function Gl(t){if(typeof t=="number")return t;if(Oa(t))return is;if(kr(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=kr(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=ml(t);var r=Bl.test(t);return r||Fl.test(t)?zl(t.slice(2),r?2:8):Pl.test(t)?is:+t}var ss=Gl,$l="Expected a function",Vl=Math.max,_l=Math.min;function Ul(t,e,r){var a,n,i,s,o,l,u=0,f=!1,h=!1,c=!0;if(typeof t!="function")throw new TypeError($l);e=ss(e)||0,kr(r)&&(f=!!r.leading,h="maxWait"in r,i=h?Vl(ss(r.maxWait)||0,e):i,c="trailing"in r?!!r.trailing:c);function d(A){var x=a,I=n;return a=n=void 0,u=A,s=t.apply(I,x),s}function v(A){return u=A,o=setTimeout(g,e),f?d(A):s}function p(A){var x=A-l,I=A-u,C=e-x;return h?_l(C,i-I):C}function y(A){var x=A-l,I=A-u;return l===void 0||x>=e||x<0||h&&I>=i}function g(){var A=ri();if(y(A))return m(A);o=setTimeout(g,p(A))}function m(A){return o=void 0,c&&a?d(A):(a=n=void 0,s)}function b(){o!==void 0&&clearTimeout(o),u=0,a=l=n=o=void 0}function E(){return o===void 0?s:m(ri())}function N(){var A=ri(),x=y(A);if(a=arguments,n=this,l=A,x){if(o===void 0)return v(l);if(h)return clearTimeout(o),o=setTimeout(g,e),d(l)}return o===void 0&&(o=setTimeout(g,e)),s}return N.cancel=b,N.flush=E,N}var fn=Ul,ai=D?D.performance:null,os=ai&&ai.now?function(){return ai.now()}:function(){return Date.now()},Yl=function(){if(D){if(D.requestAnimationFrame)return function(t){D.requestAnimationFrame(t)};if(D.mozRequestAnimationFrame)return function(t){D.mozRequestAnimationFrame(t)};if(D.webkitRequestAnimationFrame)return function(t){D.webkitRequestAnimationFrame(t)};if(D.msRequestAnimationFrame)return function(t){D.msRequestAnimationFrame(t)}}return function(t){t&&setTimeout(function(){t(os())},1e3/60)}}(),hn=function(e){return Yl(e)},gr=os,Jr=9261,us=65599,Na=5381,ls=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Jr,a=r,n;n=e.next(),!n.done;)a=a*us+n.value|0;return a},Ma=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Jr;return r*us+e|0},Ia=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Na;return(r<<5)+r+e|0},Hl=function(e,r){return e*2097152+r},wr=function(e){return e[0]*2097152+e[1]},cn=function(e,r){return[Ma(e[0],r[0]),Ia(e[1],r[1])]},Xl=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return ls(s,r)},Pr=function(e,r){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return ls(s,r)},fs=function(){return Wl(arguments)},Wl=function(e){for(var r,a=0;a<e.length;a++){var n=e[a];a===0?r=Pr(n):r=Pr(n,r)}return r},hs=!0,ql=console.warn!=null,Kl=console.trace!=null,ni=Number.MAX_SAFE_INTEGER||9007199254740991,cs=function(){return!0},vn=function(){return!1},vs=function(){return 0},ii=function(){},Tt=function(e){throw new Error(e)},ds=function(e){if(e!==void 0)hs=!!e;else return hs},ft=function(e){ds()&&(ql?console.warn(e):(console.log(e),Kl&&console.trace()))},Zl=function(e){return Ue({},e)},ur=function(e){return e==null?e:te(e)?e.slice():L(e)?Zl(e):e},Ql=function(e){return e.slice()},gs=function(e,r){for(r=e="";e++<36;r+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return r},Jl={},ps=function(){return Jl},At=function(e){var r=Object.keys(e);return function(a){for(var n={},i=0;i<r.length;i++){var s=r[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},xr=function(e,r,a){for(var n=e.length-1;n>=0&&!(e[n]===r&&(e.splice(n,1),a));n--);},si=function(e){e.splice(0,e.length)},jl=function(e,r){for(var a=0;a<r.length;a++){var n=r[a];e.push(n)}},tr=function(e,r,a){return a&&(r=wt(a,r)),e[r]},Tr=function(e,r,a,n){a&&(r=wt(a,r)),e[r]=n},ef=function(){function t(){ce(this,t),this._obj={}}return O(t,[{key:"set",value:function(r,a){return this._obj[r]=a,this}},{key:"delete",value:function(r){return this._obj[r]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(r){return this._obj[r]!==void 0}},{key:"get",value:function(r){return this._obj[r]}}]),t}(),lr=typeof Map<"u"?Map:ef,tf="undefined",rf=function(){function t(e){if(ce(this,t),this._obj=Object.create(null),this.size=0,e!=null){var r;e.instanceString!=null&&e.instanceString()===this.instanceString()?r=e.toArray():r=e;for(var a=0;a<r.length;a++)this.add(r[a])}}return O(t,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(r){var a=this._obj;a[r]!==1&&(a[r]=1,this.size++)}},{key:"delete",value:function(r){var a=this._obj;a[r]===1&&(a[r]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(r){return this._obj[r]===1}},{key:"toArray",value:function(){var r=this;return Object.keys(this._obj).filter(function(a){return r.has(a)})}},{key:"forEach",value:function(r,a){return this.toArray().forEach(r,a)}}]),t}(),jr=(typeof Set>"u"?"undefined":ee(Set))!==tf?Set:rf,dn=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||r===void 0||!_e(e)){Tt("An element must have a core reference and parameters set");return}var n=r.group;if(n==null&&(r.data&&r.data.source!=null&&r.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){Tt("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:r.data||{},position:r.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!r.selected,selectable:r.selectable===void 0?!0:!!r.selectable,locked:!!r.locked,grabbed:!1,grabbable:r.grabbable===void 0?!0:!!r.grabbable,pannable:r.pannable===void 0?n==="edges":!!r.pannable,active:!1,classes:new jr,animation:{current:[],queue:[]},rscratch:{},scratch:r.scratch||{},edges:[],children:[],parent:r.parent&&r.parent.isNode()?r.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),r.renderedPosition){var s=r.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];te(r.classes)?u=r.classes:j(r.classes)&&(u=r.classes.split(/\s+/));for(var f=0,h=u.length;f<h;f++){var c=u[f];!c||c===""||i.classes.add(c)}this.createEmitter();var d=r.style||r.css;d&&(ft("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(d)),(a===void 0||a)&&this.restore()},ys=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;L(a)&&!de(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!Y(n)?n:i,n=Y(n)?n:function(){};for(var o=this._private.cy,l=a=j(a)?this.filter(a):a,u=[],f=[],h={},c={},d={},v=0,p,y=this.byGroup(),g=y.nodes,m=y.edges,b=0;b<l.length;b++){var E=l[b],N=E.id();E.isNode()&&(u.unshift(E),e.bfs&&(d[N]=!0,f.push(E)),c[N]=0)}for(var A=function(){var X=e.bfs?u.shift():u.pop(),B=X.id();if(e.dfs){if(d[B])return"continue";d[B]=!0,f.push(X)}var re=c[B],q=h[B],Z=q!=null?q.source():null,ie=q!=null?q.target():null,ue=q==null?void 0:X.same(Z)?ie[0]:Z[0],ge=void 0;if(ge=n(X,q,ue,v++,re),ge===!0)return p=X,"break";if(ge===!1)return"break";for(var se=X.connectedEdges().filter(function(me){return(!i||me.source().same(X))&&m.has(me)}),ve=0;ve<se.length;ve++){var ye=se[ve],Te=ye.connectedNodes().filter(function(me){return!me.same(X)&&g.has(me)}),be=Te.id();Te.length!==0&&!d[be]&&(Te=Te[0],u.push(Te),e.bfs&&(d[be]=!0,f.push(Te)),h[be]=ye,c[be]=c[B]+1)}};u.length!==0;){var x=A();if(x!=="continue"&&x==="break")break}for(var I=o.collection(),C=0;C<f.length;C++){var F=f[C],z=h[F.id()];z!=null&&I.push(z),I.push(F)}return{path:o.collection(I),found:o.collection(p)}}},Ra={breadthFirstSearch:ys({bfs:!0}),depthFirstSearch:ys({dfs:!0})};Ra.bfs=Ra.breadthFirstSearch,Ra.dfs=Ra.depthFirstSearch;var af=ol(function(t,e){(function(){var r,a,n,i,s,o,l,u,f,h,c,d,v,p,y;n=Math.floor,h=Math.min,a=function(g,m){return g<m?-1:g>m?1:0},f=function(g,m,b,E,N){var A;if(b==null&&(b=0),N==null&&(N=a),b<0)throw new Error("lo must be non-negative");for(E==null&&(E=g.length);b<E;)A=n((b+E)/2),N(m,g[A])<0?E=A:b=A+1;return[].splice.apply(g,[b,b-b].concat(m)),m},o=function(g,m,b){return b==null&&(b=a),g.push(m),p(g,0,g.length-1,b)},s=function(g,m){var b,E;return m==null&&(m=a),b=g.pop(),g.length?(E=g[0],g[0]=b,y(g,0,m)):E=b,E},u=function(g,m,b){var E;return b==null&&(b=a),E=g[0],g[0]=m,y(g,0,b),E},l=function(g,m,b){var E;return b==null&&(b=a),g.length&&b(g[0],m)<0&&(E=[g[0],m],m=E[0],g[0]=E[1],y(g,0,b)),m},i=function(g,m){var b,E,N,A,x,I;for(m==null&&(m=a),A=(function(){I=[];for(var C=0,F=n(g.length/2);0<=F?C<F:C>F;0<=F?C++:C--)I.push(C);return I}).apply(this).reverse(),x=[],E=0,N=A.length;E<N;E++)b=A[E],x.push(y(g,b,m));return x},v=function(g,m,b){var E;if(b==null&&(b=a),E=g.indexOf(m),E!==-1)return p(g,0,E,b),y(g,E,b)},c=function(g,m,b){var E,N,A,x,I;if(b==null&&(b=a),N=g.slice(0,m),!N.length)return N;for(i(N,b),I=g.slice(m),A=0,x=I.length;A<x;A++)E=I[A],l(N,E,b);return N.sort(b).reverse()},d=function(g,m,b){var E,N,A,x,I,C,F,z,M;if(b==null&&(b=a),m*10<=g.length){if(A=g.slice(0,m).sort(b),!A.length)return A;for(N=A[A.length-1],F=g.slice(m),x=0,C=F.length;x<C;x++)E=F[x],b(E,N)<0&&(f(A,E,0,null,b),A.pop(),N=A[A.length-1]);return A}for(i(g,b),M=[],I=0,z=h(m,g.length);0<=z?I<z:I>z;0<=z?++I:--I)M.push(s(g,b));return M},p=function(g,m,b,E){var N,A,x;for(E==null&&(E=a),N=g[b];b>m;){if(x=b-1>>1,A=g[x],E(N,A)<0){g[b]=A,b=x;continue}break}return g[b]=N},y=function(g,m,b){var E,N,A,x,I;for(b==null&&(b=a),N=g.length,I=m,A=g[m],E=2*m+1;E<N;)x=E+1,x<N&&!(b(g[E],g[x])<0)&&(E=x),g[m]=g[E],m=E,E=2*m+1;return g[m]=A,p(g,I,m,b)},r=function(){g.push=o,g.pop=s,g.replace=u,g.pushpop=l,g.heapify=i,g.updateItem=v,g.nlargest=c,g.nsmallest=d;function g(m){this.cmp=m??a,this.nodes=[]}return g.prototype.push=function(m){return o(this.nodes,m,this.cmp)},g.prototype.pop=function(){return s(this.nodes,this.cmp)},g.prototype.peek=function(){return this.nodes[0]},g.prototype.contains=function(m){return this.nodes.indexOf(m)!==-1},g.prototype.replace=function(m){return u(this.nodes,m,this.cmp)},g.prototype.pushpop=function(m){return l(this.nodes,m,this.cmp)},g.prototype.heapify=function(){return i(this.nodes,this.cmp)},g.prototype.updateItem=function(m){return v(this.nodes,m,this.cmp)},g.prototype.clear=function(){return this.nodes=[]},g.prototype.empty=function(){return this.nodes.length===0},g.prototype.size=function(){return this.nodes.length},g.prototype.clone=function(){var m;return m=new g,m.nodes=this.nodes.slice(0),m},g.prototype.toArray=function(){return this.nodes.slice(0)},g.prototype.insert=g.prototype.push,g.prototype.top=g.prototype.peek,g.prototype.front=g.prototype.peek,g.prototype.has=g.prototype.contains,g.prototype.copy=g.prototype.clone,g}(),function(g,m){return t.exports=m()}(this,function(){return r})}).call(La)}),ka=af,nf=At({root:null,weight:function(e){return 1},directed:!1}),sf={dijkstra:function(e){if(!L(e)){var r=arguments;e={root:r[0],weight:r[1],directed:r[2]}}var a=nf(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=j(n)?this.filter(n)[0]:n[0],f={},h={},c={},d=this.byGroup(),v=d.nodes,p=d.edges;p.unmergeBy(function(re){return re.isLoop()});for(var y=function(q){return f[q.id()]},g=function(q,Z){f[q.id()]=Z,m.updateItem(q)},m=new ka(function(re,q){return y(re)-y(q)}),b=0;b<v.length;b++){var E=v[b];f[E.id()]=E.same(u)?0:1/0,m.push(E)}for(var N=function(q,Z){for(var ie=(s?q.edgesTo(Z):q.edgesWith(Z)).intersect(p),ue=1/0,ge,se=0;se<ie.length;se++){var ve=ie[se],ye=l(ve);(ye<ue||!ge)&&(ue=ye,ge=ve)}return{edge:ge,dist:ue}};m.size()>0;){var A=m.pop(),x=y(A),I=A.id();if(c[I]=x,x!==1/0)for(var C=A.neighborhood().intersect(v),F=0;F<C.length;F++){var z=C[F],M=z.id(),X=N(A,z),B=x+X.dist;B<y(z)&&(g(z,B),h[M]={node:A,edge:X.edge})}}return{distanceTo:function(q){var Z=j(q)?v.filter(q)[0]:q[0];return c[Z.id()]},pathTo:function(q){var Z=j(q)?v.filter(q)[0]:q[0],ie=[],ue=Z,ge=ue.id();if(Z.length>0)for(ie.unshift(Z);h[ge];){var se=h[ge];ie.unshift(se.edge),ie.unshift(se.node),ue=se.node,ge=ue.id()}return o.spawn(ie)}}}},of={kruskal:function(e){e=e||function(b){return 1};for(var r=this.byGroup(),a=r.nodes,n=r.edges,i=a.length,s=new Array(i),o=a,l=function(E){for(var N=0;N<s.length;N++){var A=s[N];if(A.has(E))return N}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var f=n.sort(function(b,E){return e(b)-e(E)}),h=0;h<f.length;h++){var c=f[h],d=c.source()[0],v=c.target()[0],p=l(d),y=l(v),g=s[p],m=s[y];p!==y&&(o.merge(c),g.merge(m),s.splice(y,1))}return o}},uf=At({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),lf={aStar:function(e){var r=this.cy(),a=uf(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=r.collection(n)[0],i=r.collection(i)[0];var u=n.id(),f=i.id(),h={},c={},d={},v=new ka(function(ge,se){return c[ge.id()]-c[se.id()]}),p=new jr,y={},g={},m=function(se,ve){v.push(se),p.add(ve)},b,E,N=function(){b=v.pop(),E=b.id(),p.delete(E)},A=function(se){return p.has(se)};m(n,u),h[u]=0,c[u]=s(n);for(var x=0;v.size()>0;){if(N(),x++,E===f){for(var I=[],C=i,F=f,z=g[F];I.unshift(C),z!=null&&I.unshift(z),C=y[F],C!=null;)F=C.id(),z=g[F];return{found:!0,distance:h[E],path:this.spawn(I),steps:x}}d[E]=!0;for(var M=b._private.edges,X=0;X<M.length;X++){var B=M[X];if(this.hasElementWithId(B.id())&&!(o&&B.data("source")!==E)){var re=B.source(),q=B.target(),Z=re.id()!==E?re:q,ie=Z.id();if(this.hasElementWithId(ie)&&!d[ie]){var ue=h[E]+l(B);if(!A(ie)){h[ie]=ue,c[ie]=ue+s(Z),m(Z,ie),y[ie]=b,g[ie]=B;continue}ue<h[ie]&&(h[ie]=ue,c[ie]=ue+s(Z),y[ie]=b,g[ie]=B)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},ff=At({weight:function(e){return 1},directed:!1}),hf={floydWarshall:function(e){for(var r=this.cy(),a=ff(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,f=l.length,h=f*f,c=function(ye){return l.indexOf(ye)},d=function(ye){return l[ye]},v=new Array(h),p=0;p<h;p++){var y=p%f,g=(p-y)/f;g===y?v[p]=0:v[p]=1/0}for(var m=new Array(h),b=new Array(h),E=0;E<u.length;E++){var N=u[E],A=N.source()[0],x=N.target()[0];if(A!==x){var I=c(A),C=c(x),F=I*f+C,z=s(N);if(v[F]>z&&(v[F]=z,m[F]=C,b[F]=N),!i){var M=C*f+I;!i&&v[M]>z&&(v[M]=z,m[M]=I,b[M]=N)}}}for(var X=0;X<f;X++)for(var B=0;B<f;B++)for(var re=B*f+X,q=0;q<f;q++){var Z=B*f+q,ie=X*f+q;v[re]+v[ie]<v[Z]&&(v[Z]=v[re]+v[ie],m[Z]=m[re])}var ue=function(ye){return(j(ye)?r.filter(ye):ye)[0]},ge=function(ye){return c(ue(ye))},se={distance:function(ye,Te){var be=ge(ye),me=ge(Te);return v[be*f+me]},path:function(ye,Te){var be=ge(ye),me=ge(Te),ae=d(be);if(be===me)return ae.collection();if(m[be*f+me]==null)return r.collection();var xe=r.collection(),Ce=be,Oe;for(xe.merge(ae);be!==me;)Ce=be,be=m[be*f+me],Oe=b[Ce*f+be],xe.merge(Oe),xe.merge(d(be));return xe}};return se}},cf=At({weight:function(e){return 1},directed:!1,root:null}),vf={bellmanFord:function(e){var r=this,a=cf(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),f=this.byGroup(),h=f.edges,c=f.nodes,d=c.length,v=new lr,p=!1,y=[];s=u.collection(s)[0],h.unmergeBy(function(Fe){return Fe.isLoop()});for(var g=h.length,m=function(ke){var ze=v.get(ke.id());return ze||(ze={},v.set(ke.id(),ze)),ze},b=function(ke){return(j(ke)?u.$(ke):ke)[0]},E=function(ke){return m(b(ke)).dist},N=function(ke){for(var ze=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,je=b(ke),Ze=[],Ye=je;;){if(Ye==null)return r.spawn();var ct=m(Ye),De=ct.edge,k=ct.pred;if(Ze.unshift(Ye[0]),Ye.same(ze)&&Ze.length>0)break;De!=null&&Ze.unshift(De),Ye=k}return l.spawn(Ze)},A=0;A<d;A++){var x=c[A],I=m(x);x.same(s)?I.dist=0:I.dist=1/0,I.pred=null,I.edge=null}for(var C=!1,F=function(ke,ze,je,Ze,Ye,ct){var De=Ze.dist+ct;De<Ye.dist&&!je.same(Ze.edge)&&(Ye.dist=De,Ye.pred=ke,Ye.edge=je,C=!0)},z=1;z<d;z++){C=!1;for(var M=0;M<g;M++){var X=h[M],B=X.source(),re=X.target(),q=o(X),Z=m(B),ie=m(re);F(B,re,X,Z,ie,q),i||F(re,B,X,ie,Z,q)}if(!C)break}if(C)for(var ue=[],ge=0;ge<g;ge++){var se=h[ge],ve=se.source(),ye=se.target(),Te=o(se),be=m(ve).dist,me=m(ye).dist;if(be+Te<me||!i&&me+Te<be)if(p||(ft("Graph contains a negative weight cycle for Bellman-Ford"),p=!0),e.findNegativeWeightCycles!==!1){var ae=[];be+Te<me&&ae.push(ve),!i&&me+Te<be&&ae.push(ye);for(var xe=ae.length,Ce=0;Ce<xe;Ce++){var Oe=ae[Ce],Me=[Oe];Me.push(m(Oe).edge);for(var He=m(Oe).pred;Me.indexOf(He)===-1;)Me.push(He),Me.push(m(He).edge),He=m(He).pred;Me=Me.slice(Me.indexOf(He));for(var We=Me[0].id(),Re=0,Ie=2;Ie<Me.length;Ie+=2)Me[Ie].id()<We&&(We=Me[Ie].id(),Re=Ie);Me=Me.slice(Re).concat(Me.slice(0,Re)),Me.push(Me[0]);var Ge=Me.map(function(Fe){return Fe.id()}).join(",");ue.indexOf(Ge)===-1&&(y.push(l.spawn(Me)),ue.push(Ge))}}else break}return{distanceTo:E,pathTo:N,hasNegativeWeightCycle:p,negativeWeightCycles:y}}},df=Math.sqrt(2),gf=function(e,r,a){a.length===0&&Tt("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=r[i],l=r[s],u=a,f=u.length-1;f>=0;f--){var h=u[f],c=h[1],d=h[2];(r[c]===o&&r[d]===l||r[c]===l&&r[d]===o)&&u.splice(f,1)}for(var v=0;v<u.length;v++){var p=u[v];p[1]===l?(u[v]=p.slice(),u[v][1]=o):p[2]===l&&(u[v]=p.slice(),u[v][2]=o)}for(var y=0;y<r.length;y++)r[y]===l&&(r[y]=o);return u},oi=function(e,r,a,n){for(;a>n;){var i=Math.floor(Math.random()*r.length);r=gf(i,e,r),a--}return r},pf={kargerStein:function(){var e=this,r=this.byGroup(),a=r.nodes,n=r.edges;n.unmergeBy(function(ie){return ie.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/df);if(i<2){Tt("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],f=0;f<s;f++){var h=n[f];u.push([f,a.indexOf(h.source()),a.indexOf(h.target())])}for(var c=1/0,d=[],v=new Array(i),p=new Array(i),y=new Array(i),g=function(ue,ge){for(var se=0;se<i;se++)ge[se]=ue[se]},m=0;m<=o;m++){for(var b=0;b<i;b++)p[b]=b;var E=oi(p,u.slice(),i,l),N=E.slice();g(p,y);var A=oi(p,E,l,2),x=oi(y,N,l,2);A.length<=x.length&&A.length<c?(c=A.length,d=A,g(p,v)):x.length<=A.length&&x.length<c&&(c=x.length,d=x,g(y,v))}for(var I=this.spawn(d.map(function(ie){return n[ie[0]]})),C=this.spawn(),F=this.spawn(),z=v[0],M=0;M<v.length;M++){var X=v[M],B=a[M];X===z?C.merge(B):F.merge(B)}var re=function(ue){var ge=e.spawn();return ue.forEach(function(se){ge.merge(se),se.connectedEdges().forEach(function(ve){e.contains(ve)&&!I.contains(ve)&&ge.merge(ve)})}),ge},q=[re(C),re(F)],Z={cut:I,components:q,partition1:C,partition2:F};return Z}},yf=function(e){return{x:e.x,y:e.y}},gn=function(e,r,a){return{x:e.x*r+a.x,y:e.y*r+a.y}},ms=function(e,r,a){return{x:(e.x-a.x)/r,y:(e.y-a.y)/r}},ea=function(e){return{x:e[0],y:e[1]}},mf=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},bf=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=r;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},Ef=function(e){for(var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=r;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},wf=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(r,a):(a<e.length&&e.splice(a,e.length-a),r>0&&e.splice(0,r));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,d){return c-d});var f=e.length,h=Math.floor(f/2);return f%2!==0?e[h+1+o]:(e[h-1+o]+e[h+o])/2},xf=function(e){return Math.PI*e/180},pn=function(e,r){return Math.atan2(r,e)-Math.PI/2},ui=Math.log2||function(t){return Math.log(t)/Math.log(2)},bs=function(e){return e>0?1:e<0?-1:0},Br=function(e,r){return Math.sqrt(Fr(e,r))},Fr=function(e,r){var a=r.x-e.x,n=r.y-e.y;return a*a+n*n},Tf=function(e){for(var r=e.length,a=0,n=0;n<r;n++)a+=e[n];for(var i=0;i<r;i++)e[i]=e[i]/a;return e},Lt=function(e,r,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*r+n*n*a},ta=function(e,r,a,n){return{x:Lt(e.x,r.x,a.x,n),y:Lt(e.y,r.y,a.y,n)}},Cf=function(e,r,a,n){var i={x:r.x-e.x,y:r.y-e.y},s=Br(e,r),o={x:i.x/s,y:i.y/s};return a=a??0,n=n??a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},Pa=function(e,r,a){return Math.max(e,Math.min(a,r))},Yt=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},Df=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},Sf=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},Lf=function(e,r){e.x1=Math.min(e.x1,r.x1),e.x2=Math.max(e.x2,r.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,r.y1),e.y2=Math.max(e.y2,r.y2),e.h=e.y2-e.y1},Af=function(e,r,a){e.x1=Math.min(e.x1,r),e.x2=Math.max(e.x2,r),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},yn=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=r,e.x2+=r,e.y1-=r,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},li=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(r.length===1)a=n=i=s=r[0];else if(r.length===2)a=i=r[0],s=n=r[1];else if(r.length===4){var o=w(r,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Es=function(e,r){e.x1=r.x1,e.y1=r.y1,e.x2=r.x2,e.y2=r.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},fi=function(e,r){return!(e.x1>r.x2||r.x1>e.x2||e.x2<r.x1||r.x2<e.x1||e.y2<r.y1||r.y2<e.y1||e.y1>r.y2||r.y1>e.y2)},ra=function(e,r,a){return e.x1<=r&&r<=e.x2&&e.y1<=a&&a<=e.y2},Of=function(e,r){return ra(e,r.x,r.y)},ws=function(e,r){return ra(e,r.x1,r.y1)&&ra(e,r.x2,r.y2)},xs=function(e,r,a,n,i,s,o){var l=za(i,s),u=i/2,f=s/2,h;{var c=a-u+l-o,d=n-f-o,v=a+u-l+o,p=d;if(h=Cr(e,r,a,n,c,d,v,p,!1),h.length>0)return h}{var y=a+u+o,g=n-f+l-o,m=y,b=n+f-l+o;if(h=Cr(e,r,a,n,y,g,m,b,!1),h.length>0)return h}{var E=a-u+l-o,N=n+f+o,A=a+u-l+o,x=N;if(h=Cr(e,r,a,n,E,N,A,x,!1),h.length>0)return h}{var I=a-u-o,C=n-f+l-o,F=I,z=n+f-l+o;if(h=Cr(e,r,a,n,I,C,F,z,!1),h.length>0)return h}var M;{var X=a-u+l,B=n-f+l;if(M=Ba(e,r,a,n,X,B,l+o),M.length>0&&M[0]<=X&&M[1]<=B)return[M[0],M[1]]}{var re=a+u-l,q=n-f+l;if(M=Ba(e,r,a,n,re,q,l+o),M.length>0&&M[0]>=re&&M[1]<=q)return[M[0],M[1]]}{var Z=a+u-l,ie=n+f-l;if(M=Ba(e,r,a,n,Z,ie,l+o),M.length>0&&M[0]>=Z&&M[1]>=ie)return[M[0],M[1]]}{var ue=a-u+l,ge=n+f-l;if(M=Ba(e,r,a,n,ue,ge,l+o),M.length>0&&M[0]<=ue&&M[1]>=ge)return[M[0],M[1]]}return[]},Nf=function(e,r,a,n,i,s,o){var l=o,u=Math.min(a,i),f=Math.max(a,i),h=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=f+l&&h-l<=r&&r<=c+l},Mf=function(e,r,a,n,i,s,o,l,u){var f={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<f.x1||e>f.x2||r<f.y1||r>f.y2)},If=function(e,r,a,n){a-=n;var i=r*r-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-r+s)/o,u=(-r-s)/o;return[l,u]},Rf=function(e,r,a,n,i){var s=1e-5;e===0&&(e=s),r/=e,a/=e,n/=e;var o,l,u,f,h,c,d,v;if(l=(3*a-r*r)/9,u=-(27*n)+r*(9*a-2*(r*r)),u/=54,o=l*l*l+u*u,i[1]=0,d=r/3,o>0){h=u+Math.sqrt(o),h=h<0?-Math.pow(-h,1/3):Math.pow(h,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-d+h+c,d+=(h+c)/2,i[4]=i[2]=-d,d=Math.sqrt(3)*(-c+h)/2,i[3]=d,i[5]=-d;return}if(i[5]=i[3]=0,o===0){v=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-d+2*v,i[4]=i[2]=-(v+d);return}l=-l,f=l*l*l,f=Math.acos(u/Math.sqrt(f)),v=2*Math.sqrt(l),i[0]=-d+v*Math.cos(f/3),i[2]=-d+v*Math.cos((f+2*Math.PI)/3),i[4]=-d+v*Math.cos((f+4*Math.PI)/3)},kf=function(e,r,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,f=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,h=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*r+2*s*s+2*s*r-l*r,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*r-s*r,d=[];Rf(u,f,h,c,d);for(var v=1e-7,p=[],y=0;y<6;y+=2)Math.abs(d[y+1])<v&&d[y]>=0&&d[y]<=1&&p.push(d[y]);p.push(1),p.push(0);for(var g=-1,m,b,E,N=0;N<p.length;N++)m=Math.pow(1-p[N],2)*a+2*(1-p[N])*p[N]*i+p[N]*p[N]*o,b=Math.pow(1-p[N],2)*n+2*(1-p[N])*p[N]*s+p[N]*p[N]*l,E=Math.pow(m-e,2)+Math.pow(b-r,2),g>=0?E<g&&(g=E):g=E;return g},Pf=function(e,r,a,n,i,s){var o=[e-a,r-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],f=o[0]*o[0]+o[1]*o[1],h=o[0]*l[0]+o[1]*l[1],c=h*h/u;return h<0?f:c>u?(e-i)*(e-i)+(r-s)*(r-s):f-c},Ht=function(e,r,a){for(var n,i,s,o,l,u=0,f=0;f<a.length/2;f++)if(n=a[f*2],i=a[f*2+1],f+1<a.length/2?(s=a[(f+1)*2],o=a[(f+1)*2+1]):(s=a[(f+1-a.length/2)*2],o=a[(f+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>r&&u++;else continue;return u%2!==0},pr=function(e,r,a,n,i,s,o,l,u){var f=new Array(a.length),h;l[0]!=null?(h=Math.atan(l[1]/l[0]),l[0]<0?h=h+Math.PI/2:h=-h-Math.PI/2):h=l;for(var c=Math.cos(-h),d=Math.sin(-h),v=0;v<f.length/2;v++)f[v*2]=s/2*(a[v*2]*c-a[v*2+1]*d),f[v*2+1]=o/2*(a[v*2+1]*c+a[v*2]*d),f[v*2]+=n,f[v*2+1]+=i;var p;if(u>0){var y=Cs(f,-u);p=Ts(y)}else p=f;return Ht(e,r,p)},Bf=function(e,r,a,n,i,s,o){for(var l=new Array(a.length),u=s/2,f=o/2,h=vi(s,o),c=h*h,d=0;d<a.length/4;d++){var v=void 0,p=void 0;d===0?v=a.length-2:v=d*4-2,p=d*4+2;var y=n+u*a[d*4],g=i+f*a[d*4+1],m=-a[v]*a[p]-a[v+1]*a[p+1],b=h/Math.tan(Math.acos(m)/2),E=y-b*a[v],N=g-b*a[v+1],A=y+b*a[p],x=g+b*a[p+1];l[d*4]=E,l[d*4+1]=N,l[d*4+2]=A,l[d*4+3]=x;var I=a[v+1],C=-a[v],F=I*a[p]+C*a[p+1];F<0&&(I*=-1,C*=-1);var z=E+I*h,M=N+C*h,X=Math.pow(z-e,2)+Math.pow(M-r,2);if(X<=c)return!0}return Ht(e,r,l)},Ts=function(e){for(var r=new Array(e.length/2),a,n,i,s,o,l,u,f,h=0;h<e.length/4;h++){a=e[h*4],n=e[h*4+1],i=e[h*4+2],s=e[h*4+3],h<e.length/4-1?(o=e[(h+1)*4],l=e[(h+1)*4+1],u=e[(h+1)*4+2],f=e[(h+1)*4+3]):(o=e[0],l=e[1],u=e[2],f=e[3]);var c=Cr(a,n,i,s,o,l,u,f,!0);r[h*2]=c[0],r[h*2+1]=c[1]}return r},Cs=function(e,r){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,f=-(s-n),h=Math.sqrt(u*u+f*f),c=u/h,d=f/h;a[l*4]=n+c*r,a[l*4+1]=i+d*r,a[l*4+2]=s+c*r,a[l*4+3]=o+d*r}return a},Ff=function(e,r,a,n,i,s){var o=a-e,l=n-r;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),f=u-1;if(f<0)return[];var h=f/u;return[(a-e)*h+e,(n-r)*h+r]},zr=function(e,r,a,n,i,s,o){return e-=i,r-=s,e/=a/2+o,r/=n/2+o,e*e+r*r<=1},Ba=function(e,r,a,n,i,s,o){var l=[a-e,n-r],u=[e-i,r-s],f=l[0]*l[0]+l[1]*l[1],h=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,d=h*h-4*f*c;if(d<0)return[];var v=(-h+Math.sqrt(d))/(2*f),p=(-h-Math.sqrt(d))/(2*f),y=Math.min(v,p),g=Math.max(v,p),m=[];if(y>=0&&y<=1&&m.push(y),g>=0&&g<=1&&m.push(g),m.length===0)return[];var b=m[0]*l[0]+e,E=m[0]*l[1]+r;if(m.length>1){if(m[0]==m[1])return[b,E];var N=m[1]*l[0]+e,A=m[1]*l[1]+r;return[b,E,N,A]}else return[b,E]},hi=function(e,r,a){return r<=e&&e<=a||a<=e&&e<=r?e:e<=r&&r<=a||a<=r&&r<=e?r:a},Cr=function(e,r,a,n,i,s,o,l,u){var f=e-i,h=a-e,c=o-i,d=r-s,v=n-r,p=l-s,y=c*d-p*f,g=h*d-v*f,m=p*h-c*v;if(m!==0){var b=y/m,E=g/m,N=.001,A=0-N,x=1+N;return A<=b&&b<=x&&A<=E&&E<=x?[e+b*h,r+b*v]:u?[e+b*h,r+b*v]:[]}else return y===0||g===0?hi(e,a,o)===o?[o,l]:hi(e,a,i)===i?[i,s]:hi(i,o,a)===a?[a,n]:[]:[]},Fa=function(e,r,a,n,i,s,o,l){var u=[],f,h=new Array(a.length),c=!0;s==null&&(c=!1);var d;if(c){for(var v=0;v<h.length/2;v++)h[v*2]=a[v*2]*s+n,h[v*2+1]=a[v*2+1]*o+i;if(l>0){var p=Cs(h,-l);d=Ts(p)}else d=h}else d=a;for(var y,g,m,b,E=0;E<d.length/2;E++)y=d[E*2],g=d[E*2+1],E<d.length/2-1?(m=d[(E+1)*2],b=d[(E+1)*2+1]):(m=d[0],b=d[1]),f=Cr(e,r,n,i,y,g,m,b),f.length!==0&&u.push(f[0],f[1]);return u},zf=function(e,r,a,n,i,s,o,l){for(var u=[],f,h=new Array(a.length),c=s/2,d=o/2,v=vi(s,o),p=0;p<a.length/4;p++){var y=void 0,g=void 0;p===0?y=a.length-2:y=p*4-2,g=p*4+2;var m=n+c*a[p*4],b=i+d*a[p*4+1],E=-a[y]*a[g]-a[y+1]*a[g+1],N=v/Math.tan(Math.acos(E)/2),A=m-N*a[y],x=b-N*a[y+1],I=m+N*a[g],C=b+N*a[g+1];p===0?(h[a.length-2]=A,h[a.length-1]=x):(h[p*4-2]=A,h[p*4-1]=x),h[p*4]=I,h[p*4+1]=C;var F=a[y+1],z=-a[y],M=F*a[g]+z*a[g+1];M<0&&(F*=-1,z*=-1);var X=A+F*v,B=x+z*v;f=Ba(e,r,n,i,X,B,v),f.length!==0&&u.push(f[0],f[1])}for(var re=0;re<h.length/4;re++)f=Cr(e,r,n,i,h[re*4],h[re*4+1],h[re*4+2],h[re*4+3],!1),f.length!==0&&u.push(f[0],f[1]);if(u.length>2){for(var q=[u[0],u[1]],Z=Math.pow(q[0]-e,2)+Math.pow(q[1]-r,2),ie=1;ie<u.length/2;ie++){var ue=Math.pow(u[ie*2]-e,2)+Math.pow(u[ie*2+1]-r,2);ue<=Z&&(q[0]=u[ie*2],q[1]=u[ie*2+1],Z=ue)}return q}return u},mn=function(e,r,a){var n=[e[0]-r[0],e[1]-r[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[r[0]+s*n[0],r[1]+s*n[1]]},Vt=function(e,r){var a=ci(e,r);return a=Ds(a),a},Ds=function(e){for(var r,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)r=e[2*u],a=e[2*u+1],i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),l=Math.max(l,a);for(var f=2/(o-i),h=2/(l-s),c=0;c<n;c++)r=e[2*c]=e[2*c]*f,a=e[2*c+1]=e[2*c+1]*h,i=Math.min(i,r),o=Math.max(o,r),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var d=0;d<n;d++)a=e[2*d+1]=e[2*d+1]+(-1-s);return e},ci=function(e,r){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=r;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},za=function(e,r){return Math.min(e/4,r/4,8)},vi=function(e,r){return Math.min(e/10,r/10,8)},Ss=function(){return 8},Gf=function(e,r,a){return[e-2*r+a,2*(r-e),e]},di=function(e,r){return{heightOffset:Math.min(15,.05*r),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},$f=At({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),Vf={pageRank:function(e){for(var r=$f(e),a=r.dampingFactor,n=r.precision,i=r.iterations,s=r.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,f=l.edges,h=u.length,c=h*h,d=f.length,v=new Array(c),p=new Array(h),y=(1-a)/h,g=0;g<h;g++){for(var m=0;m<h;m++){var b=g*h+m;v[b]=0}p[g]=0}for(var E=0;E<d;E++){var N=f[E],A=N.data("source"),x=N.data("target");if(A!==x){var I=u.indexOfId(A),C=u.indexOfId(x),F=s(N),z=C*h+I;v[z]+=F,p[I]+=F}}for(var M=1/h+y,X=0;X<h;X++)if(p[X]===0)for(var B=0;B<h;B++){var re=B*h+X;v[re]=M}else for(var q=0;q<h;q++){var Z=q*h+X;v[Z]=v[Z]/p[X]+y}for(var ie=new Array(h),ue=new Array(h),ge,se=0;se<h;se++)ie[se]=1;for(var ve=0;ve<i;ve++){for(var ye=0;ye<h;ye++)ue[ye]=0;for(var Te=0;Te<h;Te++)for(var be=0;be<h;be++){var me=Te*h+be;ue[Te]+=v[me]*ie[be]}Tf(ue),ge=ie,ie=ue,ue=ge;for(var ae=0,xe=0;xe<h;xe++){var Ce=ge[xe]-ie[xe];ae+=Ce*Ce}if(ae<n)break}var Oe={rank:function(He){return He=o.collection(He)[0],ie[u.indexOf(He)]}};return Oe}},Ls=At({root:null,weight:function(e){return 1},directed:!1,alpha:0}),aa={degreeCentralityNormalized:function(e){e=Ls(e);var r=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var f={},h={},c=0,d=0,v=0;v<n;v++){var p=a[v],y=p.id();e.root=p;var g=this.degreeCentrality(e);c<g.indegree&&(c=g.indegree),d<g.outdegree&&(d=g.outdegree),f[y]=g.indegree,h[y]=g.outdegree}return{indegree:function(b){return c==0?0:(j(b)&&(b=r.filter(b)),f[b.id()]/c)},outdegree:function(b){return d===0?0:(j(b)&&(b=r.filter(b)),h[b.id()]/d)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(j(b)&&(b=r.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=Ls(e);var r=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=r.collection(i)[0],o){for(var d=i.connectedEdges(),v=d.filter(function(A){return A.target().same(i)&&a.has(A)}),p=d.filter(function(A){return A.source().same(i)&&a.has(A)}),y=v.length,g=p.length,m=0,b=0,E=0;E<v.length;E++)m+=s(v[E]);for(var N=0;N<p.length;N++)b+=s(p[N]);return{indegree:Math.pow(y,1-l)*Math.pow(m,l),outdegree:Math.pow(g,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),f=u.length,h=0,c=0;c<u.length;c++)h+=s(u[c]);return{degree:Math.pow(f,1-l)*Math.pow(h,l)}}}};aa.dc=aa.degreeCentrality,aa.dcn=aa.degreeCentralityNormalised=aa.degreeCentralityNormalized;var As=At({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),na={closenessCentralityNormalized:function(e){for(var r=As(e),a=r.harmonic,n=r.weight,i=r.directed,s=this.cy(),o={},l=0,u=this.nodes(),f=this.floydWarshall({weight:n,directed:i}),h=0;h<u.length;h++){for(var c=0,d=u[h],v=0;v<u.length;v++)if(h!==v){var p=f.distance(d,u[v]);a?c+=1/p:c+=p}a||(c=1/c),l<c&&(l=c),o[d.id()]=c}return{closeness:function(g){return l==0?0:(j(g)?g=s.filter(g)[0].id():g=g.id(),o[g]/l)}}},closenessCentrality:function(e){var r=As(e),a=r.root,n=r.weight,i=r.directed,s=r.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),f=0;f<u.length;f++){var h=u[f];if(!h.same(a)){var c=o.distanceTo(h);s?l+=1/c:l+=c}}return s?l:1/l}};na.cc=na.closenessCentrality,na.ccn=na.closenessCentralityNormalised=na.closenessCentralityNormalized;var _f=At({weight:null,directed:!1}),gi={betweennessCentrality:function(e){for(var r=_f(e),a=r.directed,n=r.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},f=0,h={set:function(b,E){u[b]=E,E>f&&(f=E)},get:function(b){return u[b]}},c=0;c<o.length;c++){var d=o[c],v=d.id();a?l[v]=d.outgoers().nodes():l[v]=d.openNeighborhood().nodes(),h.set(v,0)}for(var p=function(b){for(var E=o[b].id(),N=[],A={},x={},I={},C=new ka(function(be,me){return I[be]-I[me]}),F=0;F<o.length;F++){var z=o[F].id();A[z]=[],x[z]=0,I[z]=1/0}for(x[E]=1,I[E]=0,C.push(E);!C.empty();){var M=C.pop();if(N.push(M),i)for(var X=0;X<l[M].length;X++){var B=l[M][X],re=s.getElementById(M),q=void 0;re.edgesTo(B).length>0?q=re.edgesTo(B)[0]:q=B.edgesTo(re)[0];var Z=n(q);B=B.id(),I[B]>I[M]+Z&&(I[B]=I[M]+Z,C.nodes.indexOf(B)<0?C.push(B):C.updateItem(B),x[B]=0,A[B]=[]),I[B]==I[M]+Z&&(x[B]=x[B]+x[M],A[B].push(M))}else for(var ie=0;ie<l[M].length;ie++){var ue=l[M][ie].id();I[ue]==1/0&&(C.push(ue),I[ue]=I[M]+1),I[ue]==I[M]+1&&(x[ue]=x[ue]+x[M],A[ue].push(M))}}for(var ge={},se=0;se<o.length;se++)ge[o[se].id()]=0;for(;N.length>0;){for(var ve=N.pop(),ye=0;ye<A[ve].length;ye++){var Te=A[ve][ye];ge[Te]=ge[Te]+x[Te]/x[ve]*(1+ge[ve])}ve!=o[b].id()&&h.set(ve,h.get(ve)+ge[ve])}},y=0;y<o.length;y++)p(y);var g={betweenness:function(b){var E=s.collection(b).id();return h.get(E)},betweennessNormalized:function(b){if(f==0)return 0;var E=s.collection(b).id();return h.get(E)/f}};return g.betweennessNormalised=g.betweennessNormalized,g}};gi.bc=gi.betweennessCentrality;var Uf=At({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(t){return 1}]}),Yf=function(e){return Uf(e)},Hf=function(e,r){for(var a=0,n=0;n<r.length;n++)a+=r[n](e);return a},Xf=function(e,r,a){for(var n=0;n<r;n++)e[n*r+n]=a},Os=function(e,r){for(var a,n=0;n<r;n++){a=0;for(var i=0;i<r;i++)a+=e[i*r+n];for(var s=0;s<r;s++)e[s*r+n]=e[s*r+n]/a}},Wf=function(e,r,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*r[o*a+l]}return n},qf=function(e,r,a){for(var n=e.slice(0),i=1;i<a;i++)e=Wf(e,n,r);return e},Kf=function(e,r,a){for(var n=new Array(r*r),i=0;i<r*r;i++)n[i]=Math.pow(e[i],a);return Os(n,r),n},Zf=function(e,r,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(r[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},Qf=function(e,r,a,n){for(var i=[],s=0;s<r;s++){for(var o=[],l=0;l<r;l++)Math.round(e[s*r+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},Jf=function(e,r){for(var a=0;a<e.length;a++)if(!r[a]||e[a].id()!==r[a].id())return!1;return!0},jf=function(e){for(var r=0;r<e.length;r++)for(var a=0;a<e.length;a++)r!=a&&Jf(e[r],e[a])&&e.splice(a,1);return e},Ns=function(e){for(var r=this.nodes(),a=this.edges(),n=this.cy(),i=Yf(e),s={},o=0;o<r.length;o++)s[r[o].id()]=o;for(var l=r.length,u=l*l,f=new Array(u),h,c=0;c<u;c++)f[c]=0;for(var d=0;d<a.length;d++){var v=a[d],p=s[v.source().id()],y=s[v.target().id()],g=Hf(v,i.attributes);f[p*l+y]+=g,f[y*l+p]+=g}Xf(f,l,i.multFactor),Os(f,l);for(var m=!0,b=0;m&&b<i.maxIterations;)m=!1,h=qf(f,l,i.expandFactor),f=Kf(h,l,i.inflateFactor),Zf(f,h,u,4)||(m=!0),b++;var E=Qf(f,l,r,n);return E=jf(E),E},eh={markovClustering:Ns,mcl:Ns},th=function(e){return e},Ms=function(e,r){return Math.abs(r-e)},Is=function(e,r,a){return e+Ms(r,a)},Rs=function(e,r,a){return e+Math.pow(a-r,2)},rh=function(e){return Math.sqrt(e)},ah=function(e,r,a){return Math.max(e,Ms(r,a))},Ga=function(e,r,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:th,o=n,l,u,f=0;f<e;f++)l=r(f),u=a(f),o=i(o,l,u);return s(o)},ia={euclidean:function(e,r,a){return e>=2?Ga(e,r,a,0,Rs,rh):Ga(e,r,a,0,Is)},squaredEuclidean:function(e,r,a){return Ga(e,r,a,0,Rs)},manhattan:function(e,r,a){return Ga(e,r,a,0,Is)},max:function(e,r,a){return Ga(e,r,a,-1/0,ah)}};ia["squared-euclidean"]=ia.squaredEuclidean,ia.squaredeuclidean=ia.squaredEuclidean;function bn(t,e,r,a,n,i){var s;return Y(t)?s=t:s=ia[t]||ia.euclidean,e===0&&Y(t)?s(n,i):s(e,r,a,n,i)}var nh=At({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),pi=function(e){return nh(e)},En=function(e,r,a,n,i){var s=i!=="kMedoids",o=s?function(h){return a[h]}:function(h){return n[h](a)},l=function(c){return n[c](r)},u=a,f=r;return bn(e,n.length,o,l,u,f)},yi=function(e,r,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(r),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var f=0;f<r;f++){l=[];for(var h=0;h<n;h++)l[h]=Math.random()*(s[h]-i[h])+i[h];o[f]=l}return o},ks=function(e,r,a,n,i){for(var s=1/0,o=0,l=0;l<r.length;l++){var u=En(a,e,r[l],n,i);u<s&&(s=u,o=l)}return o},Ps=function(e,r,a){for(var n=[],i=null,s=0;s<r.length;s++)i=r[s],a[i.id()]===e&&n.push(i);return n},ih=function(e,r,a){return Math.abs(r-e)<=a},sh=function(e,r,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-r[n][i]);if(s>a)return!1}return!0},oh=function(e,r,a){for(var n=0;n<a;n++)if(e===r[n])return!0;return!1},Bs=function(e,r){var a=new Array(r);if(e.length<50)for(var n=0;n<r;n++){for(var i=e[Math.floor(Math.random()*e.length)];oh(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<r;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},Fs=function(e,r,a){for(var n=0,i=0;i<r.length;i++)n+=En("manhattan",r[i],e,a,"kMedoids");return n},uh=function(e){var r=this.cy(),a=this.nodes(),n=null,i=pi(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=yi(a,i.k,i.attributes)):ee(i.testCentroids)==="object"?l=i.testCentroids:l=yi(a,i.k,i.attributes):l=yi(a,i.k,i.attributes);for(var u=!0,f=0;u&&f<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],o[n.id()]=ks(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var d=Ps(c,a,o);if(d.length!==0){for(var v=i.attributes.length,p=l[c],y=new Array(v),g=new Array(v),m=0;m<v;m++){g[m]=0;for(var b=0;b<d.length;b++)n=d[b],g[m]+=i.attributes[m](n);y[m]=g[m]/d.length,ih(y[m],p[m],i.sensitivityThreshold)||(u=!0)}l[c]=y,s[c]=r.collection(d)}}f++}return s},lh=function(e){var r=this.cy(),a=this.nodes(),n=null,i=pi(e),s=new Array(i.k),o,l={},u,f=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(ee(i.testCentroids)==="object"?o=i.testCentroids:o=Bs(a,i.k)):o=Bs(a,i.k);for(var h=!0,c=0;h&&c<i.maxIterations;){for(var d=0;d<a.length;d++)n=a[d],l[n.id()]=ks(n,o,i.distance,i.attributes,"kMedoids");h=!1;for(var v=0;v<o.length;v++){var p=Ps(v,a,l);if(p.length!==0){f[v]=Fs(o[v],p,i.attributes);for(var y=0;y<p.length;y++)u=Fs(p[y],p,i.attributes),u<f[v]&&(f[v]=u,o[v]=p[y],h=!0);s[v]=r.collection(p)}}c++}return s},fh=function(e,r,a,n,i){for(var s,o,l=0;l<r.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var f=0;f<e.length;f++)for(var h=0;h<i.attributes.length;h++){s=0,o=0;for(var c=0;c<r.length;c++)s+=n[c][f]*i.attributes[h](r[c]),o+=n[c][f];e[f][h]=s/o}},hh=function(e,r,a,n,i){for(var s=0;s<e.length;s++)r[s]=e[s].slice();for(var o,l,u,f=2/(i.m-1),h=0;h<a.length;h++)for(var c=0;c<n.length;c++){o=0;for(var d=0;d<a.length;d++)l=En(i.distance,n[c],a[h],i.attributes,"cmeans"),u=En(i.distance,n[c],a[d],i.attributes,"cmeans"),o+=Math.pow(l/u,f);e[c][h]=1/o}},ch=function(e,r,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<r.length;u++){o=-1/0,l=-1;for(var f=0;f<r[0].length;f++)r[u][f]>o&&(o=r[u][f],l=f);i[l].push(e[u])}for(var h=0;h<i.length;h++)i[h]=n.collection(i[h]);return i},zs=function(e){var r=this.cy(),a=this.nodes(),n=pi(e),i,s,o,l,u;l=new Array(a.length);for(var f=0;f<a.length;f++)l[f]=new Array(n.k);o=new Array(a.length);for(var h=0;h<a.length;h++)o[h]=new Array(n.k);for(var c=0;c<a.length;c++){for(var d=0,v=0;v<n.k;v++)o[c][v]=Math.random(),d+=o[c][v];for(var p=0;p<n.k;p++)o[c][p]=o[c][p]/d}s=new Array(n.k);for(var y=0;y<n.k;y++)s[y]=new Array(n.attributes.length);u=new Array(a.length);for(var g=0;g<a.length;g++)u[g]=new Array(n.k);for(var m=!0,b=0;m&&b<n.maxIterations;)m=!1,fh(s,a,o,u,n),hh(o,l,s,a,n),sh(o,l,n.sensitivityThreshold)||(m=!0),b++;return i=ch(a,o,n,r),{clusters:i,degreeOfMembership:o}},vh={kMeans:uh,kMedoids:lh,fuzzyCMeans:zs,fcm:zs},dh=At({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),gh={single:"min",complete:"max"},ph=function(e){var r=dh(e),a=gh[r.linkage];return a!=null&&(r.linkage=a),r},Gs=function(e,r,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,f=function(C,F){return bn(i.distance,u.length,function(z){return u[z](C)},function(z){return u[z](F)},C,F)},h=0;h<e.length;h++){var c=e[h].key,d=a[c][n[c]];d<o&&(s=c,o=d)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var v=r[s],p=r[n[s]],y;i.mode==="dendrogram"?y={left:v,right:p,key:v.key}:y={value:v.value.concat(p.value),key:v.key},e[v.index]=y,e.splice(p.index,1),r[v.key]=y;for(var g=0;g<e.length;g++){var m=e[g];v.key===m.key?l=1/0:i.linkage==="min"?(l=a[v.key][m.key],a[v.key][m.key]>a[p.key][m.key]&&(l=a[p.key][m.key])):i.linkage==="max"?(l=a[v.key][m.key],a[v.key][m.key]<a[p.key][m.key]&&(l=a[p.key][m.key])):i.linkage==="mean"?l=(a[v.key][m.key]*v.size+a[p.key][m.key]*p.size)/(v.size+p.size):i.mode==="dendrogram"?l=f(m.value,v.value):l=f(m.value[0],v.value[0]),a[v.key][m.key]=a[m.key][v.key]=l}for(var b=0;b<e.length;b++){var E=e[b].key;if(n[E]===v.key||n[E]===p.key){for(var N=E,A=0;A<e.length;A++){var x=e[A].key;a[E][x]<a[E][N]&&(N=x)}n[E]=N}e[b].index=b}return v.key=p.key=v.index=p.index=null,!0},wn=function t(e,r,a){e&&(e.value?r.push(e.value):(e.left&&t(e.left,r),e.right&&t(e.right,r)))},yh=function t(e,r){if(!e)return"";if(e.left&&e.right){var a=t(e.left,r),n=t(e.right,r),i=r.add({group:"nodes",data:{id:a+","+n}});return r.add({group:"edges",data:{source:a,target:i.id()}}),r.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},mh=function t(e,r,a){if(!e)return[];var n=[],i=[],s=[];return r===0?(e.left&&wn(e.left,n),e.right&&wn(e.right,i),s=n.concat(i),[a.collection(s)]):r===1?e.value?[a.collection(e.value)]:(e.left&&wn(e.left,n),e.right&&wn(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=t(e.left,r-1,a)),e.right&&(i=t(e.right,r-1,a)),n.concat(i))},$s=function(e){for(var r=this.cy(),a=this.nodes(),n=ph(e),i=n.attributes,s=function(b,E){return bn(n.distance,i.length,function(N){return i[N](b)},function(N){return i[N](E)},b,E)},o=[],l=[],u=[],f=[],h=0;h<a.length;h++){var c={value:n.mode==="dendrogram"?a[h]:[a[h]],key:h,index:h};o[h]=c,f[h]=c,l[h]=[],u[h]=0}for(var d=0;d<o.length;d++)for(var v=0;v<=d;v++){var p=void 0;n.mode==="dendrogram"?p=d===v?1/0:s(o[d].value,o[v].value):p=d===v?1/0:s(o[d].value[0],o[v].value[0]),l[d][v]=p,l[v][d]=p,p<l[d][u[d]]&&(u[d]=v)}for(var y=Gs(o,f,l,u,n);y;)y=Gs(o,f,l,u,n);var g;return n.mode==="dendrogram"?(g=mh(o[0],n.dendrogramDepth,r),n.addDendrogram&&yh(o[0],r)):(g=new Array(o.length),o.forEach(function(m,b){m.key=m.index=null,g[b]=r.collection(m.value)})),g},bh={hierarchicalClustering:$s,hca:$s},Eh=At({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),wh=function(e){var r=e.damping,a=e.preference;.5<=r&&r<1||Tt("Damping must range on [0.5, 1).  Got: ".concat(r));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||R(a)||Tt("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),Eh(e)},xh=function(e,r,a,n){var i=function(o,l){return n[l](o)};return-bn(e,n.length,function(s){return i(r,s)},function(s){return i(a,s)},r,a)},Th=function(e,r){var a=null;return r==="median"?a=wf(e):r==="mean"?a=Ef(e):r==="min"?a=mf(e):r==="max"?a=bf(e):a=r,a},Ch=function(e,r,a){for(var n=[],i=0;i<e;i++)r[i*e+i]+a[i*e+i]>0&&n.push(i);return n},Vs=function(e,r,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];r[i*e+u]>o&&(s=u,o=r[i*e+u])}s>0&&n.push(s)}for(var f=0;f<a.length;f++)n[a[f]]=a[f];return n},Dh=function(e,r,a){for(var n=Vs(e,r,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,f=0;f<s.length;f++){for(var h=0,c=0;c<s.length;c++)h+=r[s[c]*e+s[f]];h>u&&(l=f,u=h)}a[i]=s[l]}return n=Vs(e,r,a),n},_s=function(e){for(var r=this.cy(),a=this.nodes(),n=wh(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,f,h,c;o=a.length,l=o*o,u=new Array(l);for(var d=0;d<l;d++)u[d]=-1/0;for(var v=0;v<o;v++)for(var p=0;p<o;p++)v!==p&&(u[v*o+p]=xh(n.distance,a[v],a[p],n.attributes));f=Th(u,n.preference);for(var y=0;y<o;y++)u[y*o+y]=f;h=new Array(l);for(var g=0;g<l;g++)h[g]=0;c=new Array(l);for(var m=0;m<l;m++)c[m]=0;for(var b=new Array(o),E=new Array(o),N=new Array(o),A=0;A<o;A++)b[A]=0,E[A]=0,N[A]=0;for(var x=new Array(o*n.minIterations),I=0;I<x.length;I++)x[I]=0;var C;for(C=0;C<n.maxIterations;C++){for(var F=0;F<o;F++){for(var z=-1/0,M=-1/0,X=-1,B=0,re=0;re<o;re++)b[re]=h[F*o+re],B=c[F*o+re]+u[F*o+re],B>=z?(M=z,z=B,X=re):B>M&&(M=B);for(var q=0;q<o;q++)h[F*o+q]=(1-n.damping)*(u[F*o+q]-z)+n.damping*b[q];h[F*o+X]=(1-n.damping)*(u[F*o+X]-M)+n.damping*b[X]}for(var Z=0;Z<o;Z++){for(var ie=0,ue=0;ue<o;ue++)b[ue]=c[ue*o+Z],E[ue]=Math.max(0,h[ue*o+Z]),ie+=E[ue];ie-=E[Z],E[Z]=h[Z*o+Z],ie+=E[Z];for(var ge=0;ge<o;ge++)c[ge*o+Z]=(1-n.damping)*Math.min(0,ie-E[ge])+n.damping*b[ge];c[Z*o+Z]=(1-n.damping)*(ie-E[Z])+n.damping*b[Z]}for(var se=0,ve=0;ve<o;ve++){var ye=c[ve*o+ve]+h[ve*o+ve]>0?1:0;x[C%n.minIterations*o+ve]=ye,se+=ye}if(se>0&&(C>=n.minIterations-1||C==n.maxIterations-1)){for(var Te=0,be=0;be<o;be++){N[be]=0;for(var me=0;me<n.minIterations;me++)N[be]+=x[me*o+be];(N[be]===0||N[be]===n.minIterations)&&Te++}if(Te===o)break}}for(var ae=Ch(o,h,c),xe=Dh(o,u,ae),Ce={},Oe=0;Oe<ae.length;Oe++)Ce[ae[Oe]]=[];for(var Me=0;Me<a.length;Me++){var He=i[a[Me].id()],We=xe[He];We!=null&&Ce[We].push(a[Me])}for(var Re=new Array(ae.length),Ie=0;Ie<ae.length;Ie++)Re[Ie]=r.collection(Ce[ae[Ie]]);return Re},Sh={affinityPropagation:_s,ap:_s},Lh=At({root:void 0,directed:!1}),Ah={hierholzer:function(e){if(!L(e)){var r=arguments;e={root:r[0],directed:r[1]}}var a=Lh(e),n=a.root,i=a.directed,s=this,o=!1,l,u,f;n&&(f=j(n)?this.filter(n)[0].id():n[0].id());var h={},c={};i?s.forEach(function(m){var b=m.id();if(m.isNode()){var E=m.indegree(!0),N=m.outdegree(!0),A=E-N,x=N-E;A==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||A>1)&&(o=!0),h[b]=[],m.outgoers().forEach(function(I){I.isEdge()&&h[b].push(I.id())})}else c[b]=[void 0,m.target().id()]}):s.forEach(function(m){var b=m.id();if(m.isNode()){var E=m.degree(!0);E%2&&(l?u?o=!0:u=b:l=b),h[b]=[],m.connectedEdges().forEach(function(N){return h[b].push(N.id())})}else c[b]=[m.source().id(),m.target().id()]});var d={found:!1,trail:void 0};if(o)return d;if(u&&l)if(i){if(f&&u!=f)return d;f=u}else{if(f&&u!=f&&l!=f)return d;f||(f=u)}else f||(f=s[0].id());var v=function(b){for(var E=b,N=[b],A,x,I;h[E].length;)A=h[E].shift(),x=c[A][0],I=c[A][1],E!=I?(h[I]=h[I].filter(function(C){return C!=A}),E=I):!i&&E!=x&&(h[x]=h[x].filter(function(C){return C!=A}),E=x),N.unshift(A),N.unshift(E);return N},p=[],y=[];for(y=v(f);y.length!=1;)h[y[0]].length==0?(p.unshift(s.getElementById(y.shift())),p.unshift(s.getElementById(y.shift()))):y=v(y.shift()).concat(y);p.unshift(s.getElementById(y.shift()));for(var g in h)if(h[g].length)return d;return d.found=!0,d.trail=this.spawn(p,!0),d}},xn=function(){var e=this,r={},a=0,n=0,i=[],s=[],o={},l=function(c,d){for(var v=s.length-1,p=[],y=e.spawn();s[v].x!=c||s[v].y!=d;)p.push(s.pop().edge),v--;p.push(s.pop().edge),p.forEach(function(g){var m=g.connectedNodes().intersection(e);y.merge(g),m.forEach(function(b){var E=b.id(),N=b.connectedEdges().intersection(e);y.merge(b),r[E].cutVertex?y.merge(N.filter(function(A){return A.isLoop()})):y.merge(N)})}),i.push(y)},u=function h(c,d,v){c===v&&(n+=1),r[d]={id:a,low:a++,cutVertex:!1};var p=e.getElementById(d).connectedEdges().intersection(e);if(p.size()===0)i.push(e.spawn(e.getElementById(d)));else{var y,g,m,b;p.forEach(function(E){y=E.source().id(),g=E.target().id(),m=y===d?g:y,m!==v&&(b=E.id(),o[b]||(o[b]=!0,s.push({x:d,y:m,edge:E})),m in r?r[d].low=Math.min(r[d].low,r[m].id):(h(c,m,d),r[d].low=Math.min(r[d].low,r[m].low),r[d].id<=r[m].low&&(r[d].cutVertex=!0,l(d,m))))})}};e.forEach(function(h){if(h.isNode()){var c=h.id();c in r||(n=0,u(c,c),r[c].cutVertex=n>1)}});var f=Object.keys(r).filter(function(h){return r[h].cutVertex}).map(function(h){return e.getElementById(h)});return{cut:e.spawn(f),components:i}},Oh={hopcroftTarjanBiconnected:xn,htbc:xn,htb:xn,hopcroftTarjanBiconnectedComponents:xn},Tn=function(){var e=this,r={},a=0,n=[],i=[],s=e.spawn(e),o=function l(u){i.push(u),r[u]={index:a,low:a++,explored:!1};var f=e.getElementById(u).connectedEdges().intersection(e);if(f.forEach(function(p){var y=p.target().id();y!==u&&(y in r||l(y),r[y].explored||(r[u].low=Math.min(r[u].low,r[y].low)))}),r[u].index===r[u].low){for(var h=e.spawn();;){var c=i.pop();if(h.merge(e.getElementById(c)),r[c].low=r[u].index,r[c].explored=!0,c===u)break}var d=h.edgesWith(h),v=h.merge(d);n.push(v),s=s.difference(v)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in r||o(u)}}),{cut:s,components:n}},Nh={tarjanStronglyConnected:Tn,tsc:Tn,tscc:Tn,tarjanStronglyConnectedComponents:Tn},Us={};[Ra,sf,of,lf,hf,vf,pf,Vf,aa,na,gi,eh,vh,bh,Sh,Ah,Oh,Nh].forEach(function(t){Ue(Us,t)});/*!
Embeddable Minimum Strictly-Compliant Promises/A+ 1.1.1 Thenable
Copyright (c) 2013-2014 Ralf S. Engelschall (http://engelschall.com)
Licensed under The MIT License (http://opensource.org/licenses/MIT)
*/var Ys=0,Hs=1,Xs=2,yr=function t(e){if(!(this instanceof t))return new t(e);this.id="Thenable/1.0.7",this.state=Ys,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};yr.prototype={fulfill:function(e){return Ws(this,Hs,"fulfillValue",e)},reject:function(e){return Ws(this,Xs,"rejectReason",e)},then:function(e,r){var a=this,n=new yr;return a.onFulfilled.push(Zs(e,n,"fulfill")),a.onRejected.push(Zs(r,n,"reject")),qs(a),n.proxy}};var Ws=function(e,r,a,n){return e.state===Ys&&(e.state=r,e[a]=n,qs(e)),e},qs=function(e){e.state===Hs?Ks(e,"onFulfilled",e.fulfillValue):e.state===Xs&&Ks(e,"onRejected",e.rejectReason)},Ks=function(e,r,a){if(e[r].length!==0){var n=e[r];e[r]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},Zs=function(e,r,a){return function(n){if(typeof e!="function")r[a].call(r,n);else{var i;try{i=e(n)}catch(s){r.reject(s);return}Mh(r,i)}}},Mh=function t(e,r){if(e===r||e.proxy===r){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(ee(r)==="object"&&r!==null||typeof r=="function")try{a=r.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(r,function(i){n||(n=!0,i===r?e.reject(new TypeError("circular thenable chain")):t(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(r)};yr.all=function(t){return new yr(function(e,r){for(var a=new Array(t.length),n=0,i=function(l,u){a[l]=u,n++,n===t.length&&e(a)},s=0;s<t.length;s++)(function(o){var l=t[o],u=l!=null&&l.then!=null;if(u)l.then(function(h){i(o,h)},function(h){r(h)});else{var f=l;i(o,f)}})(s)})},yr.resolve=function(t){return new yr(function(e,r){e(t)})},yr.reject=function(t){return new yr(function(e,r){r(t)})};var sa=typeof Promise<"u"?Promise:yr,mi=function(e,r,a){var n=_e(e),i=!n,s=this._private=Ue({duration:1e3},r,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&Y(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},Gr=mi.prototype;Ue(Gr,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var r,a=e.target._private.animation;e.queue?r=a.queue:r=a.current,r.push(this),de(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var r=this._private;return e===void 0?r.progress*r.duration:this.progress(e/r.duration)},progress:function(e){var r=this._private,a=r.playing;return e===void 0?r.progress:(a&&this.pause(),r.progress=e,r.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,r=e.playing;r&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,f){var h=e[u];h!=null&&(e[u]=e[f],e[f]=h)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return r&&this.play(),this},promise:function(e){var r=this._private,a;switch(e){case"frame":a=r.frames;break;default:case"complete":case"completed":a=r.completes}return new sa(function(n,i){a.push(function(){n()})})}}),Gr.complete=Gr.completed,Gr.run=Gr.play,Gr.running=Gr.playing;var Ih={animated:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var r=this,a=r.length!==void 0,n=a?r:[r],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:r,duration:r,complete:a}):this}},delayAnimation:function(){return function(r,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:r,duration:r,complete:a}):this}},animation:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var f=o.style();r=Ue({},r,a);var h=Object.keys(r).length===0;if(h)return new mi(s[0],r);switch(r.duration===void 0&&(r.duration=400),r.duration){case"slow":r.duration=600;break;case"fast":r.duration=200;break}if(u&&(r.style=f.getPropsList(r.style||r.css),r.css=void 0),u&&r.renderedPosition!=null){var c=r.renderedPosition,d=o.pan(),v=o.zoom();r.position=ms(c,v,d)}if(l&&r.panBy!=null){var p=r.panBy,y=o.pan();r.pan={x:y.x+p.x,y:y.y+p.y}}var g=r.center||r.centre;if(l&&g!=null){var m=o.getCenterPan(g.eles,r.zoom);m!=null&&(r.pan=m)}if(l&&r.fit!=null){var b=r.fit,E=o.getFitViewport(b.eles||b.boundingBox,b.padding);E!=null&&(r.pan=E.pan,r.zoom=E.zoom)}if(l&&L(r.zoom)){var N=o.getZoomedViewport(r.zoom);N!=null?(N.zoomed&&(r.zoom=N.zoom),N.panned&&(r.pan=N.pan)):r.zoom=null}return new mi(s[0],r)}},animate:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(r=Ue({},r,a));for(var l=0;l<s.length;l++){var u=s[l],f=u.animated()&&(r.queue===void 0||r.queue),h=u.animation(r,f?{queue:!0}:void 0);h.play()}return this}},stop:function(){return function(r,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],f=u._private,h=f.animation.current,c=0;c<h.length;c++){var d=h[c],v=d._private;a&&(v.duration=0)}r&&(f.animation.queue=[]),a||(f.animation.current=[])}return o.notify("draw"),this}}},Rh=Array.isArray,Cn=Rh,kh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ph=/^\w*$/;function Bh(t,e){if(Cn(t))return!1;var r=typeof t;return r=="number"||r=="symbol"||r=="boolean"||t==null||Oa(t)?!0:Ph.test(t)||!kh.test(t)||e!=null&&t in Object(e)}var Fh=Bh,zh="[object AsyncFunction]",Gh="[object Function]",$h="[object GeneratorFunction]",Vh="[object Proxy]";function _h(t){if(!kr(t))return!1;var e=ns(t);return e==Gh||e==$h||e==zh||e==Vh}var Uh=_h,Yh=ln["__core-js_shared__"],bi=Yh,Qs=function(){var t=/[^.]+$/.exec(bi&&bi.keys&&bi.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Hh(t){return!!Qs&&Qs in t}var Xh=Hh,Wh=Function.prototype,qh=Wh.toString;function Kh(t){if(t!=null){try{return qh.call(t)}catch{}try{return t+""}catch{}}return""}var Zh=Kh,Qh=/[\\^$.*+?()[\]{}|]/g,Jh=/^\[object .+?Constructor\]$/,jh=Function.prototype,ec=Object.prototype,tc=jh.toString,rc=ec.hasOwnProperty,ac=RegExp("^"+tc.call(rc).replace(Qh,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function nc(t){if(!kr(t)||Xh(t))return!1;var e=Uh(t)?ac:Jh;return e.test(Zh(t))}var ic=nc;function sc(t,e){return t==null?void 0:t[e]}var oc=sc;function uc(t,e){var r=oc(t,e);return ic(r)?r:void 0}var Ei=uc,lc=Ei(Object,"create"),$a=lc;function fc(){this.__data__=$a?$a(null):{},this.size=0}var hc=fc;function cc(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var vc=cc,dc="__lodash_hash_undefined__",gc=Object.prototype,pc=gc.hasOwnProperty;function yc(t){var e=this.__data__;if($a){var r=e[t];return r===dc?void 0:r}return pc.call(e,t)?e[t]:void 0}var mc=yc,bc=Object.prototype,Ec=bc.hasOwnProperty;function wc(t){var e=this.__data__;return $a?e[t]!==void 0:Ec.call(e,t)}var xc=wc,Tc="__lodash_hash_undefined__";function Cc(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=$a&&e===void 0?Tc:e,this}var Dc=Cc;function oa(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}oa.prototype.clear=hc,oa.prototype.delete=vc,oa.prototype.get=mc,oa.prototype.has=xc,oa.prototype.set=Dc;var Js=oa;function Sc(){this.__data__=[],this.size=0}var Lc=Sc;function Ac(t,e){return t===e||t!==t&&e!==e}var js=Ac;function Oc(t,e){for(var r=t.length;r--;)if(js(t[r][0],e))return r;return-1}var Dn=Oc,Nc=Array.prototype,Mc=Nc.splice;function Ic(t){var e=this.__data__,r=Dn(e,t);if(r<0)return!1;var a=e.length-1;return r==a?e.pop():Mc.call(e,r,1),--this.size,!0}var Rc=Ic;function kc(t){var e=this.__data__,r=Dn(e,t);return r<0?void 0:e[r][1]}var Pc=kc;function Bc(t){return Dn(this.__data__,t)>-1}var Fc=Bc;function zc(t,e){var r=this.__data__,a=Dn(r,t);return a<0?(++this.size,r.push([t,e])):r[a][1]=e,this}var Gc=zc;function ua(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}ua.prototype.clear=Lc,ua.prototype.delete=Rc,ua.prototype.get=Pc,ua.prototype.has=Fc,ua.prototype.set=Gc;var $c=ua,Vc=Ei(ln,"Map"),_c=Vc;function Uc(){this.size=0,this.__data__={hash:new Js,map:new(_c||$c),string:new Js}}var Yc=Uc;function Hc(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}var Xc=Hc;function Wc(t,e){var r=t.__data__;return Xc(e)?r[typeof e=="string"?"string":"hash"]:r.map}var Sn=Wc;function qc(t){var e=Sn(this,t).delete(t);return this.size-=e?1:0,e}var Kc=qc;function Zc(t){return Sn(this,t).get(t)}var Qc=Zc;function Jc(t){return Sn(this,t).has(t)}var jc=Jc;function ev(t,e){var r=Sn(this,t),a=r.size;return r.set(t,e),this.size+=r.size==a?0:1,this}var tv=ev;function la(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}la.prototype.clear=Yc,la.prototype.delete=Kc,la.prototype.get=Qc,la.prototype.has=jc,la.prototype.set=tv;var eo=la,rv="Expected a function";function wi(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(rv);var r=function(){var a=arguments,n=e?e.apply(this,a):a[0],i=r.cache;if(i.has(n))return i.get(n);var s=t.apply(this,a);return r.cache=i.set(n,s)||i,s};return r.cache=new(wi.Cache||eo),r}wi.Cache=eo;var av=wi,nv=500;function iv(t){var e=av(t,function(a){return r.size===nv&&r.clear(),a}),r=e.cache;return e}var sv=iv,ov=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,uv=/\\(\\)?/g,lv=sv(function(t){var e=[];return t.charCodeAt(0)===46&&e.push(""),t.replace(ov,function(r,a,n,i){e.push(n?i.replace(uv,"$1"):a||r)}),e}),to=lv;function fv(t,e){for(var r=-1,a=t==null?0:t.length,n=Array(a);++r<a;)n[r]=e(t[r],r,t);return n}var ro=fv,hv=1/0,ao=Qr?Qr.prototype:void 0,no=ao?ao.toString:void 0;function io(t){if(typeof t=="string")return t;if(Cn(t))return ro(t,io)+"";if(Oa(t))return no?no.call(t):"";var e=t+"";return e=="0"&&1/t==-hv?"-0":e}var cv=io;function vv(t){return t==null?"":cv(t)}var so=vv;function dv(t,e){return Cn(t)?t:Fh(t,e)?[t]:to(so(t))}var oo=dv,gv=1/0;function pv(t){if(typeof t=="string"||Oa(t))return t;var e=t+"";return e=="0"&&1/t==-gv?"-0":e}var xi=pv;function yv(t,e){e=oo(e,t);for(var r=0,a=e.length;t!=null&&r<a;)t=t[xi(e[r++])];return r&&r==a?t:void 0}var mv=yv;function bv(t,e,r){var a=t==null?void 0:mv(t,e);return a===void 0?r:a}var Ev=bv,wv=function(){try{var t=Ei(Object,"defineProperty");return t({},"",{}),t}catch{}}(),uo=wv;function xv(t,e,r){e=="__proto__"&&uo?uo(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}var Tv=xv,Cv=Object.prototype,Dv=Cv.hasOwnProperty;function Sv(t,e,r){var a=t[e];(!(Dv.call(t,e)&&js(a,r))||r===void 0&&!(e in t))&&Tv(t,e,r)}var Lv=Sv,Av=9007199254740991,Ov=/^(?:0|[1-9]\d*)$/;function Nv(t,e){var r=typeof t;return e=e??Av,!!e&&(r=="number"||r!="symbol"&&Ov.test(t))&&t>-1&&t%1==0&&t<e}var Mv=Nv;function Iv(t,e,r,a){if(!kr(t))return t;e=oo(e,t);for(var n=-1,i=e.length,s=i-1,o=t;o!=null&&++n<i;){var l=xi(e[n]),u=r;if(l==="__proto__"||l==="constructor"||l==="prototype")return t;if(n!=s){var f=o[l];u=a?a(f,l,o):void 0,u===void 0&&(u=kr(f)?f:Mv(e[n+1])?[]:{})}Lv(o,l,u),o=o[l]}return t}var Rv=Iv;function kv(t,e,r){return t==null?t:Rv(t,e,r)}var Pv=kv;function Bv(t,e){var r=-1,a=t.length;for(e||(e=Array(a));++r<a;)e[r]=t[r];return e}var Fv=Bv;function zv(t){return Cn(t)?ro(t,xi):Oa(t)?[t]:Fv(to(so(t)))}var Gv=zv,$v={data:function(e){var r={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=Ue({},r,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],f=l?o[0]:o;if(j(n)){var h=n.indexOf(".")!==-1,c=h&&Gv(n);if(s.allowGetting&&i===void 0){var d;return f&&(s.beforeGet(f),c&&f._private[s.field][n]===void 0?d=Ev(f._private[s.field],c):d=f._private[s.field][n]),d}else if(s.allowSetting&&i!==void 0){var v=!s.immutableKeys[n];if(v){var p=T({},n,i);s.beforeSet(o,p);for(var y=0,g=u.length;y<g;y++){var m=u[y];s.canSet(m)&&(c&&f._private[s.field][n]===void 0?Pv(m._private[s.field],c,i):m._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&L(n)){var b=n,E,N,A=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<A.length;x++){E=A[x],N=b[E];var I=!s.immutableKeys[E];if(I)for(var C=0;C<u.length;C++){var F=u[C];s.canSet(F)&&(F._private[s.field][E]=N)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&Y(n)){var z=n;o.on(s.bindingEvent,z)}else if(s.allowGetting&&n===void 0){var M;return f&&(s.beforeGet(f),M=f._private[s.field]),M}return o}},removeData:function(e){var r={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=Ue({},r,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(j(n)){for(var u=n.split(/\s+/),f=u.length,h=0;h<f;h++){var c=u[h];if(!Pe(c)){var d=!i.immutableKeys[c];if(d)for(var v=0,p=l.length;v<p;v++)l[v]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var y=0,g=l.length;y<g;y++)for(var m=l[y]._private[i.field],b=Object.keys(m),E=0;E<b.length;E++){var N=b[E],A=!i.immutableKeys[N];A&&(m[N]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},Vv={eventAliasesOn:function(e){var r=e;r.addListener=r.listen=r.bind=r.on,r.unlisten=r.unbind=r.off=r.removeListener,r.trigger=r.emit,r.pon=r.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new sa(function(o,l){var u=function(d){i.off.apply(i,h),o(d)},f=s.concat([u]),h=f.concat([]);i.on.apply(i,f)})}}},ht={};[Ih,$v,Vv].forEach(function(t){Ue(ht,t)});var _v={animate:ht.animate(),animation:ht.animation(),animated:ht.animated(),clearQueue:ht.clearQueue(),delay:ht.delay(),delayAnimation:ht.delayAnimation(),stop:ht.stop()},Ln={classes:function(e){var r=this;if(e===void 0){var a=[];return r[0]._private.classes.forEach(function(v){return a.push(v)}),a}else te(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new jr(e),s=0;s<r.length;s++){for(var o=r[s],l=o._private,u=l.classes,f=!1,h=0;h<e.length;h++){var c=e[h],d=u.has(c);if(!d){f=!0;break}}f||(f=u.size!==e.length),f&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),r},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var r=this[0];return r!=null&&r._private.classes.has(e)},toggleClass:function(e,r){te(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=r===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,f=!1,h=0;h<e.length;h++){var c=e[h],d=u.has(c),v=!1;r||n&&!d?(u.add(c),v=!0):(!r||n&&d)&&(u.delete(c),v=!0),!f&&v&&(i.push(l),f=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,r){var a=this;if(r==null)r=250;else if(r===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},r),a}};Ln.className=Ln.classNames=Ln.classes;var nt={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:dt,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};nt.variable="(?:[\\w-.]|(?:\\\\"+nt.metaChar+"))+",nt.className="(?:[\\w-]|(?:\\\\"+nt.metaChar+"))+",nt.value=nt.string+"|"+nt.number,nt.id=nt.variable,function(){var t,e,r;for(t=nt.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],nt.comparatorOp+="|@"+e;for(t=nt.comparatorOp.split("|"),r=0;r<t.length;r++)e=t[r],!(e.indexOf("!")>=0)&&e!=="="&&(nt.comparatorOp+="|\\!"+e)}();var pt=function(){return{checks:[]}},Be={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Ti=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(t,e){return ju(t.selector,e.selector)}),Uv=function(){for(var t={},e,r=0;r<Ti.length;r++)e=Ti[r],t[e.selector]=e.matches;return t}(),Yv=function(e,r){return Uv[e](r)},Hv="("+Ti.map(function(t){return t.selector}).join("|")+")",fa=function(e){return e.replace(new RegExp("\\\\("+nt.metaChar+")","g"),function(r,a){return a})},Dr=function(e,r,a){e[e.length-1]=a},Ci=[{name:"group",query:!0,regex:"("+nt.group+")",populate:function(e,r,a){var n=w(a,1),i=n[0];r.checks.push({type:Be.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:Hv,populate:function(e,r,a){var n=w(a,1),i=n[0];r.checks.push({type:Be.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+nt.id+")",populate:function(e,r,a){var n=w(a,1),i=n[0];r.checks.push({type:Be.ID,value:fa(i)})}},{name:"className",query:!0,regex:"\\.("+nt.className+")",populate:function(e,r,a){var n=w(a,1),i=n[0];r.checks.push({type:Be.CLASS,value:fa(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+nt.variable+")\\s*\\]",populate:function(e,r,a){var n=w(a,1),i=n[0];r.checks.push({type:Be.DATA_EXIST,field:fa(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+nt.variable+")\\s*("+nt.comparatorOp+")\\s*("+nt.value+")\\s*\\]",populate:function(e,r,a){var n=w(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+nt.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),r.checks.push({type:Be.DATA_COMPARE,field:fa(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+nt.boolOp+")\\s*("+nt.variable+")\\s*\\]",populate:function(e,r,a){var n=w(a,2),i=n[0],s=n[1];r.checks.push({type:Be.DATA_BOOL,field:fa(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+nt.meta+")\\s*("+nt.comparatorOp+")\\s*("+nt.number+")\\s*\\]\\]",populate:function(e,r,a){var n=w(a,3),i=n[0],s=n[1],o=n[2];r.checks.push({type:Be.META_COMPARE,field:fa(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:nt.separator,populate:function(e,r){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=pt();return o}},{name:"directedEdge",separator:!0,regex:nt.directedEdge,populate:function(e,r){if(e.currentSubject==null){var a=pt(),n=r,i=pt();return a.checks.push({type:Be.DIRECTED_EDGE,source:n,target:i}),Dr(e,r,a),e.edgeCount++,i}else{var s=pt(),o=r,l=pt();return s.checks.push({type:Be.NODE_SOURCE,source:o,target:l}),Dr(e,r,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:nt.undirectedEdge,populate:function(e,r){if(e.currentSubject==null){var a=pt(),n=r,i=pt();return a.checks.push({type:Be.UNDIRECTED_EDGE,nodes:[n,i]}),Dr(e,r,a),e.edgeCount++,i}else{var s=pt(),o=r,l=pt();return s.checks.push({type:Be.NODE_NEIGHBOR,node:o,neighbor:l}),Dr(e,r,s),l}}},{name:"child",separator:!0,regex:nt.child,populate:function(e,r){if(e.currentSubject==null){var a=pt(),n=pt(),i=e[e.length-1];return a.checks.push({type:Be.CHILD,parent:i,child:n}),Dr(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=pt(),o=e[e.length-1],l=pt(),u=pt(),f=pt(),h=pt();return s.checks.push({type:Be.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=r.checks,r.checks=[{type:Be.TRUE}],h.checks.push({type:Be.TRUE}),l.checks.push({type:Be.PARENT,parent:h,child:f}),Dr(e,o,s),e.currentSubject=u,e.compoundCount++,f}else{var c=pt(),d=pt(),v=[{type:Be.PARENT,parent:c,child:d}];return c.checks=r.checks,r.checks=v,e.compoundCount++,d}}},{name:"descendant",separator:!0,regex:nt.descendant,populate:function(e,r){if(e.currentSubject==null){var a=pt(),n=pt(),i=e[e.length-1];return a.checks.push({type:Be.DESCENDANT,ancestor:i,descendant:n}),Dr(e,r,a),e.compoundCount++,n}else if(e.currentSubject===r){var s=pt(),o=e[e.length-1],l=pt(),u=pt(),f=pt(),h=pt();return s.checks.push({type:Be.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=r.checks,r.checks=[{type:Be.TRUE}],h.checks.push({type:Be.TRUE}),l.checks.push({type:Be.ANCESTOR,ancestor:h,descendant:f}),Dr(e,o,s),e.currentSubject=u,e.compoundCount++,f}else{var c=pt(),d=pt(),v=[{type:Be.ANCESTOR,ancestor:c,descendant:d}];return c.checks=r.checks,r.checks=v,e.compoundCount++,d}}},{name:"subject",modifier:!0,regex:nt.subject,populate:function(e,r){if(e.currentSubject!=null&&e.currentSubject!==r)return ft("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=r;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===Be.DIRECTED_EDGE?n.type=Be.NODE_TARGET:i===Be.UNDIRECTED_EDGE&&(n.type=Be.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Ci.forEach(function(t){return t.regexObj=new RegExp("^"+t.regex)});var Xv=function(e){for(var r,a,n,i=0;i<Ci.length;i++){var s=Ci[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,r=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:r,match:a,name:n,remaining:e}},Wv=function(e){var r=e.match(/^\s+/);if(r){var a=r[0];e=e.substring(a.length)}return e},qv=function(e){var r=this,a=r.inputText=e,n=r[0]=pt();for(r.length=1,a=Wv(a);;){var i=Xv(a);if(i.expr==null)return ft("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(r,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=r[r.length-1];r.currentSubject!=null&&(l.subject=r.currentSubject),l.edgeCount=r.edgeCount,l.compoundCount=r.compoundCount;for(var u=0;u<r.length;u++){var f=r[u];if(f.compoundCount>0&&f.edgeCount>0)return ft("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(f.edgeCount>1)return ft("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;f.edgeCount===1&&ft("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},Kv=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(f){return f??""},r=function(f){return j(f)?'"'+f+'"':e(f)},a=function(f){return" "+f+" "},n=function(f,h){var c=f.type,d=f.value;switch(c){case Be.GROUP:{var v=e(d);return v.substring(0,v.length-1)}case Be.DATA_COMPARE:{var p=f.field,y=f.operator;return"["+p+a(e(y))+r(d)+"]"}case Be.DATA_BOOL:{var g=f.operator,m=f.field;return"["+e(g)+m+"]"}case Be.DATA_EXIST:{var b=f.field;return"["+b+"]"}case Be.META_COMPARE:{var E=f.operator,N=f.field;return"[["+N+a(e(E))+r(d)+"]]"}case Be.STATE:return d;case Be.ID:return"#"+d;case Be.CLASS:return"."+d;case Be.PARENT:case Be.CHILD:return i(f.parent,h)+a(">")+i(f.child,h);case Be.ANCESTOR:case Be.DESCENDANT:return i(f.ancestor,h)+" "+i(f.descendant,h);case Be.COMPOUND_SPLIT:{var A=i(f.left,h),x=i(f.subject,h),I=i(f.right,h);return A+(A.length>0?" ":"")+x+I}case Be.TRUE:return""}},i=function(f,h){return f.checks.reduce(function(c,d,v){return c+(h===f&&v===0?"$":"")+n(d,h)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},Zv={parse:qv,toString:Kv},lo=function(e,r,a){var n,i=j(e),s=R(e),o=j(a),l,u,f=!1,h=!1,c=!1;switch(r.indexOf("!")>=0&&(r=r.replace("!",""),h=!0),r.indexOf("@")>=0&&(r=r.replace("@",""),f=!0),(i||o||f)&&(l=!i&&!s?"":""+e,u=""+a),f&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),r){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return h&&(e!=null||!c)&&(n=!n),n},Qv=function(e,r){switch(r){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},Jv=function(e){return e!==void 0},Di=function(e,r){return e.data(r)},jv=function(e,r){return e[r]()},Ct=[],xt=function(e,r){return e.checks.every(function(a){return Ct[a.type](a,r)})};Ct[Be.GROUP]=function(t,e){var r=t.value;return r==="*"||r===e.group()},Ct[Be.STATE]=function(t,e){var r=t.value;return Yv(r,e)},Ct[Be.ID]=function(t,e){var r=t.value;return e.id()===r},Ct[Be.CLASS]=function(t,e){var r=t.value;return e.hasClass(r)},Ct[Be.META_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return lo(jv(e,r),a,n)},Ct[Be.DATA_COMPARE]=function(t,e){var r=t.field,a=t.operator,n=t.value;return lo(Di(e,r),a,n)},Ct[Be.DATA_BOOL]=function(t,e){var r=t.field,a=t.operator;return Qv(Di(e,r),a)},Ct[Be.DATA_EXIST]=function(t,e){var r=t.field;return t.operator,Jv(Di(e,r))},Ct[Be.UNDIRECTED_EDGE]=function(t,e){var r=t.nodes[0],a=t.nodes[1],n=e.source(),i=e.target();return xt(r,n)&&xt(a,i)||xt(a,n)&&xt(r,i)},Ct[Be.NODE_NEIGHBOR]=function(t,e){return xt(t.node,e)&&e.neighborhood().some(function(r){return r.isNode()&&xt(t.neighbor,r)})},Ct[Be.DIRECTED_EDGE]=function(t,e){return xt(t.source,e.source())&&xt(t.target,e.target())},Ct[Be.NODE_SOURCE]=function(t,e){return xt(t.source,e)&&e.outgoers().some(function(r){return r.isNode()&&xt(t.target,r)})},Ct[Be.NODE_TARGET]=function(t,e){return xt(t.target,e)&&e.incomers().some(function(r){return r.isNode()&&xt(t.source,r)})},Ct[Be.CHILD]=function(t,e){return xt(t.child,e)&&xt(t.parent,e.parent())},Ct[Be.PARENT]=function(t,e){return xt(t.parent,e)&&e.children().some(function(r){return xt(t.child,r)})},Ct[Be.DESCENDANT]=function(t,e){return xt(t.descendant,e)&&e.ancestors().some(function(r){return xt(t.ancestor,r)})},Ct[Be.ANCESTOR]=function(t,e){return xt(t.ancestor,e)&&e.descendants().some(function(r){return xt(t.descendant,r)})},Ct[Be.COMPOUND_SPLIT]=function(t,e){return xt(t.subject,e)&&xt(t.left,e)&&xt(t.right,e)},Ct[Be.TRUE]=function(){return!0},Ct[Be.COLLECTION]=function(t,e){var r=t.value;return r.has(e)},Ct[Be.FILTER]=function(t,e){var r=t.value;return r(e)};var ed=function(e){var r=this;if(r.length===1&&r[0].checks.length===1&&r[0].checks[0].type===Be.ID)return e.getElementById(r[0].checks[0].value).collection();var a=function(i){for(var s=0;s<r.length;s++){var o=r[s];if(xt(o,i))return!0}return!1};return r.text()==null&&(a=function(){return!0}),e.filter(a)},td=function(e){for(var r=this,a=0;a<r.length;a++){var n=r[a];if(xt(n,e))return!0}return!1},rd={matches:td,filter:ed},Sr=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||j(e)&&e.match(/^\s*$/)||(de(e)?this.addQuery({checks:[{type:Be.COLLECTION,value:e.collection()}]}):Y(e)?this.addQuery({checks:[{type:Be.FILTER,value:e}]}):j(e)?this.parse(e)||(this.invalid=!0):Tt("A selector must be created from a string; found "))},Lr=Sr.prototype;[Zv,rd].forEach(function(t){return Ue(Lr,t)}),Lr.text=function(){return this.inputText},Lr.size=function(){return this.length},Lr.eq=function(t){return this[t]},Lr.sameText=function(t){return!this.invalid&&!t.invalid&&this.text()===t.text()},Lr.addQuery=function(t){this[this.length++]=t},Lr.selector=Lr.toString;var Ar={allAre:function(e){var r=new Sr(e);return this.every(function(a){return r.matches(a)})},is:function(e){var r=new Sr(e);return this.some(function(a){return r.matches(a)})},some:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,r){for(var a=0;a<this.length;a++){var n=r?e.apply(r,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var r=this.length,a=e.length;return r!==a?!1:r===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(r){return e.hasElementWithId(r.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var r=this.neighborhood();return e.every(function(a){return r.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var r=this;return e.every(function(a){return r.hasElementWithId(a.id())})}};Ar.allAreNeighbours=Ar.allAreNeighbors,Ar.has=Ar.contains,Ar.equal=Ar.equals=Ar.same;var Zt=function(e,r){return function(n,i,s,o){var l=n,u=this,f;if(l==null?f="":de(l)&&l.length===1&&(f=l.id()),u.length===1&&f){var h=u[0]._private,c=h.traversalCache=h.traversalCache||{},d=c[r]=c[r]||[],v=Pr(f),p=d[v];return p||(d[v]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},ha={parent:function(e){var r=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&r.push(s)}return this.spawn(r,!0).filter(e)},parents:function(e){for(var r=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];r.push(i)}a=a.parent()}return this.spawn(r,!0).filter(e)},commonAncestors:function(e){for(var r,a=0;a<this.length;a++){var n=this[a],i=n.parents();r=r||i,r=r.intersect(i)}return r.filter(e)},orphans:function(e){return this.stdFilter(function(r){return r.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(r){return r.isChild()}).filter(e)},children:Zt(function(t){for(var e=[],r=0;r<this.length;r++)for(var a=this[r],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(t)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var r=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];r.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(r,!0).filter(e)}};function Si(t,e,r,a){for(var n=[],i=new jr,s=t.cy(),o=s.hasCompoundNodes(),l=0;l<t.length;l++){var u=t[l];r?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var f=n.shift();e(f),i.add(f.id()),o&&a(n,i,f)}return t}function fo(t,e,r){if(r.isParent())for(var a=r._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||t.push(i)}}ha.forEachDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Si(this,t,e,fo)};function ho(t,e,r){if(r.isChild()){var a=r._private.parent;e.has(a.id())||t.push(a)}}ha.forEachUp=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Si(this,t,e,ho)};function ad(t,e,r){ho(t,e,r),fo(t,e,r)}ha.forEachUpAndDown=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Si(this,t,e,ad)},ha.ancestors=ha.parents;var Va,co;Va=co={data:ht.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:ht.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:ht.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:ht.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:ht.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:ht.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}},Va.attr=Va.data,Va.removeAttr=Va.removeData;var nd=co,An={};function Li(t){return function(e){var r=this;if(e===void 0&&(e=!0),r.length!==0)if(r.isNode()&&!r.removed()){for(var a=0,n=r[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=t(n,o))}return a}else return}}Ue(An,{degree:Li(function(t,e){return e.source().same(e.target())?2:1}),indegree:Li(function(t,e){return e.target().same(t)?1:0}),outdegree:Li(function(t,e){return e.source().same(t)?1:0})});function ca(t,e){return function(r){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[t](r);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}Ue(An,{minDegree:ca("degree",function(t,e){return t<e}),maxDegree:ca("degree",function(t,e){return t>e}),minIndegree:ca("indegree",function(t,e){return t<e}),maxIndegree:ca("indegree",function(t,e){return t>e}),minOutdegree:ca("outdegree",function(t,e){return t<e}),maxOutdegree:ca("outdegree",function(t,e){return t>e})}),Ue(An,{totalDegree:function(e){for(var r=0,a=this.nodes(),n=0;n<a.length;n++)r+=a[n].degree(e);return r}});var rr,vo,go=function(e,r,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:r.x!=null?r.x-s.x:0,y:r.y!=null?r.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},po={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,r){go(e,r,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};rr=vo={position:ht.data(po),silentPosition:ht.data(Ue({},po,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,r){go(e,r,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,r){if(L(e))r?this.silentPosition(e):this.position(e);else if(Y(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(r?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,r,a){var n;if(L(e)?(n={x:R(e.x)?e.x:0,y:R(e.y)?e.y:0},a=r):j(e)&&R(r)&&(n={x:0,y:0},n[e]=r),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,r){return L(e)?this.shift(e,!0):j(e)&&R(r)&&this.shift(e,r,!0),this},renderedPosition:function(e,r){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=L(e)?e:void 0,l=o!==void 0||r!==void 0&&j(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var f=this[u];r!==void 0?f.position(e,(r-s[e])/i):o!==void 0&&f.position(ms(o,i,s))}else{var h=a.position();return o=gn(h,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,r){var a=this[0],n=this.cy(),i=L(e)?e:void 0,s=i!==void 0||r!==void 0&&j(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],f=o?u.parent():null,h=f&&f.length>0,c=h;h&&(f=f[0]);var d=c?f.position():{x:0,y:0};r!==void 0?u.position(e,r+d[e]):i!==void 0&&u.position({x:i.x+d.x,y:i.y+d.y})}else{var v=a.position(),p=o?a.parent():null,y=p&&p.length>0,g=y;y&&(p=p[0]);var m=g?p.position():{x:0,y:0};return i={x:v.x-m.x,y:v.y-m.y},e===void 0?i:i[e]}else if(!s)return;return this}},rr.modelPosition=rr.point=rr.position,rr.modelPositions=rr.points=rr.positions,rr.renderedPoint=rr.renderedPosition,rr.relativePoint=rr.relativePosition;var id=vo,va,Or;va=Or={},Or.renderedBoundingBox=function(t){var e=this.boundingBox(t),r=this.cy(),a=r.zoom(),n=r.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}},Or.dirtyCompoundBoundsCache=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(r){if(r.isParent()){var a=r._private;a.compoundBoundsClean=!1,a.bbCache=null,t||r.emitAndNotify("bounds")}}),this)},Or.updateCompoundBounds=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!t&&e.batching())return this;function r(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",f={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},h=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(h.w===0||h.h===0)&&(h={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},h.x1=c.x-h.w/2,h.x2=c.x+h.w/2,h.y1=c.y-h.h/2,h.y2=c.y+h.h/2);function d(C,F,z){var M=0,X=0,B=F+z;return C>0&&B>0&&(M=F/B*C,X=z/B*C),{biasDiff:M,biasComplementDiff:X}}function v(C,F,z,M){if(z.units==="%")switch(M){case"width":return C>0?z.pfValue*C:0;case"height":return F>0?z.pfValue*F:0;case"average":return C>0&&F>0?z.pfValue*(C+F)/2:0;case"min":return C>0&&F>0?C>F?z.pfValue*F:z.pfValue*C:0;case"max":return C>0&&F>0?C>F?z.pfValue*C:z.pfValue*F:0;default:return 0}else return z.units==="px"?z.pfValue:0}var p=f.width.left.value;f.width.left.units==="px"&&f.width.val>0&&(p=p*100/f.width.val);var y=f.width.right.value;f.width.right.units==="px"&&f.width.val>0&&(y=y*100/f.width.val);var g=f.height.top.value;f.height.top.units==="px"&&f.height.val>0&&(g=g*100/f.height.val);var m=f.height.bottom.value;f.height.bottom.units==="px"&&f.height.val>0&&(m=m*100/f.height.val);var b=d(f.width.val-h.w,p,y),E=b.biasDiff,N=b.biasComplementDiff,A=d(f.height.val-h.h,g,m),x=A.biasDiff,I=A.biasComplementDiff;o.autoPadding=v(h.w,h.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(h.w,f.width.val),c.x=(-E+h.x1+h.x2+N)/2,o.autoHeight=Math.max(h.h,f.height.val),c.y=(-x+h.y1+h.y2+I)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||t)&&(r(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Qt=function(e){return e===1/0||e===-1/0?0:e},ar=function(e,r,a,n,i){n-r===0||i-a===0||r==null||a==null||n==null||i==null||(e.x1=r<e.x1?r:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},$r=function(e,r){return r==null?e:ar(e,r.x1,r.y1,r.x2,r.y2)},_a=function(e,r,a){return tr(e,r,a)},On=function(e,r,a){if(!r.cy().headless()){var n=r._private,i=n.rstyle,s=i.arrowWidth/2,o=r.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var f=n.arrowBounds=n.arrowBounds||{},h=f[a]=f[a]||{};h.x1=l-s,h.y1=u-s,h.x2=l+s,h.y2=u+s,h.w=h.x2-h.x1,h.h=h.y2-h.y1,yn(h,1),ar(e,h.x1,h.y1,h.x2,h.y2)}}},Ai=function(e,r,a){if(!r.cy().headless()){var n;a?n=a+"-":n="";var i=r._private,s=i.rstyle,o=r.pstyle(n+"label").strValue;if(o){var l=r.pstyle("text-halign"),u=r.pstyle("text-valign"),f=_a(s,"labelWidth",a),h=_a(s,"labelHeight",a),c=_a(s,"labelX",a),d=_a(s,"labelY",a),v=r.pstyle(n+"text-margin-x").pfValue,p=r.pstyle(n+"text-margin-y").pfValue,y=r.isEdge(),g=r.pstyle(n+"text-rotation"),m=r.pstyle("text-outline-width").pfValue,b=r.pstyle("text-border-width").pfValue,E=b/2,N=r.pstyle("text-background-padding").pfValue,A=2,x=h,I=f,C=I/2,F=x/2,z,M,X,B;if(y)z=c-C,M=c+C,X=d-F,B=d+F;else{switch(l.value){case"left":z=c-I,M=c;break;case"center":z=c-C,M=c+C;break;case"right":z=c,M=c+I;break}switch(u.value){case"top":X=d-x,B=d;break;case"center":X=d-F,B=d+F;break;case"bottom":X=d,B=d+x;break}}z+=v-Math.max(m,E)-N-A,M+=v+Math.max(m,E)+N+A,X+=p-Math.max(m,E)-N-A,B+=p+Math.max(m,E)+N+A;var re=a||"main",q=i.labelBounds,Z=q[re]=q[re]||{};Z.x1=z,Z.y1=X,Z.x2=M,Z.y2=B,Z.w=M-z,Z.h=B-X;var ie=y&&g.strValue==="autorotate",ue=g.pfValue!=null&&g.pfValue!==0;if(ie||ue){var ge=ie?_a(i.rstyle,"labelAngle",a):g.pfValue,se=Math.cos(ge),ve=Math.sin(ge),ye=(z+M)/2,Te=(X+B)/2;if(!y){switch(l.value){case"left":ye=M;break;case"right":ye=z;break}switch(u.value){case"top":Te=B;break;case"bottom":Te=X;break}}var be=function(We,Re){return We=We-ye,Re=Re-Te,{x:We*se-Re*ve+ye,y:We*ve+Re*se+Te}},me=be(z,X),ae=be(z,B),xe=be(M,X),Ce=be(M,B);z=Math.min(me.x,ae.x,xe.x,Ce.x),M=Math.max(me.x,ae.x,xe.x,Ce.x),X=Math.min(me.y,ae.y,xe.y,Ce.y),B=Math.max(me.y,ae.y,xe.y,Ce.y)}var Oe=re+"Rot",Me=q[Oe]=q[Oe]||{};Me.x1=z,Me.y1=X,Me.x2=M,Me.y2=B,Me.w=M-z,Me.h=B-X,ar(e,z,X,M,B),ar(i.labelBounds.all,z,X,M,B)}return e}},sd=function(e,r){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=Yt(),o=e._private,l=e.isNode(),u=e.isEdge(),f,h,c,d,v,p,y=o.rstyle,g=l&&n?e.pstyle("bounds-expansion").pfValue:[0],m=function(Ge){return Ge.pstyle("display").value!=="none"},b=!n||m(e)&&(!u||m(e.source())&&m(e.target()));if(b){var E=0,N=0;n&&r.includeOverlays&&(E=e.pstyle("overlay-opacity").value,E!==0&&(N=e.pstyle("overlay-padding").value));var A=0,x=0;n&&r.includeUnderlays&&(A=e.pstyle("underlay-opacity").value,A!==0&&(x=e.pstyle("underlay-padding").value));var I=Math.max(N,x),C=0,F=0;if(n&&(C=e.pstyle("width").pfValue,F=C/2),l&&r.includeNodes){var z=e.position();v=z.x,p=z.y;var M=e.outerWidth(),X=M/2,B=e.outerHeight(),re=B/2;f=v-X,h=v+X,c=p-re,d=p+re,ar(s,f,c,h,d)}else if(u&&r.includeEdges)if(n&&!i){var q=e.pstyle("curve-style").strValue;if(f=Math.min(y.srcX,y.midX,y.tgtX),h=Math.max(y.srcX,y.midX,y.tgtX),c=Math.min(y.srcY,y.midY,y.tgtY),d=Math.max(y.srcY,y.midY,y.tgtY),f-=F,h+=F,c-=F,d+=F,ar(s,f,c,h,d),q==="haystack"){var Z=y.haystackPts;if(Z&&Z.length===2){if(f=Z[0].x,c=Z[0].y,h=Z[1].x,d=Z[1].y,f>h){var ie=f;f=h,h=ie}if(c>d){var ue=c;c=d,d=ue}ar(s,f-F,c-F,h+F,d+F)}}else if(q==="bezier"||q==="unbundled-bezier"||q==="segments"||q==="taxi"){var ge;switch(q){case"bezier":case"unbundled-bezier":ge=y.bezierPts;break;case"segments":case"taxi":ge=y.linePts;break}if(ge!=null)for(var se=0;se<ge.length;se++){var ve=ge[se];f=ve.x-F,h=ve.x+F,c=ve.y-F,d=ve.y+F,ar(s,f,c,h,d)}}}else{var ye=e.source(),Te=ye.position(),be=e.target(),me=be.position();if(f=Te.x,h=me.x,c=Te.y,d=me.y,f>h){var ae=f;f=h,h=ae}if(c>d){var xe=c;c=d,d=xe}f-=F,h+=F,c-=F,d+=F,ar(s,f,c,h,d)}if(n&&r.includeEdges&&u&&(On(s,e,"mid-source"),On(s,e,"mid-target"),On(s,e,"source"),On(s,e,"target")),n){var Ce=e.pstyle("ghost").value==="yes";if(Ce){var Oe=e.pstyle("ghost-offset-x").pfValue,Me=e.pstyle("ghost-offset-y").pfValue;ar(s,s.x1+Oe,s.y1+Me,s.x2+Oe,s.y2+Me)}}var He=o.bodyBounds=o.bodyBounds||{};Es(He,s),li(He,g),yn(He,1),n&&(f=s.x1,h=s.x2,c=s.y1,d=s.y2,ar(s,f-I,c-I,h+I,d+I));var We=o.overlayBounds=o.overlayBounds||{};Es(We,s),li(We,g),yn(We,1);var Re=o.labelBounds=o.labelBounds||{};Re.all!=null?Sf(Re.all):Re.all=Yt(),n&&r.includeLabels&&(r.includeMainLabels&&Ai(s,e,null),u&&(r.includeSourceLabels&&Ai(s,e,"source"),r.includeTargetLabels&&Ai(s,e,"target")))}return s.x1=Qt(s.x1),s.y1=Qt(s.y1),s.x2=Qt(s.x2),s.y2=Qt(s.y2),s.w=Qt(s.x2-s.x1),s.h=Qt(s.y2-s.y1),s.w>0&&s.h>0&&b&&(li(s,g),yn(s,1)),s},yo=function(e){var r=0,a=function(s){return(s?1:0)<<r++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n},mo=function(e){if(e.isEdge()){var r=e.source().position(),a=e.target().position(),n=function(s){return Math.round(s)};return Xl([n(r.x),n(r.y),n(a.x),n(a.y)])}else return 0},bo=function(e,r){var a=e._private,n,i=e.isEdge(),s=r==null?Eo:yo(r),o=s===Eo,l=mo(e),u=a.bbCachePosKey===l,f=r.useCache&&u,h=function(p){return p._private.bbCache==null||p._private.styleDirty},c=!f||h(e)||i&&h(e.source())||h(e.target());if(c?(u||e.recalculateRenderedStyle(f),n=sd(e,Ua),a.bbCache=n,a.bbCachePosKey=l):n=a.bbCache,!o){var d=e.isNode();n=Yt(),(r.includeNodes&&d||r.includeEdges&&!d)&&(r.includeOverlays?$r(n,a.overlayBounds):$r(n,a.bodyBounds)),r.includeLabels&&(r.includeMainLabels&&(!i||r.includeSourceLabels&&r.includeTargetLabels)?$r(n,a.labelBounds.all):(r.includeMainLabels&&$r(n,a.labelBounds.mainRot),r.includeSourceLabels&&$r(n,a.labelBounds.sourceRot),r.includeTargetLabels&&$r(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},Ua={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,useCache:!0},Eo=yo(Ua),wo=At(Ua);Or.boundingBox=function(t){var e;if(this.length===1&&this[0]._private.bbCache!=null&&!this[0]._private.styleDirty&&(t===void 0||t.useCache===void 0||t.useCache===!0))t===void 0?t=Ua:t=wo(t),e=bo(this[0],t);else{e=Yt(),t=t||Ua;var r=wo(t),a=this,n=a.cy(),i=n.styleEnabled();if(i)for(var s=0;s<a.length;s++){var o=a[s],l=o._private,u=mo(o),f=l.bbCachePosKey===u,h=r.useCache&&f&&!l.styleDirty;o.recalculateRenderedStyle(h)}this.updateCompoundBounds(!t.useCache);for(var c=0;c<a.length;c++){var d=a[c];$r(e,bo(d,r))}}return e.x1=Qt(e.x1),e.y1=Qt(e.y1),e.x2=Qt(e.x2),e.y2=Qt(e.y2),e.w=Qt(e.x2-e.x1),e.h=Qt(e.y2-e.y1),e},Or.dirtyBoundingBoxCache=function(){for(var t=0;t<this.length;t++){var e=this[t]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},Or.boundingBoxAt=function(t){var e=this.nodes(),r=this.cy(),a=r.hasCompoundNodes(),n=r.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),L(t)){var i=t;t=function(){return i}}var s=function(f,h){return f._private.bbAtOldPos=t(f,h)},o=function(f){return f._private.bbAtOldPos};r.startBatch(),e.forEach(s).silentPositions(t),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=Df(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),r.endBatch(),l},va.boundingbox=va.bb=va.boundingBox,va.renderedBoundingbox=va.renderedBoundingBox;var od=Or,Ya,Ha;Ya=Ha={};var xo=function(e){e.uppercaseName=Pt(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Pt(e.outerName),Ya[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},Ya["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-width").pfValue,u=2*a.padding();return o+l+u}else return 1},Ya["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},Ya["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};xo({name:"width"}),xo({name:"height"}),Ha.padding=function(){var t=this[0],e=t._private;return t.isParent()?(t.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:t.pstyle("padding").pfValue):t.pstyle("padding").pfValue},Ha.paddedHeight=function(){var t=this[0];return t.height()+2*t.padding()},Ha.paddedWidth=function(){var t=this[0];return t.width()+2*t.padding()};var ud=Ha,ld=function(e,r){if(e.isEdge())return r(e)},fd=function(e,r){if(e.isEdge()){var a=e.cy();return gn(r(e),a.zoom(),a.pan())}},hd=function(e,r){if(e.isEdge()){var a=e.cy(),n=a.pan(),i=a.zoom();return r(e).map(function(s){return gn(s,i,n)})}},cd=function(e){return e.renderer().getControlPoints(e)},vd=function(e){return e.renderer().getSegmentPoints(e)},dd=function(e){return e.renderer().getSourceEndpoint(e)},gd=function(e){return e.renderer().getTargetEndpoint(e)},pd=function(e){return e.renderer().getEdgeMidpoint(e)},To={controlPoints:{get:cd,mult:!0},segmentPoints:{get:vd,mult:!0},sourceEndpoint:{get:dd},targetEndpoint:{get:gd},midpoint:{get:pd}},yd=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},md=Object.keys(To).reduce(function(t,e){var r=To[e],a=yd(e);return t[e]=function(){return ld(this,r.get)},r.mult?t[a]=function(){return hd(this,r.get)}:t[a]=function(){return fd(this,r.get)},t},{}),bd=Ue({},id,od,ud,md);/*!
	  Event object based on jQuery events, MIT license

	  https://jquery.org/license/
	  https://tldrlegal.com/license/mit-license
	  https://github.com/jquery/jquery/blob/master/src/event.js
	  */var Co=function(e,r){this.recycle(e,r)};function Xa(){return!1}function Nn(){return!0}Co.prototype={instanceString:function(){return"event"},recycle:function(e,r){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Xa,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?Nn:Xa):e!=null&&e.type?r=e:this.type=e,r!=null&&(this.originalEvent=r.originalEvent,this.type=r.type!=null?r.type:this.type,this.cy=r.cy,this.target=r.target,this.position=r.position,this.renderedPosition=r.renderedPosition,this.namespace=r.namespace,this.layout=r.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=Nn;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=Nn;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Nn,this.stopPropagation()},isDefaultPrevented:Xa,isPropagationStopped:Xa,isImmediatePropagationStopped:Xa};var Do=/^([^.]+)(\.(?:[^.]+))?$/,Ed=".*",So={qualifierCompare:function(e,r){return e===r},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},Lo=Object.keys(So),wd={};function Mn(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:wd,e=arguments.length>1?arguments[1]:void 0,r=0;r<Lo.length;r++){var a=Lo[r];this[a]=t[a]||So[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var Nr=Mn.prototype,Ao=function(e,r,a,n,i,s,o){Y(n)&&(i=n,n=null),o&&(s==null?s=o:s=Ue({},s,o));for(var l=te(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var f=l[u];if(!Pe(f)){var h=f.match(Do);if(h){var c=h[1],d=h[2]?h[2]:null,v=r(e,f,c,d,n,i,s);if(v===!1)break}}}},Oo=function(e,r){return e.addEventFields(e.context,r),new Co(r.type,r)},xd=function(e,r,a){if(vt(a)){r(e,a);return}else if(L(a)){r(e,Oo(e,a));return}for(var n=te(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!Pe(s)){var o=s.match(Do);if(o){var l=o[1],u=o[2]?o[2]:null,f=Oo(e,{type:l,namespace:u,target:e.context});r(e,f)}}}};Nr.on=Nr.addListener=function(t,e,r,a,n){return Ao(this,function(i,s,o,l,u,f,h){Y(f)&&i.listeners.push({event:s,callback:f,type:o,namespace:l,qualifier:u,conf:h})},t,e,r,a,n),this},Nr.one=function(t,e,r,a){return this.on(t,e,r,a,{one:!0})},Nr.removeListener=Nr.off=function(t,e,r,a){var n=this;this.emitting!==0&&(this.listeners=Ql(this.listeners));for(var i=this.listeners,s=function(u){var f=i[u];Ao(n,function(h,c,d,v,p,y){if((f.type===d||t==="*")&&(!v&&f.namespace!==".*"||f.namespace===v)&&(!p||h.qualifierCompare(f.qualifier,p))&&(!y||f.callback===y))return i.splice(u,1),!1},t,e,r,a)},o=i.length-1;o>=0;o--)s(o);return this},Nr.removeAllListeners=function(){return this.removeListener("*")},Nr.emit=Nr.trigger=function(t,e,r){var a=this.listeners,n=a.length;return this.emitting++,te(e)||(e=[e]),xd(this,function(i,s){r!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:r}],n=a.length);for(var o=function(f){var h=a[f];if(h.type===s.type&&(!h.namespace||h.namespace===s.namespace||h.namespace===Ed)&&i.eventMatches(i.context,h,s)){var c=[s];e!=null&&jl(c,e),i.beforeEmit(i.context,h,s),h.conf&&h.conf.one&&(i.listeners=i.listeners.filter(function(p){return p!==h}));var d=i.callbackContext(i.context,h,s),v=h.callback.apply(d,c);i.afterEmit(i.context,h,s),v===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o(l);i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},t),this.emitting--,this};var Td={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ae(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e.cy(),r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e},beforeEmit:function(e,r){r.conf&&r.conf.once&&r.conf.onceCollection.removeListener(r.event,r.qualifier,r.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},In=function(e){return j(e)?new Sr(e):e},No={createEmitter:function(){for(var e=0;e<this.length;e++){var r=this[e],a=r._private;a.emitter||(a.emitter=new Mn(Td,r))}return this},emitter:function(){return this._private.emitter},on:function(e,r,a){for(var n=In(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,r,a){for(var n=In(r),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var r=this[e];r.emitter().removeAllListeners()}return this},one:function(e,r,a){for(var n=In(r),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,r,a){for(var n=In(r),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,r){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,r)}return this},emitAndNotify:function(e,r){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,r),this}};ht.eventAliasesOn(No);var Mo={nodes:function(e){return this.filter(function(r){return r.isNode()}).filter(e)},edges:function(e){return this.filter(function(r){return r.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),r=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):r.push(n)}return{nodes:e,edges:r}},filter:function(e,r){if(e===void 0)return this;if(j(e)||de(e))return new Sr(e).filter(this);if(Y(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){j(e)&&(e=this.filter(e));for(var r=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||r.push(n)}return r}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(j(e)){var r=e;return this.filter(r)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var f=o[u];l.has(f)&&a.push(f)}return a},xor:function(e){var r=this._private.cy;j(e)&&(e=r.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var f=0;f<l.length;f++){var h=l[f],c=h._private.data.id,d=u.hasElementWithId(c);d||a.push(h)}};return s(n,i),s(i,n),a},diff:function(e){var r=this._private.cy;j(e)&&(e=r.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(f,h,c){for(var d=0;d<f.length;d++){var v=f[d],p=v._private.data.id,y=h.hasElementWithId(p);y?i.merge(v):c.push(v)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var r=this._private.cy;if(!e)return this;if(j(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var r=this._private,a=r.cy;if(!e)return this;if(e&&j(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=r.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var f=this.length++;this[f]=o,i.set(l,{ele:o,index:f})}}return this},unmergeAt:function(e){var r=this[e],a=r.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var r=this._private,a=e._private.data.id,n=r.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var r=this._private.cy;if(!e)return this;if(e&&j(e)){var a=e;e=r.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var r=this.length-1;r>=0;r--){var a=this[r];e(a)&&this.unmergeAt(r)}return this},map:function(e,r){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=r?e.apply(r,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,r){for(var a=r,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,r){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=r?e.apply(r,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,r){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=r?e.apply(r,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},it=Mo;it.u=it["|"]=it["+"]=it.union=it.or=it.add,it["\\"]=it["!"]=it["-"]=it.difference=it.relativeComplement=it.subtract=it.not,it.n=it["&"]=it["."]=it.and=it.intersection=it.intersect,it["^"]=it["(+)"]=it["(-)"]=it.symmetricDifference=it.symdiff=it.xor,it.fnFilter=it.filterFn=it.stdFilter=it.filter,it.complement=it.abscomp=it.absoluteComplement;var Cd={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},Io=function(e,r){var a=e.cy(),n=a.hasCompoundNodes();function i(f){var h=f.pstyle("z-compound-depth");return h.value==="auto"?n?f.zDepth():0:h.value==="bottom"?-1:h.value==="top"?ni:0}var s=i(e)-i(r);if(s!==0)return s;function o(f){var h=f.pstyle("z-index-compare");return h.value==="auto"&&f.isNode()?1:0}var l=o(e)-o(r);if(l!==0)return l;var u=e.pstyle("z-index").value-r.pstyle("z-index").value;return u!==0?u:e.poolIndex()-r.poolIndex()},Rn={forEach:function(e,r){if(Y(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=r?e.apply(r,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],r=0;r<this.length;r++)e.push(this[r]);return e},slice:function(e,r){var a=[],n=this.length;r==null&&(r=n),e==null&&(e=0),e<0&&(e=n+e),r<0&&(r=n+r);for(var i=e;i>=0&&i<r&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!Y(e))return this;var r=this.toArray().sort(e);return this.spawn(r)},sortByZIndex:function(){return this.sort(Io)},zDepth:function(){var e=this[0];if(e){var r=e._private,a=r.group;if(a==="nodes"){var n=r.data.parent?e.parents().size():0;return e.isParent()?n:ni-1}else{var i=r.source,s=r.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};Rn.each=Rn.forEach;var Dd=function(){var e="undefined",r=(typeof Symbol>"u"?"undefined":ee(Symbol))!=e&&ee(Symbol.iterator)!=e;r&&(Rn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return T({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};Dd();var Sd=At({nodeDimensionsIncludeLabels:!1}),kn={layoutDimensions:function(e){e=Sd(e);var r;if(!this.takesUpSpace())r={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();r={w:a.w,h:a.h}}else r={w:this.outerWidth(),h:this.outerHeight()};return(r.w===0||r.h===0)&&(r.w=r.h=1),r},layoutPositions:function(e,r,a){var n=this.nodes().filter(function(N){return!N.isParent()}),i=this.cy(),s=r.eles,o=function(A){return A.id()},l=at(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(A,x,I){var C={x:x.x1+x.w/2,y:x.y1+x.h/2},F={x:(I.x-C.x)*A,y:(I.y-C.y)*A};return{x:C.x+F.x,y:C.y+F.y}},f=r.spacingFactor&&r.spacingFactor!==1,h=function(){if(!f)return null;for(var A=Yt(),x=0;x<n.length;x++){var I=n[x],C=l(I,x);Af(A,C.x,C.y)}return A},c=h(),d=at(function(N,A){var x=l(N,A);if(f){var I=Math.abs(r.spacingFactor);x=u(I,c,x)}return r.transform!=null&&(x=r.transform(N,x)),x},o);if(r.animate){for(var v=0;v<n.length;v++){var p=n[v],y=d(p,v),g=r.animateFilter==null||r.animateFilter(p,v);if(g){var m=p.animation({position:y,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(m)}else p.position(y)}if(r.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(d),padding:r.padding},duration:r.animationDuration,easing:r.animationEasing});e.animations.push(b)}else if(r.zoom!==void 0&&r.pan!==void 0){var E=i.animation({zoom:r.zoom,pan:r.pan,duration:r.animationDuration,easing:r.animationEasing});e.animations.push(E)}e.animations.forEach(function(N){return N.play()}),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),sa.all(e.animations.map(function(N){return N.promise()})).then(function(){e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(d),r.fit&&i.fit(r.eles,r.padding),r.zoom!=null&&i.zoom(r.zoom),r.pan&&i.pan(r.pan),e.one("layoutready",r.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",r.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var r=this.cy();return r.makeLayout(Ue({},e,{eles:this}))}};kn.createLayout=kn.makeLayout=kn.layout;function Ro(t,e,r){var a=r._private,n=a.styleCache=a.styleCache||[],i;return(i=n[t])!=null||(i=n[t]=e(r)),i}function Pn(t,e){return t=Pr(t),function(a){return Ro(t,e,a)}}function Bn(t,e){t=Pr(t);var r=function(n){return e.call(n)};return function(){var n=this[0];if(n)return Ro(t,r,n)}}var Ot={recalculateRenderedStyle:function(e){var r=this.cy(),a=r.renderer(),n=r.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),r=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(r)}else this.forEach(function(n){r(n),n.connectedEdges().forEach(r)});return this},updateStyle:function(e){var r=this._private.cy;if(!r.styleEnabled())return this;if(r.batching()){var a=r._private.batchStyleEles;return a.merge(this),this}var n=r.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var r=0;r<this.length;r++){var a=this[r];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){this.cleanStyle();var i=a._private.style[e];return i??(r?n.style().getDefaultProperty(e):null)}},numericStyle:function(e){var r=this[0];if(r.cy().styleEnabled()&&r){var a=r.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var r=this[0];if(r.cy().styleEnabled()&&r)return r.pstyle(e).units},renderedStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=this[0];if(a)return r.style().getRenderedStyle(a,e)},style:function(e,r){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(L(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(j(e))if(r===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,r,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var r=this.cy();if(!r.styleEnabled())return this;var a=!1,n=r.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var r=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!r)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0],a=r.cy().hasCompoundNodes();if(r)return a?r.effectiveOpacity()===0:r.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var r=this[0];return!!r._private.backgrounding}};function Oi(t,e){var r=t._private,a=r.data.parent?t.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function Ni(t){var e=t.ok,r=t.edgeOkViaNode||t.ok,a=t.parentOk||t.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||Oi(i,a);var l=o.source,u=o.target;return r(l)&&(!s||Oi(l,r))&&(l===u||r(u)&&(!s||Oi(u,r)))}}}var da=Pn("eleTakesUpSpace",function(t){return t.pstyle("display").value==="element"&&t.width()!==0&&(t.isNode()?t.height()!==0:!0)});Ot.takesUpSpace=Bn("takesUpSpace",Ni({ok:da}));var Ld=Pn("eleInteractive",function(t){return t.pstyle("events").value==="yes"&&t.pstyle("visibility").value==="visible"&&da(t)}),Ad=Pn("parentInteractive",function(t){return t.pstyle("visibility").value==="visible"&&da(t)});Ot.interactive=Bn("interactive",Ni({ok:Ld,parentOk:Ad,edgeOkViaNode:da})),Ot.noninteractive=function(){var t=this[0];if(t)return!t.interactive()};var Od=Pn("eleVisible",function(t){return t.pstyle("visibility").value==="visible"&&t.pstyle("opacity").pfValue!==0&&da(t)}),Nd=da;Ot.visible=Bn("visible",Ni({ok:Od,edgeOkViaNode:Nd})),Ot.hidden=function(){var t=this[0];if(t)return!t.visible()},Ot.isBundledBezier=Bn("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1}),Ot.bypass=Ot.css=Ot.style,Ot.renderedCss=Ot.renderedStyle,Ot.removeBypass=Ot.removeCss=Ot.removeStyle,Ot.pstyle=Ot.parsedStyle;var Mr={};function ko(t){return function(){var e=arguments,r=[];if(e.length===2){var a=e[0],n=e[1];this.on(t.event,a,n)}else if(e.length===1&&Y(e[0])){var i=e[0];this.on(t.event,i)}else if(e.length===0||e.length===1&&te(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!t.ableField||l._private[t.ableField],f=l._private[t.field]!=t.value;if(t.overrideAble){var h=t.overrideAble(l);if(h!==void 0&&(u=h,!h))return this}u&&(l._private[t.field]=t.value,f&&r.push(l))}var c=this.spawn(r);c.updateStyle(),c.emit(t.event),s&&c.emit(s)}return this}}function ga(t){Mr[t.field]=function(){var e=this[0];if(e){if(t.overrideField){var r=t.overrideField(e);if(r!==void 0)return r}return e._private[t.field]}},Mr[t.on]=ko({event:t.on,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!0}),Mr[t.off]=ko({event:t.off,field:t.field,ableField:t.ableField,overrideAble:t.overrideAble,value:!1})}ga({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"}),ga({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"}),ga({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"}),ga({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"}),Mr.deselect=Mr.unselect,Mr.grabbed=function(){var t=this[0];if(t)return t._private.grabbed},ga({field:"active",on:"activate",off:"unactivate"}),ga({field:"pannable",on:"panify",off:"unpanify"}),Mr.inactive=function(){var t=this[0];if(t)return!t._private.active};var Bt={},Po=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),f=0;f<u.length;f++){var h=u[f],c=h.source(),d=h.target();if(e.noIncomingEdges&&d===o&&c!==o||e.noOutgoingEdges&&c===o&&d!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},Bo=function(e){return function(r){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],f=u.source(),h=u.target();e.outgoing&&f===s?(n.push(u),n.push(h)):e.incoming&&h===s&&(n.push(u),n.push(f))}}return this.spawn(n,!0).filter(r)}},Fo=function(e){return function(r){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],f=u.id();i[f]||(i[f]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(r)}};Bt.clearTraversalCache=function(){for(var t=0;t<this.length;t++)this[t]._private.traversalCache=null},Ue(Bt,{roots:Po({noIncomingEdges:!0}),leaves:Po({noOutgoingEdges:!0}),outgoers:Zt(Bo({outgoing:!0}),"outgoers"),successors:Fo({outgoing:!0}),incomers:Zt(Bo({incoming:!0}),"incomers"),predecessors:Fo({incoming:!0})}),Ue(Bt,{neighborhood:Zt(function(t){for(var e=[],r=this.nodes(),a=0;a<r.length;a++)for(var n=r[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),f=n===l?u:l;f.length>0&&e.push(f[0]),e.push(o[0])}return this.spawn(e,!0).filter(t)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),Bt.neighbourhood=Bt.neighborhood,Bt.closedNeighbourhood=Bt.closedNeighborhood,Bt.openNeighbourhood=Bt.openNeighborhood,Ue(Bt,{source:Zt(function(e){var r=this[0],a;return r&&(a=r._private.source||r.cy().collection()),a&&e?a.filter(e):a},"source"),target:Zt(function(e){var r=this[0],a;return r&&(a=r._private.target||r.cy().collection()),a&&e?a.filter(e):a},"target"),sources:zo({attr:"source"}),targets:zo({attr:"target"})});function zo(t){return function(r){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[t.attr];s&&a.push(s)}return this.spawn(a,!0).filter(r)}}Ue(Bt,{edgesWith:Zt(Go(),"edgesWith"),edgesTo:Zt(Go({thisIsSrc:!0}),"edgesTo")});function Go(t){return function(r){var a=[],n=this._private.cy,i=t||{};j(r)&&(r=n.$(r));for(var s=0;s<r.length;s++)for(var o=r[s]._private.edges,l=0;l<o.length;l++){var u=o[l],f=u._private.data,h=this.hasElementWithId(f.source)&&r.hasElementWithId(f.target),c=r.hasElementWithId(f.source)&&this.hasElementWithId(f.target),d=h||c;d&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!h||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}Ue(Bt,{connectedEdges:Zt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(t)},"connectedEdges"),connectedNodes:Zt(function(t){for(var e=[],r=this,a=0;a<r.length;a++){var n=r[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(t)},"connectedNodes"),parallelEdges:Zt($o(),"parallelEdges"),codirectedEdges:Zt($o({codirected:!0}),"codirectedEdges")});function $o(t){var e={codirected:!1};return t=Ue({},e,t),function(a){for(var n=[],i=this.edges(),s=t,o=0;o<i.length;o++)for(var l=i[o],u=l._private,f=u.source,h=f._private.data.id,c=u.data.target,d=f._private.edges,v=0;v<d.length;v++){var p=d[v],y=p._private.data,g=y.target,m=y.source,b=g===c&&m===h,E=h===g&&c===m;(s.codirected&&b||!s.codirected&&(b||E))&&n.push(p)}return this.spawn(n,!0).filter(a)}}Ue(Bt,{components:function(e){var r=this,a=r.cy(),n=a.collection(),i=e==null?r.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(f,h){n.merge(f),i.unmerge(f),h.merge(f)};if(i.empty())return r.spawn();var l=function(){var f=a.collection();s.push(f);var h=i[0];o(h,f),r.bfs({directed:!1,roots:h,visit:function(d){return o(d,f)}}),f.forEach(function(c){c.connectedEdges().forEach(function(d){r.has(d)&&f.has(d.source())&&f.has(d.target())&&f.merge(d)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),Bt.componentsOf=Bt.components;var Nt=function(e,r){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){Tt("A collection must have a reference to the core");return}var i=new lr,s=!1;if(!r)r=[];else if(r.length>0&&L(r[0])&&!Ae(r[0])){s=!0;for(var o=[],l=new jr,u=0,f=r.length;u<f;u++){var h=r[u];h.data==null&&(h.data={});var c=h.data;if(c.id==null)c.id=gs();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var d=new dn(e,h,!1);o.push(d),l.add(c.id)}r=o}this.length=0;for(var v=0,p=r.length;v<p;v++){var y=r[v][0];if(y!=null){var g=y._private.data.id;(!a||!i.has(g))&&(a&&i.set(g,{index:this.length,ele:y}),this[this.length]=y,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var b=this.lazyMap=new lr,E=this.eles,N=0;N<E.length;N++){var A=E[N];b.set(A.id(),{index:N,ele:A})}}},a&&(this._private.map=i),s&&!n&&this.restore()},bt=dn.prototype=Nt.prototype=Object.create(Array.prototype);bt.instanceString=function(){return"collection"},bt.spawn=function(t,e){return new Nt(this.cy(),t,e)},bt.spawnSelf=function(){return this.spawn(this)},bt.cy=function(){return this._private.cy},bt.renderer=function(){return this._private.cy.renderer()},bt.element=function(){return this[0]},bt.collection=function(){return Ne(this)?this:new Nt(this._private.cy,[this])},bt.unique=function(){return new Nt(this._private.cy,this,!0)},bt.hasElementWithId=function(t){return t=""+t,this._private.map.has(t)},bt.getElementById=function(t){t=""+t;var e=this._private.cy,r=this._private.map.get(t);return r?r.ele:new Nt(e)},bt.$id=bt.getElementById,bt.poolIndex=function(){var t=this._private.cy,e=t._private.elements,r=this[0]._private.data.id;return e._private.map.get(r).index},bt.indexOf=function(t){var e=t[0]._private.data.id;return this._private.map.get(e).index},bt.indexOfId=function(t){return t=""+t,this._private.map.get(t).index},bt.json=function(t){var e=this.element(),r=this.cy();if(e==null&&t)return this;if(e!=null){var a=e._private;if(L(t)){if(r.startBatch(),t.data){e.data(t.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=t.data.source,l=t.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in t.data,f=t.data.parent;u&&(f!=null||n.parent!=null)&&f!=n.parent&&(f===void 0&&(f=null),f!=null&&(f=""+f),e=e.move({parent:f}))}}t.position&&e.position(t.position);var h=function(p,y,g){var m=t[p];m!=null&&m!==a[p]&&(m?e[y]():e[g]())};return h("removed","remove","restore"),h("selected","select","unselect"),h("selectable","selectify","unselectify"),h("locked","lock","unlock"),h("grabbable","grabify","ungrabify"),h("pannable","panify","unpanify"),t.classes!=null&&e.classes(t.classes),r.endBatch(),this}else if(t===void 0){var c={data:ur(a.data),position:ur(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var d=0;return a.classes.forEach(function(v){return c.classes+=d++===0?v:" "+v}),c}}},bt.jsons=function(){for(var t=[],e=0;e<this.length;e++){var r=this[e],a=r.json();t.push(a)}return t},bt.clone=function(){for(var t=this.cy(),e=[],r=0;r<this.length;r++){var a=this[r],n=a.json(),i=new dn(t,n,!1);e.push(i)}return new Nt(t,e)},bt.copy=bt.clone,bt.restore=function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=r.cy(),n=a._private,i=[],s=[],o,l=0,u=r.length;l<u;l++){var f=r[l];e&&!f.removed()||(f.isNode()?i.push(f):s.push(f))}o=i.concat(s);var h,c=function(){o.splice(h,1),h--};for(h=0;h<o.length;h++){var d=o[h],v=d._private,p=v.data;if(d.clearTraversalCache(),!(!e&&!v.removed)){if(p.id===void 0)p.id=gs();else if(R(p.id))p.id=""+p.id;else if(Pe(p.id)||!j(p.id)){Tt("Can not create element with invalid string ID `"+p.id+"`"),c();continue}else if(a.hasElementWithId(p.id)){Tt("Can not create second element with ID `"+p.id+"`"),c();continue}}var y=p.id;if(d.isNode()){var g=v.position;g.x==null&&(g.x=0),g.y==null&&(g.y=0)}if(d.isEdge()){for(var m=d,b=["source","target"],E=b.length,N=!1,A=0;A<E;A++){var x=b[A],I=p[x];R(I)&&(I=p[x]=""+p[x]),I==null||I===""?(Tt("Can not create edge `"+y+"` with unspecified "+x),N=!0):a.hasElementWithId(I)||(Tt("Can not create edge `"+y+"` with nonexistant "+x+" `"+I+"`"),N=!0)}if(N){c();continue}var C=a.getElementById(p.source),F=a.getElementById(p.target);C.same(F)?C._private.edges.push(m):(C._private.edges.push(m),F._private.edges.push(m)),m._private.source=C,m._private.target=F}v.map=new lr,v.map.set(y,{ele:d,index:0}),v.removed=!1,e&&a.addToPool(d)}for(var z=0;z<i.length;z++){var M=i[z],X=M._private.data;R(X.parent)&&(X.parent=""+X.parent);var B=X.parent,re=B!=null;if(re||M._private.parent){var q=M._private.parent?a.collection().merge(M._private.parent):a.getElementById(B);if(q.empty())X.parent=void 0;else if(q[0].removed())ft("Node added with missing parent, reference to parent removed"),X.parent=void 0,M._private.parent=null;else{for(var Z=!1,ie=q;!ie.empty();){if(M.same(ie)){Z=!0,X.parent=void 0;break}ie=ie.parent()}Z||(q[0]._private.children.push(M),M._private.parent=q[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var ue=o.length===r.length?r:new Nt(a,o),ge=0;ge<ue.length;ge++){var se=ue[ge];se.isNode()||(se.parallelEdges().clearTraversalCache(),se.source().clearTraversalCache(),se.target().clearTraversalCache())}var ve;n.hasCompoundNodes?ve=a.collection().merge(ue).merge(ue.connectedNodes()).merge(ue.parent()):ve=ue,ve.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(t),t?ue.emitAndNotify("add"):e&&ue.emit("add")}return r},bt.removed=function(){var t=this[0];return t&&t._private.removed},bt.inside=function(){var t=this[0];return t&&!t._private.removed},bt.remove=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,r=this,a=[],n={},i=r._private.cy;function s(B){for(var re=B._private.edges,q=0;q<re.length;q++)l(re[q])}function o(B){for(var re=B._private.children,q=0;q<re.length;q++)l(re[q])}function l(B){var re=n[B.id()];e&&B.removed()||re||(n[B.id()]=!0,B.isNode()?(a.push(B),s(B),o(B)):a.unshift(B))}for(var u=0,f=r.length;u<f;u++){var h=r[u];l(h)}function c(B,re){var q=B._private.edges;xr(q,re),B.clearTraversalCache()}function d(B){B.clearTraversalCache()}var v=[];v.ids={};function p(B,re){re=re[0],B=B[0];var q=B._private.children,Z=B.id();xr(q,re),re._private.parent=null,v.ids[Z]||(v.ids[Z]=!0,v.push(B))}r.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var y=0;y<a.length;y++){var g=a[y];if(g.isEdge()){var m=g.source()[0],b=g.target()[0];c(m,g),c(b,g);for(var E=g.parallelEdges(),N=0;N<E.length;N++){var A=E[N];d(A),A.isBundledBezier()&&A.dirtyBoundingBoxCache()}}else{var x=g.parent();x.length!==0&&p(x,g)}e&&(g._private.removed=!0)}var I=i._private.elements;i._private.hasCompoundNodes=!1;for(var C=0;C<I.length;C++){var F=I[C];if(F.isParent()){i._private.hasCompoundNodes=!0;break}}var z=new Nt(this.cy(),a);z.size()>0&&(t?z.emitAndNotify("remove"):e&&z.emit("remove"));for(var M=0;M<v.length;M++){var X=v[M];(!e||!X.removed())&&X.updateStyle()}return z},bt.move=function(t){var e=this._private.cy,r=this,a=!1,n=!1,i=function(v){return v==null?v:""+v};if(t.source!==void 0||t.target!==void 0){var s=i(t.source),o=i(t.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){r.remove(a,n),r.emitAndNotify("moveout");for(var d=0;d<r.length;d++){var v=r[d],p=v._private.data;v.isEdge()&&(l&&(p.source=s),u&&(p.target=o))}r.restore(a,n)}),r.emitAndNotify("move"))}else if(t.parent!==void 0){var f=i(t.parent),h=f===null||e.hasElementWithId(f);if(h){var c=f===null?void 0:f;e.batch(function(){var d=r.remove(a,n);d.emitAndNotify("moveout");for(var v=0;v<r.length;v++){var p=r[v],y=p._private.data;p.isNode()&&(y.parent=c)}d.restore(a,n)}),r.emitAndNotify("move")}}return this},[Us,_v,Ln,Ar,ha,nd,An,bd,No,Mo,Cd,Rn,kn,Ot,Mr,Bt].forEach(function(t){Ue(bt,t)});var Md={add:function(e){var r,a=this;if(de(e)){var n=e;if(n._private.cy===a)r=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}r=new Nt(a,i)}}else if(te(e)){var l=e;r=new Nt(a,l)}else if(L(e)&&(te(e.nodes)||te(e.edges))){for(var u=e,f=[],h=["nodes","edges"],c=0,d=h.length;c<d;c++){var v=h[c],p=u[v];if(te(p))for(var y=0,g=p.length;y<g;y++){var m=Ue({group:v},p[y]);f.push(m)}}r=new Nt(a,f)}else{var b=e;r=new dn(a,b).collection()}return r},remove:function(e){if(!de(e)){if(j(e)){var r=e;e=this.$(r)}}return e.remove()}};/*! Bezier curve function generator. Copyright Gaetan Renaudeau. MIT License: http://en.wikipedia.org/wiki/MIT_License */function Id(t,e,r,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),f=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var h=0;h<4;++h)if(typeof arguments[h]!="number"||isNaN(arguments[h])||!isFinite(arguments[h]))return!1;t=Math.min(t,1),r=Math.min(r,1),t=Math.max(t,0),r=Math.max(r,0);var c=f?new Float32Array(l):new Array(l);function d(F,z){return 1-3*z+3*F}function v(F,z){return 3*z-6*F}function p(F){return 3*F}function y(F,z,M){return((d(z,M)*F+v(z,M))*F+p(z))*F}function g(F,z,M){return 3*d(z,M)*F*F+2*v(z,M)*F+p(z)}function m(F,z){for(var M=0;M<n;++M){var X=g(z,t,r);if(X===0)return z;var B=y(z,t,r)-F;z-=B/X}return z}function b(){for(var F=0;F<l;++F)c[F]=y(F*u,t,r)}function E(F,z,M){var X,B,re=0;do B=z+(M-z)/2,X=y(B,t,r)-F,X>0?M=B:z=B;while(Math.abs(X)>s&&++re<o);return B}function N(F){for(var z=0,M=1,X=l-1;M!==X&&c[M]<=F;++M)z+=u;--M;var B=(F-c[M])/(c[M+1]-c[M]),re=z+B*u,q=g(re,t,r);return q>=i?m(F,re):q===0?re:E(F,z,z+u)}var A=!1;function x(){A=!0,(t!==e||r!==a)&&b()}var I=function(z){return A||x(),t===e&&r===a?z:z===0?0:z===1?1:y(N(z),e,a)};I.getControlPoints=function(){return[{x:t,y:e},{x:r,y:a}]};var C="generateBezier("+[t,e,r,a]+")";return I.toString=function(){return C},I}/*! Runge-Kutta spring physics function generator. Adapted from Framer.js, copyright Koen Bok. MIT License: http://en.wikipedia.org/wiki/MIT_License */var Rd=function(){function t(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:t(s)}}function r(a,n){var i={dx:a.v,dv:t(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),f=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+f*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,f=1/1e4,h=16/1e3,c,d,v;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),d=u/s*h):d=h;v=r(v||o,d),l.push(1+v.x),u+=16,Math.abs(v.x)>f&&Math.abs(v.v)>f;);return c?function(p){return l[p*(l.length-1)|0]}:u}}(),Et=function(e,r,a,n){var i=Id(e,r,a,n);return function(s,o,l){return s+(o-s)*i(l)}},Fn={linear:function(e,r,a){return e+(r-e)*a},ease:Et(.25,.1,.25,1),"ease-in":Et(.42,0,1,1),"ease-out":Et(0,0,.58,1),"ease-in-out":Et(.42,0,.58,1),"ease-in-sine":Et(.47,0,.745,.715),"ease-out-sine":Et(.39,.575,.565,1),"ease-in-out-sine":Et(.445,.05,.55,.95),"ease-in-quad":Et(.55,.085,.68,.53),"ease-out-quad":Et(.25,.46,.45,.94),"ease-in-out-quad":Et(.455,.03,.515,.955),"ease-in-cubic":Et(.55,.055,.675,.19),"ease-out-cubic":Et(.215,.61,.355,1),"ease-in-out-cubic":Et(.645,.045,.355,1),"ease-in-quart":Et(.895,.03,.685,.22),"ease-out-quart":Et(.165,.84,.44,1),"ease-in-out-quart":Et(.77,0,.175,1),"ease-in-quint":Et(.755,.05,.855,.06),"ease-out-quint":Et(.23,1,.32,1),"ease-in-out-quint":Et(.86,0,.07,1),"ease-in-expo":Et(.95,.05,.795,.035),"ease-out-expo":Et(.19,1,.22,1),"ease-in-out-expo":Et(1,0,0,1),"ease-in-circ":Et(.6,.04,.98,.335),"ease-out-circ":Et(.075,.82,.165,1),"ease-in-out-circ":Et(.785,.135,.15,.86),spring:function(e,r,a){if(a===0)return Fn.linear;var n=Rd(e,r,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":Et};function Vo(t,e,r,a,n){if(a===1||e===r)return r;var i=n(e,r,a);return t==null||((t.roundValue||t.color)&&(i=Math.round(i)),t.min!==void 0&&(i=Math.max(i,t.min)),t.max!==void 0&&(i=Math.min(i,t.max))),i}function _o(t,e){return t.pfValue!=null||t.value!=null?t.pfValue!=null&&(e==null||e.type.units!=="%")?t.pfValue:t.value:t}function pa(t,e,r,a,n){var i=n!=null?n.type:null;r<0?r=0:r>1&&(r=1);var s=_o(t,n),o=_o(e,n);if(R(s)&&R(o))return Vo(i,s,o,r,a);if(te(s)&&te(o)){for(var l=[],u=0;u<o.length;u++){var f=s[u],h=o[u];if(f!=null&&h!=null){var c=Vo(i,f,h,r,a);l.push(c)}else l.push(h)}return l}}function kd(t,e,r,a){var n=!a,i=t._private,s=e._private,o=s.easing,l=s.startTime,u=a?t:t.cy(),f=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=Fn.linear;else{var h;if(j(o)){var c=f.parse("transition-timing-function",o);h=c.value}else h=o;var d,v;j(h)?(d=h,v=[]):(d=h[1],v=h.slice(2).map(function(ue){return+ue})),v.length>0?(d==="spring"&&v.push(s.duration),s.easingImpl=Fn[d].apply(null,v)):s.easingImpl=Fn[d]}var p=s.easingImpl,y;if(s.duration===0?y=1:y=(r-l)/s.duration,s.applying&&(y=s.progress),y<0?y=0:y>1&&(y=1),s.delay==null){var g=s.startPosition,m=s.position;if(m&&n&&!t.locked()){var b={};Wa(g.x,m.x)&&(b.x=pa(g.x,m.x,y,p)),Wa(g.y,m.y)&&(b.y=pa(g.y,m.y,y,p)),t.position(b)}var E=s.startPan,N=s.pan,A=i.pan,x=N!=null&&a;x&&(Wa(E.x,N.x)&&(A.x=pa(E.x,N.x,y,p)),Wa(E.y,N.y)&&(A.y=pa(E.y,N.y,y,p)),t.emit("pan"));var I=s.startZoom,C=s.zoom,F=C!=null&&a;F&&(Wa(I,C)&&(i.zoom=Pa(i.minZoom,pa(I,C,y,p),i.maxZoom)),t.emit("zoom")),(x||F)&&t.emit("viewport");var z=s.style;if(z&&z.length>0&&n){for(var M=0;M<z.length;M++){var X=z[M],B=X.name,re=X,q=s.startStyle[B],Z=f.properties[q.name],ie=pa(q,re,y,p,Z);f.overrideBypass(t,B,ie)}t.emit("style")}}return s.progress=y,y}function Wa(t,e){return t==null||e==null?!1:R(t)&&R(e)?!0:!!(t&&e)}function Pd(t,e,r,a){var n=e._private;n.started=!0,n.startTime=r-n.progress*n.duration}function Uo(t,e){var r=e._private.aniEles,a=[];function n(f,h){var c=f._private,d=c.animation.current,v=c.animation.queue,p=!1;if(d.length===0){var y=v.shift();y&&d.push(y)}for(var g=function(A){for(var x=A.length-1;x>=0;x--){var I=A[x];I()}A.splice(0,A.length)},m=d.length-1;m>=0;m--){var b=d[m],E=b._private;if(E.stopped){d.splice(m,1),E.hooked=!1,E.playing=!1,E.started=!1,g(E.frames);continue}!E.playing&&!E.applying||(E.playing&&E.applying&&(E.applying=!1),E.started||Pd(f,b,t),kd(f,b,t,h),E.applying&&(E.applying=!1),g(E.frames),E.step!=null&&E.step(t),b.completed()&&(d.splice(m,1),E.hooked=!1,E.playing=!1,E.started=!1,g(E.completes)),p=!0)}return!h&&d.length===0&&v.length===0&&a.push(f),p}for(var i=!1,s=0;s<r.length;s++){var o=r[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(r.length>0?e.notify("draw",r):e.notify("draw")),r.unmerge(a),e.emit("step")}var Bd={animate:ht.animate(),animation:ht.animation(),animated:ht.animated(),clearQueue:ht.clearQueue(),delay:ht.delay(),delayAnimation:ht.delayAnimation(),stop:ht.stop(),addToAnimationPool:function(e){var r=this;r.styleEnabled()&&r._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function r(){e._private.animationsRunning&&hn(function(i){Uo(i,e),r()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){Uo(s,e)},a.beforeRenderPriorities.animations):r()}},Fd={qualifierCompare:function(e,r){return e==null||r==null?e==null&&r==null:e.sameText(r)},eventMatches:function(e,r,a){var n=r.qualifier;return n!=null?e!==a.target&&Ae(a.target)&&n.matches(a.target):!0},addEventFields:function(e,r){r.cy=e,r.target=e},callbackContext:function(e,r,a){return r.qualifier!=null?a.target:e}},zn=function(e){return j(e)?new Sr(e):e},Yo={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new Mn(Fd,this)),this},emitter:function(){return this._private.emitter},on:function(e,r,a){return this.emitter().on(e,zn(r),a),this},removeListener:function(e,r,a){return this.emitter().removeListener(e,zn(r),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,r,a){return this.emitter().one(e,zn(r),a),this},once:function(e,r,a){return this.emitter().one(e,zn(r),a),this},emit:function(e,r){return this.emitter().emit(e,r),this},emitAndNotify:function(e,r){return this.emit(e),this.notify(e,r),this}};ht.eventAliasesOn(Yo);var Mi={png:function(e){var r=this._private.renderer;return e=e||{},r.png(e)},jpg:function(e){var r=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",r.jpg(e)}};Mi.jpeg=Mi.jpg;var Gn={layout:function(e){var r=this;if(e==null){Tt("Layout options must be specified to make a layout");return}if(e.name==null){Tt("A `name` must be specified to make a layout");return}var a=e.name,n=r.extension("layout",a);if(n==null){Tt("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;j(e.eles)?i=r.$(e.eles):i=e.eles!=null?e.eles:r.$();var s=new n(Ue({},e,{cy:r,eles:i}));return s}};Gn.createLayout=Gn.makeLayout=Gn.layout;var zd={notify:function(e,r){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();r!=null&&n.merge(r);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,r)}},notifications:function(e){var r=this._private;return e===void 0?r.notificationsEnabled:(r.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var r=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?r.notify(a):r.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var r=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=r.getElementById(i);o.data(s)}})}},Gd=At({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1}),Ii={renderTo:function(e,r,a,n){var i=this._private.renderer;return i.renderTo(e,r,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var r=this,a=r.extension("renderer",e.name);if(a==null){Tt("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&ft("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=Gd(e);n.cy=r,r._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var r=e.container();if(r)for(r._cyreg=null;r.childNodes.length>0;)r.removeChild(r.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Ii.invalidateDimensions=Ii.resize;var $n={collection:function(e,r){return j(e)?this.$(e):de(e)?e.collection():te(e)?(r||(r={}),new Nt(this,e,r.unique,r.removed)):new Nt(this)},nodes:function(e){var r=this.$(function(a){return a.isNode()});return e?r.filter(e):r},edges:function(e){var r=this.$(function(a){return a.isEdge()});return e?r.filter(e):r},$:function(e){var r=this._private.elements;return e?r.filter(e):r.spawnSelf()},mutableElements:function(){return this._private.elements}};$n.elements=$n.filter=$n.$;var Ft={},qa="t",$d="f";Ft.apply=function(t){for(var e=this,r=e._private,a=r.cy,n=a.collection(),i=0;i<t.length;i++){var s=t[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var f=e.updateStyleHints(s);f&&n.push(s)}}return n},Ft.getPropertiesDiff=function(t,e){var r=this,a=r._private.propDiffs=r._private.propDiffs||{},n=t+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<r.length;l++){var u=r[l],f=t[l]===qa,h=e[l]===qa,c=f!==h,d=u.mappedProperties.length>0;if(c||h&&d){var v=void 0;c&&d||c?v=u.properties:d&&(v=u.mappedProperties);for(var p=0;p<v.length;p++){for(var y=v[p],g=y.name,m=!1,b=l+1;b<r.length;b++){var E=r[b],N=e[b]===qa;if(N&&(m=E.properties[y.name]!=null,m))break}!o[g]&&!m&&(o[g]=!0,s.push(g))}}}return a[n]=s,s},Ft.getContextMeta=function(t){for(var e=this,r="",a,n=t._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(t);o?r+=qa:r+=$d}return a=e.getPropertiesDiff(n,r),t._private.styleCxtKey=r,{key:r,diffPropNames:a,empty:a.length===0}},Ft.getContextStyle=function(t){var e=t.key,r=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<r.length;i++){var s=r[i],o=e[i]===qa;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n},Ft.applyContextStyle=function(t,e,r){for(var a=this,n=t.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],f=r.pstyle(l);if(!u)if(f)f.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(f!==u){if(u.mapped===s.fn&&f!=null&&f.mapping!=null&&f.mapping.value===u.value){var h=f.mapping,c=h.fnValue=u.value(r);if(c===h.prevFnValue)continue}var d=i[l]={prev:f};a.applyParsedProperty(r,u),d.next=r.pstyle(l),d.next&&d.next.bypass&&(d.next=d.next.bypassed)}}return{diffProps:i}},Ft.updateStyleHints=function(t){var e=t._private,r=this,a=r.propertyGroupNames,n=r.propertyGroupKeys,i=function(Oe,Me,He){return r.getPropertiesHash(Oe,Me,He)},s=e.styleKey;if(t.removed())return!1;var o=e.group==="nodes",l=t._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var f=n[u];e.styleKeys[f]=[Jr,Na]}for(var h=function(Oe,Me){return e.styleKeys[Me][0]=Ma(Oe,e.styleKeys[Me][0])},c=function(Oe,Me){return e.styleKeys[Me][1]=Ia(Oe,e.styleKeys[Me][1])},d=function(Oe,Me){h(Oe,Me),c(Oe,Me)},v=function(Oe,Me){for(var He=0;He<Oe.length;He++){var We=Oe.charCodeAt(He);h(We,Me),c(We,Me)}},p=2e9,y=function(Oe){return-128<Oe&&Oe<128&&Math.floor(Oe)!==Oe?p-(Oe*1024|0):Oe},g=0;g<a.length;g++){var m=a[g],b=l[m];if(b!=null){var E=this.properties[m],N=E.type,A=E.groupKey,x=void 0;E.hashOverride!=null?x=E.hashOverride(t,b):b.pfValue!=null&&(x=b.pfValue);var I=E.enums==null?b.value:null,C=x!=null,F=I!=null,z=C||F,M=b.units;if(N.number&&z&&!N.multiple){var X=C?x:I;d(y(X),A),!C&&M!=null&&v(M,A)}else v(b.strValue,A)}}for(var B=[Jr,Na],re=0;re<n.length;re++){var q=n[re],Z=e.styleKeys[q];B[0]=Ma(Z[0],B[0]),B[1]=Ia(Z[1],B[1])}e.styleKey=Hl(B[0],B[1]);var ie=e.styleKeys;e.labelDimsKey=wr(ie.labelDimensions);var ue=i(t,["label"],ie.labelDimensions);if(e.labelKey=wr(ue),e.labelStyleKey=wr(cn(ie.commonLabel,ue)),!o){var ge=i(t,["source-label"],ie.labelDimensions);e.sourceLabelKey=wr(ge),e.sourceLabelStyleKey=wr(cn(ie.commonLabel,ge));var se=i(t,["target-label"],ie.labelDimensions);e.targetLabelKey=wr(se),e.targetLabelStyleKey=wr(cn(ie.commonLabel,se))}if(o){var ve=e.styleKeys,ye=ve.nodeBody,Te=ve.nodeBorder,be=ve.backgroundImage,me=ve.compound,ae=ve.pie,xe=[ye,Te,be,me,ae].filter(function(Ce){return Ce!=null}).reduce(cn,[Jr,Na]);e.nodeKey=wr(xe),e.hasPie=ae!=null&&ae[0]!==Jr&&ae[1]!==Na}return s!==e.styleKey},Ft.clearStyleHints=function(t){var e=t._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null},Ft.applyParsedProperty=function(t,e){var r=this,a=e,n=t._private.style,i,s=r.types,o=r.properties[a.name].type,l=a.bypass,u=n[a.name],f=u&&u.bypass,h=t._private,c="mapping",d=function(ye){return ye==null?null:ye.pfValue!=null?ye.pfValue:ye.value},v=function(){var ye=d(u),Te=d(a);r.checkTriggers(t,a.name,ye,Te)};if(a&&a.name.substr(0,3)==="pie"&&ft("The pie style properties are deprecated.  Create charts using background images instead."),e.name==="curve-style"&&t.isEdge()&&(e.value!=="bezier"&&t.isLoop()||e.value==="haystack"&&(t.source().isParent()||t.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,v(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,v(),!0):!1:(v(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,v(),!0):!1:(v(),!0);var p=function(){ft("Do not assign mappings to elements without corresponding data (i.e. ele `"+t.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var y=a.field.split("."),g=h.data,m=0;m<y.length&&g;m++){var b=y[m];g=g[b]}if(g==null)return p(),!1;var E;if(R(g)){var N=a.fieldMax-a.fieldMin;N===0?E=0:E=(g-a.fieldMin)/N}else return ft("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+g+"` for `"+t.id()+"` is non-numeric)"),!1;if(E<0?E=0:E>1&&(E=1),o.color){var A=a.valueMin[0],x=a.valueMax[0],I=a.valueMin[1],C=a.valueMax[1],F=a.valueMin[2],z=a.valueMax[2],M=a.valueMin[3]==null?1:a.valueMin[3],X=a.valueMax[3]==null?1:a.valueMax[3],B=[Math.round(A+(x-A)*E),Math.round(I+(C-I)*E),Math.round(F+(z-F)*E),Math.round(M+(X-M)*E)];i={bypass:a.bypass,name:a.name,value:B,strValue:"rgb("+B[0]+", "+B[1]+", "+B[2]+")"}}else if(o.number){var re=a.valueMin+(a.valueMax-a.valueMin)*E;i=this.parse(a.name,re,a.bypass,c)}else return!1;if(!i)return p(),!1;i.mapping=a,a=i;break}case s.data:{for(var q=a.field.split("."),Z=h.data,ie=0;ie<q.length&&Z;ie++){var ue=q[ie];Z=Z[ue]}if(Z!=null&&(i=this.parse(a.name,Z,a.bypass,c)),!i)return p(),!1;i.mapping=a,a=i;break}case s.fn:{var ge=a.value,se=a.fnValue!=null?a.fnValue:ge(t);if(a.prevFnValue=se,se==null)return ft("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+t.id()+"` is null)"),!1;if(i=this.parse(a.name,se,a.bypass,c),!i)return ft("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+t.id()+"` is invalid)"),!1;i.mapping=ur(a),a=i;break}case void 0:break;default:return!1}return l?(f?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):f?u.bypassed=a:n[a.name]=a,v(),!0},Ft.cleanElements=function(t,e){for(var r=0;r<t.length;r++){var a=t[r];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}},Ft.update=function(){var t=this._private.cy,e=t.mutableElements();e.updateStyle()},Ft.updateTransitions=function(t,e){var r=this,a=t._private,n=t.pstyle("transition-property").value,i=t.pstyle("transition-duration").pfValue,s=t.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var f=n[u],h=t.pstyle(f),c=e[f];if(c){var d=c.prev,v=d,p=c.next!=null?c.next:h,y=!1,g=void 0,m=1e-6;v&&(R(v.pfValue)&&R(p.pfValue)?(y=p.pfValue-v.pfValue,g=v.pfValue+m*y):R(v.value)&&R(p.value)?(y=p.value-v.value,g=v.value+m*y):te(v.value)&&te(p.value)&&(y=v.value[0]!==p.value[0]||v.value[1]!==p.value[1]||v.value[2]!==p.value[2],g=v.strValue),y&&(o[f]=p.strValue,this.applyBypass(t,f,g),l=!0))}}if(!l)return;a.transitioning=!0,new sa(function(b){s>0?t.delayAnimation(s).play().promise().then(b):b()}).then(function(){return t.animation({style:o,duration:i,easing:t.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){r.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(t,n),t.emitAndNotify("style"),a.transitioning=!1)},Ft.checkTrigger=function(t,e,r,a,n,i){var s=this.properties[e],o=n(s);o!=null&&o(r,a)&&i(s)},Ft.checkZOrderTrigger=function(t,e,r,a){var n=this;this.checkTrigger(t,e,r,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",t)})},Ft.checkBoundsTrigger=function(t,e,r,a){this.checkTrigger(t,e,r,a,function(n){return n.triggersBounds},function(n){t.dirtyCompoundBoundsCache(),t.dirtyBoundingBoxCache(),n.triggersBoundsOfParallelBeziers&&(e==="curve-style"&&(r==="bezier"||a==="bezier")||e==="display"&&(r==="none"||a==="none"))&&t.parallelEdges().forEach(function(i){i.isBundledBezier()&&i.dirtyBoundingBoxCache()})})},Ft.checkTriggers=function(t,e,r,a){t.dirtyStyleCache(),this.checkZOrderTrigger(t,e,r,a),this.checkBoundsTrigger(t,e,r,a)};var Ka={};Ka.applyBypass=function(t,e,r,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(r!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,f=this.parse(u,r,!0);f&&i.push(f)}}else if(j(e)){var h=this.parse(e,r,!0);h&&i.push(h)}else if(L(e)){var c=e;a=r;for(var d=Object.keys(c),v=0;v<d.length;v++){var p=d[v],y=c[p];if(y===void 0&&(y=c[yt(p)]),y!==void 0){var g=this.parse(p,y,!0);g&&i.push(g)}}}else return!1;if(i.length===0)return!1;for(var m=!1,b=0;b<t.length;b++){for(var E=t[b],N={},A=void 0,x=0;x<i.length;x++){var I=i[x];if(a){var C=E.pstyle(I.name);A=N[I.name]={prev:C}}m=this.applyParsedProperty(E,ur(I))||m,a&&(A.next=E.pstyle(I.name))}m&&this.updateStyleHints(E),a&&this.updateTransitions(E,N,s)}return m},Ka.overrideBypass=function(t,e,r){e=et(e);for(var a=0;a<t.length;a++){var n=t[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,r):(i.value=r,i.pfValue!=null&&(i.pfValue=r),o?i.strValue="rgb("+r.join(",")+")":l?i.strValue=r.join(" "):i.strValue=""+r,this.updateStyleHints(n)),this.checkTriggers(n,e,u,r)}},Ka.removeAllBypasses=function(t,e){return this.removeBypasses(t,this.propertyNames,e)},Ka.removeBypasses=function(t,e,r){for(var a=!0,n=0;n<t.length;n++){for(var i=t[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],f=i.pstyle(u.name);if(!(!f||!f.bypass)){var h="",c=this.parse(l,h,!0),d=s[u.name]={prev:f};this.applyParsedProperty(i,c),d.next=i.pstyle(u.name)}}this.updateStyleHints(i),r&&this.updateTransitions(i,s,a)}};var Ri={};Ri.getEmSizeInPixels=function(){var t=this.containerCss("font-size");return t!=null?parseFloat(t):1},Ri.containerCss=function(t){var e=this._private.cy,r=e.container(),a=e.window();if(a&&r&&a.getComputedStyle)return a.getComputedStyle(r).getPropertyValue(t)};var fr={};fr.getRenderedStyle=function(t,e){return e?this.getStylePropertyValue(t,e,!0):this.getRawStyle(t,!0)},fr.getRawStyle=function(t,e){var r=this;if(t=t[0],t){for(var a={},n=0;n<r.properties.length;n++){var i=r.properties[n],s=r.getStylePropertyValue(t,i.name,e);s!=null&&(a[i.name]=s,a[yt(i.name)]=s)}return a}},fr.getIndexedStyle=function(t,e,r,a){var n=t.pstyle(e)[r][a];return n??t.cy().style().getDefaultProperty(e)[r][0]},fr.getStylePropertyValue=function(t,e,r){var a=this;if(t=t[0],t){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=t.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(r&&i.number&&o!=null&&R(o)){var f=t.cy().zoom(),h=function(y){return y*f},c=function(y,g){return h(y)+g},d=te(o),v=d?l.every(function(p){return p!=null}):l!=null;return v?d?o.map(function(p,y){return c(p,l[y])}).join(" "):c(o,l):d?o.map(function(p){return j(p)?p:""+h(p)}).join(" "):""+h(o)}else if(u!=null)return u}return null}},fr.getAnimationStartStyle=function(t,e){for(var r={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=t.pstyle(i);s!==void 0&&(L(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(r[i]=s)}return r},fr.getPropsList=function(t){var e=this,r=[],a=t,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[et(o)],f=this.parse(u.name,l);f&&r.push(f)}return r},fr.getNonDefaultPropertiesHash=function(t,e,r){var a=r.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=t.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=Ma(o,a[0]),a[1]=Ia(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=Ma(o,a[0]),a[1]=Ia(o,a[1]);return a},fr.getPropertiesHash=fr.getNonDefaultPropertiesHash;var Vn={};Vn.appendFromJson=function(t){for(var e=this,r=0;r<t.length;r++){var a=t[r],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e},Vn.fromJson=function(t){var e=this;return e.resetToDefault(),e.appendFromJson(t),e},Vn.json=function(){for(var t=[],e=this.defaultLength;e<this.length;e++){for(var r=this[e],a=r.selector,n=r.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}t.push({selector:a?a.toString():"core",style:i})}return t};var ki={};ki.appendFromString=function(t){var e=this,r=this,a=""+t,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var f=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!f){ft("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=f[0];var h=f[1];if(h!=="core"){var c=new Sr(h);if(c.invalid){ft("Skipping parsing of block: Invalid selector found in string stylesheet: "+h),o();continue}}var d=f[2],v=!1;i=d;for(var p=[];;){var y=i.match(/^\s*$/);if(y)break;var g=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!g){ft("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+d),v=!0;break}s=g[0];var m=g[1],b=g[2],E=e.properties[m];if(!E){ft("Skipping property: Invalid property name in: "+s),l();continue}var N=r.parse(m,b);if(!N){ft("Skipping property: Invalid property definition in: "+s),l();continue}p.push({name:m,val:b}),l()}if(v){o();break}r.selector(h);for(var A=0;A<p.length;A++){var x=p[A];r.css(x.name,x.val)}o()}return r},ki.fromString=function(t){var e=this;return e.resetToDefault(),e.appendFromString(t),e};var Mt={};(function(){var t=dt,e=qt,r=Zr,a=Sa,n=er,i=function(xe){return"^"+xe+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(xe){var Ce=t+"|\\w+|"+e+"|"+r+"|"+a+"|"+n;return"^"+xe+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+t+")\\s*\\,\\s*("+t+")\\s*,\\s*("+Ce+")\\s*\\,\\s*("+Ce+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Mt.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi"]},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(xe,Ce){switch(xe.length){case 2:return Ce[0]!=="deg"&&Ce[0]!=="rad"&&Ce[1]!=="deg"&&Ce[1]!=="rad";case 1:return j(xe[0])||Ce[0]==="deg"||Ce[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*,\\s*("+t+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(xe){var Ce=xe.length;return Ce===1||Ce===2||Ce===4}}};var l={zeroNonZero:function(xe,Ce){return(xe==null||Ce==null)&&xe!==Ce||xe==0&&Ce!=0?!0:xe!=0&&Ce==0},any:function(xe,Ce){return xe!=Ce},emptyNonEmpty:function(xe,Ce){var Oe=Pe(xe),Me=Pe(Ce);return Oe&&!Me||!Oe&&Me}},u=Mt.types,f=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],h=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],d=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],v=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification}],p=[{name:"events",type:u.bool},{name:"text-events",type:u.bool}],y=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfParallelBeziers:!0},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.nonNegativeInt,triggersZOrder:l.any}],g=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any}],m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],E=function(xe,Ce){return Ce.value==="label"?-xe.poolIndex():Ce.pfValue},N=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:E},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:E},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],A=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle}],x=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],I=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],C=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],F=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],z=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],M=[];Mt.pieBackgroundN=16,M.push({name:"pie-size",type:u.sizeMaybePercent});for(var X=1;X<=Mt.pieBackgroundN;X++)M.push({name:"pie-"+X+"-background-color",type:u.color}),M.push({name:"pie-"+X+"-background-size",type:u.percent}),M.push({name:"pie-"+X+"-background-opacity",type:u.zeroOneNumber});var B=[],re=Mt.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill}].forEach(function(ae){re.forEach(function(xe){var Ce=xe+"-"+ae.name,Oe=ae.type,Me=ae.triggersBounds;B.push({name:Ce,type:Oe,triggersBounds:Me})})},{});var q=Mt.properties=[].concat(p,b,y,g,m,F,v,d,f,h,c,N,A,x,M,I,C,B,z),Z=Mt.propertyGroups={behavior:p,transition:b,visibility:y,overlay:g,underlay:m,ghost:F,commonLabel:v,labelDimensions:d,mainLabel:f,sourceLabel:h,targetLabel:c,nodeBody:N,nodeBorder:A,backgroundImage:x,pie:M,compound:I,edgeLine:C,edgeArrow:B,core:z},ie=Mt.propertyGroupNames={},ue=Mt.propertyGroupKeys=Object.keys(Z);ue.forEach(function(ae){ie[ae]=Z[ae].map(function(xe){return xe.name}),Z[ae].forEach(function(xe){return xe.groupKey=ae})});var ge=Mt.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Mt.propertyNames=q.map(function(ae){return ae.name});for(var se=0;se<q.length;se++){var ve=q[se];q[ve.name]=ve}for(var ye=0;ye<ge.length;ye++){var Te=ge[ye],be=q[Te.pointsTo],me={name:Te.name,alias:!0,pointsTo:be};q.push(me),q[Te.name]=me}})(),Mt.getDefaultProperty=function(t){return this.getDefaultProperties()[t]},Mt.getDefaultProperties=function(){var t=this._private;if(t.defaultProperties!=null)return t.defaultProperties;for(var e=Ue({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var f=1;f<=Mt.pieBackgroundN;f++){var h=u.name.replace("{{i}}",f),c=u.value;l[h]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"taxi-turn":"50%","taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"}].reduce(function(l,u){return Mt.arrowPrefixes.forEach(function(f){var h=f+"-"+u.name,c=u.value;l[h]=c}),l},{})),r={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);r[i]=o}}return t.defaultProperties=r,t.defaultProperties},Mt.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var _n={};_n.parse=function(t,e,r,a){var n=this;if(Y(e))return n.parseImplWarn(t,e,r,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=r?"t":"f",o=""+e,l=fs(t,o,s,i),u=n.propCache=n.propCache||[],f;return(f=u[l])||(f=u[l]=n.parseImplWarn(t,e,r,a)),(r||a==="mapping")&&(f=ur(f),f&&(f.value=ur(f.value))),f},_n.parseImplWarn=function(t,e,r,a){var n=this.parseImpl(t,e,r,a);return!n&&e!=null&&ft("The style property `".concat(t,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&ft("The style value of `label` is deprecated for `"+n.name+"`"),n},_n.parseImpl=function(t,e,r,a){var n=this;t=et(t);var i=n.properties[t],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,t=i.name);var l=j(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(r&&(e===""||e===null))return{name:t,value:e,bypass:!0,deleteBypass:!0};if(Y(e))return{name:t,value:e,strValue:"fn",mapped:o.fn,bypass:r};var f,h;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(f=new RegExp(o.data.regex).exec(e))){if(r)return!1;var c=o.data;return{name:t,value:f,strValue:""+e,mapped:c,field:f[1],bypass:r}}else if(e.length>=10&&e[0]==="m"&&(h=new RegExp(o.mapData.regex).exec(e))){if(r||u.multiple)return!1;var d=o.mapData;if(!(u.color||u.number))return!1;var v=this.parse(t,h[4]);if(!v||v.mapped)return!1;var p=this.parse(t,h[5]);if(!p||p.mapped)return!1;if(v.pfValue===p.pfValue||v.strValue===p.strValue)return ft("`"+t+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+t+": "+v.strValue+"`"),this.parse(t,v.strValue);if(u.color){var y=v.value,g=p.value,m=y[0]===g[0]&&y[1]===g[1]&&y[2]===g[2]&&(y[3]===g[3]||(y[3]==null||y[3]===1)&&(g[3]==null||g[3]===1));if(m)return!1}return{name:t,value:h,strValue:""+e,mapped:d,field:h[1],fieldMin:parseFloat(h[2]),fieldMax:parseFloat(h[3]),valueMin:v.value,valueMax:p.value,bypass:r}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):te(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var E=[],N=[],A=[],x="",I=!1,C=0;C<b.length;C++){var F=n.parse(t,b[C],r,"multiple");I=I||j(F.value),E.push(F.value),A.push(F.pfValue!=null?F.pfValue:F.value),N.push(F.units),x+=(C>0?" ":"")+F.strValue}return u.validate&&!u.validate(E,N)?null:u.singleEnum&&I?E.length===1&&j(E[0])?{name:t,value:E[0],strValue:E[0],bypass:r}:null:{name:t,value:E,pfValue:A,strValue:x,bypass:r,units:N}}var z=function(){for(var Ce=0;Ce<u.enums.length;Ce++){var Oe=u.enums[Ce];if(Oe===e)return{name:t,value:e,strValue:""+e,bypass:r}}return null};if(u.number){var M,X="px";if(u.units&&(M=u.units),u.implicitUnits&&(X=u.implicitUnits),!u.unitless)if(l){var B="px|em"+(u.allowPercent?"|\\%":"");M&&(B=M);var re=e.match("^("+dt+")("+B+")?$");re&&(e=re[1],M=re[2]||X)}else(!M||u.implicitUnits)&&(M=X);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,z();if(u.integer&&!W(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var q={name:t,value:e,strValue:""+e+(M||""),units:M,bypass:r};return u.unitless||M!=="px"&&M!=="em"?q.pfValue=e:q.pfValue=M==="px"||!M?e:this.getEmSizeInPixels()*e,(M==="ms"||M==="s")&&(q.pfValue=M==="ms"?e:1e3*e),(M==="deg"||M==="rad")&&(q.pfValue=M==="rad"?e:xf(e)),M==="%"&&(q.pfValue=e/100),q}else if(u.propList){var Z=[],ie=""+e;if(ie!=="none"){for(var ue=ie.split(/\s*,\s*|\s+/),ge=0;ge<ue.length;ge++){var se=ue[ge].trim();n.properties[se]?Z.push(se):ft("`"+se+"` is not a valid property name")}if(Z.length===0)return null}return{name:t,value:Z,strValue:Z.length===0?"none":Z.join(" "),bypass:r}}else if(u.color){var ve=nl(e);return ve?{name:t,value:ve,pfValue:ve,strValue:"rgb("+ve[0]+","+ve[1]+","+ve[2]+")",bypass:r}:null}else if(u.regex||u.regexes){if(u.enums){var ye=z();if(ye)return ye}for(var Te=u.regexes?u.regexes:[u.regex],be=0;be<Te.length;be++){var me=new RegExp(Te[be]),ae=me.exec(e);if(ae)return{name:t,value:u.singleRegexMatchValue?ae[1]:ae,strValue:""+e,bypass:r}}return null}else return u.string?{name:t,value:""+e,strValue:""+e,bypass:r}:u.enums?z():null};var zt=function t(e){if(!(this instanceof t))return new t(e);if(!_e(e)){Tt("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},Gt=zt.prototype;Gt.instanceString=function(){return"style"},Gt.clear=function(){for(var t=this._private,e=t.cy,r=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,t.contextStyles={},t.propDiffs={},this.cleanElements(r,!0),r.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this},Gt.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},Gt.core=function(t){return this._private.coreStyle[t]||this.getDefaultProperty(t)},Gt.selector=function(t){var e=t==="core"?null:new Sr(t),r=this.length++;return this[r]={selector:e,properties:[],mappedProperties:[],index:r},this},Gt.css=function(){var t=this,e=arguments;if(e.length===1)for(var r=e[0],a=0;a<t.properties.length;a++){var n=t.properties[a],i=r[n.name];i===void 0&&(i=r[yt(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this},Gt.style=Gt.css,Gt.cssRule=function(t,e){var r=this.parse(t,e);if(r){var a=this.length-1;this[a].properties.push(r),this[a].properties[r.name]=r,r.name.match(/pie-(\d+)-background-size/)&&r.value&&(this._private.hasPie=!0),r.mapped&&this[a].mappedProperties.push(r);var n=!this[a].selector;n&&(this._private.coreStyle[r.name]=r)}return this},Gt.append=function(t){return tt(t)?t.appendToStyle(this):te(t)?this.appendFromJson(t):j(t)&&this.appendFromString(t),this},zt.fromJson=function(t,e){var r=new zt(t);return r.fromJson(e),r},zt.fromString=function(t,e){return new zt(t).fromString(e)},[Ft,Ka,Ri,fr,Vn,ki,Mt,_n].forEach(function(t){Ue(Gt,t)}),zt.types=Gt.types,zt.properties=Gt.properties,zt.propertyGroups=Gt.propertyGroups,zt.propertyGroupNames=Gt.propertyGroupNames,zt.propertyGroupKeys=Gt.propertyGroupKeys;var Vd={style:function(e){if(e){var r=this.setStyle(e);r.update()}return this._private.style},setStyle:function(e){var r=this._private;return tt(e)?r.style=e.generateStyle(this):te(e)?r.style=zt.fromJson(this,e):j(e)?r.style=zt.fromString(this,e):r.style=zt(this),r.style},updateStyle:function(){this.mutableElements().updateStyle()}},_d="single",Vr={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var r=this._private;if(r.selectionType==null&&(r.selectionType=_d),e!==void 0)(e==="additive"||e==="single")&&(r.selectionType=e);else return r.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,r=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return r;case 1:if(j(e[0]))return a=e[0],r[a];if(L(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,R(s)&&(r.x=s),R(o)&&(r.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&R(n)&&(r[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,r){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:L(e)&&(o=a[0],l=o.x,u=o.y,R(l)&&(n.x+=l),R(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=r,(i==="x"||i==="y")&&R(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},fit:function(e,r){var a=this.getFitViewport(e,r);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,r){if(R(e)&&r===void 0&&(r=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(j(e)){var n=e;e=this.$(n)}else if(Xe(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else de(e)||(e=this.mutableElements());if(!(de(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(r=R(r)?r:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*r)/a.w,(o-2*r)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,r){var a=this._private;if(r==null){var n=e;e=n.min,r=n.max}return R(e)&&R(r)&&e<=r?(a.minZoom=e,a.maxZoom=r):R(e)&&r===void 0&&e<=a.maxZoom?a.minZoom=e:R(r)&&e===void 0&&r>=a.minZoom&&(a.maxZoom=r),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var r=this._private,a=r.pan,n=r.zoom,i,s,o=!1;if(r.zoomingEnabled||(o=!0),R(e)?s=e:L(e)&&(s=e.level,e.position!=null?i=gn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!r.panningEnabled&&(o=!0)),s=s>r.maxZoom?r.maxZoom:s,s=s<r.minZoom?r.minZoom:s,o||!R(s)||s===n||i!=null&&(!R(i.x)||!R(i.y)))return null;if(i!=null){var l=a,u=n,f=s,h={x:-f/u*(i.x-l.x)+i.x,y:-f/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:f,pan:h}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var r=this.getZoomedViewport(e),a=this._private;return r==null||!r.zoomed?this:(a.zoom=r.zoom,r.panned&&(a.pan.x=r.pan.x,a.pan.y=r.pan.y),this.emit("zoom"+(r.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var r=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(R(e.zoom)||(a=!1),L(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<r.minZoom||l>r.maxZoom||!r.zoomingEnabled?s=!0:(r.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&r.panningEnabled){var u=e.pan;R(u.x)&&(r.pan.x=u.x,o=!1),R(u.y)&&(r.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var r=this.getCenterPan(e);return r&&(this._private.pan=r,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,r){if(this._private.panningEnabled){if(j(e)){var a=e;e=this.mutableElements().filter(a)}else de(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();r=r===void 0?this._private.zoom:r;var o={x:(i-r*(n.x1+n.x2))/2,y:(s-r*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,r=e.container,a=this;return e.sizeCache=e.sizeCache||(r?function(){var n=a.window().getComputedStyle(r),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:r.clientWidth-i("padding-left")-i("padding-right"),height:r.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,r=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/r,x2:(a.x2-e.x)/r,y1:(a.y1-e.y)/r,y2:(a.y2-e.y)/r};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),r=this.height();return{x1:0,y1:0,x2:e,y2:r,w:e,h:r}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};Vr.centre=Vr.center,Vr.autolockNodes=Vr.autolock,Vr.autoungrabifyNodes=Vr.autoungrabify;var Za={data:ht.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:ht.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:ht.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:ht.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Za.attr=Za.data,Za.removeAttr=Za.removeData;var Qa=function(e){var r=this;e=Ue({},e);var a=e.container;a&&!fe(a)&&fe(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=r;var s=D!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=Ue({name:s?"grid":"null"},o.layout),o.renderer=Ue({name:s?"canvas":"null"},o.renderer);var l=function(v,p,y){return p!==void 0?p:y!==void 0?y:v},u=this._private={container:a,ready:!1,options:o,elements:new Nt(this),listeners:[],aniEles:new Nt(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:R(o.zoom)?o.zoom:1,pan:{x:L(o.pan)&&R(o.pan.x)?o.pan.x:0,y:L(o.pan)&&R(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var f=function(v,p){var y=v.some(rt);if(y)return sa.all(v).then(p);p(v)};u.styleEnabled&&r.setStyle([]);var h=Ue({},o,o.renderer);r.initRenderer(h);var c=function(v,p,y){r.notifications(!1);var g=r.mutableElements();g.length>0&&g.remove(),v!=null&&(L(v)||te(v))&&r.add(v),r.one("layoutready",function(b){r.notifications(!0),r.emit(b),r.one("load",p),r.emitAndNotify("load")}).one("layoutstop",function(){r.one("done",y),r.emit("done")});var m=Ue({},r._private.options.layout);m.eles=r.elements(),r.layout(m).run()};f([o.style,o.elements],function(d){var v=d[0],p=d[1];u.styleEnabled&&r.style().append(v),c(p,function(){r.startAnimationLoop(),u.ready=!0,Y(o.ready)&&r.on("ready",o.ready);for(var y=0;y<i.length;y++){var g=i[y];r.on("ready",g)}n&&(n.readies=[]),r.emit("ready")},o.done)})},Un=Qa.prototype;Ue(Un,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return D;var r=this._private.container.ownerDocument;return r===void 0||r==null?D:r.defaultView||D},mount:function(e){if(e!=null){var r=this,a=r._private,n=a.options;return!fe(e)&&fe(e[0])&&(e=e[0]),r.stopAnimationLoop(),r.destroyRenderer(),a.container=e,a.styleEnabled=!0,r.invalidateSize(),r.initRenderer(Ue({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),r.startAnimationLoop(),r.style(n.style),r.emit("mount"),r}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return ur(this._private.options)},json:function(e){var r=this,a=r._private,n=r.mutableElements(),i=function(E){return r.getElementById(E.id())};if(L(e)){if(r.startBatch(),e.elements){var s={},o=function(E,N){for(var A=[],x=[],I=0;I<E.length;I++){var C=E[I];if(!C.data.id){ft("cy.json() cannot handle elements without an ID attribute");continue}var F=""+C.data.id,z=r.getElementById(F);s[F]=!0,z.length!==0?x.push({ele:z,json:C}):(N&&(C.group=N),A.push(C))}r.add(A);for(var M=0;M<x.length;M++){var X=x[M],B=X.ele,re=X.json;B.json(re)}};if(te(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var f=l[u],h=e.elements[f];te(h)&&o(h,f)}var c=r.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&r.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&r.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&r.pan(e.pan),e.data&&r.data(e.data);for(var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],v=0;v<d.length;v++){var p=d[v];e[p]!=null&&r[p](e[p])}return r.endBatch(),this}else{var y=!!e,g={};y?g.elements=this.elements().map(function(b){return b.json()}):(g.elements={},n.forEach(function(b){var E=b.group();g.elements[E]||(g.elements[E]=[]),g.elements[E].push(b.json())})),this._private.styleEnabled&&(g.style=r.style().json()),g.data=ur(r.data());var m=a.options;return g.zoomingEnabled=a.zoomingEnabled,g.userZoomingEnabled=a.userZoomingEnabled,g.zoom=a.zoom,g.minZoom=a.minZoom,g.maxZoom=a.maxZoom,g.panningEnabled=a.panningEnabled,g.userPanningEnabled=a.userPanningEnabled,g.pan=ur(a.pan),g.boxSelectionEnabled=a.boxSelectionEnabled,g.renderer=ur(m.renderer),g.hideEdgesOnViewport=m.hideEdgesOnViewport,g.textureOnViewport=m.textureOnViewport,g.wheelSensitivity=m.wheelSensitivity,g.motionBlur=m.motionBlur,g.multiClickDebounceTime=m.multiClickDebounceTime,g}}}),Un.$id=Un.getElementById,[Md,Bd,Yo,Mi,Gn,zd,Ii,$n,Vd,Vr,Za].forEach(function(t){Ue(Un,t)});var Ud={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}},Yd={maximal:!1,acyclic:!1},ya=function(e){return e.scratch("breadthfirst")},Ho=function(e,r){return e.scratch("breadthfirst",r)};function Xo(t){this.options=Ue({},Ud,Yd,t)}Xo.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().filter(function(Re){return!Re.isParent()}),i=a,s=e.directed,o=e.acyclic||e.maximal||e.maximalAdjustments>0,l=Yt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),u;if(de(e.roots))u=e.roots;else if(te(e.roots)){for(var f=[],h=0;h<e.roots.length;h++){var c=e.roots[h],d=r.getElementById(c);f.push(d)}u=r.collection(f)}else if(j(e.roots))u=r.$(e.roots);else if(s)u=n.roots();else{var v=a.components();u=r.collection();for(var p=function(Ie){var Ge=v[Ie],Fe=Ge.maxDegree(!1),ke=Ge.filter(function(ze){return ze.degree(!1)===Fe});u=u.add(ke)},y=0;y<v.length;y++)p(y)}var g=[],m={},b=function(Ie,Ge){g[Ge]==null&&(g[Ge]=[]);var Fe=g[Ge].length;g[Ge].push(Ie),Ho(Ie,{index:Fe,depth:Ge})},E=function(Ie,Ge){var Fe=ya(Ie),ke=Fe.depth,ze=Fe.index;g[ke][ze]=null,b(Ie,Ge)};i.bfs({roots:u,directed:e.directed,visit:function(Ie,Ge,Fe,ke,ze){var je=Ie[0],Ze=je.id();b(je,ze),m[Ze]=!0}});for(var N=[],A=0;A<n.length;A++){var x=n[A];m[x.id()]||N.push(x)}var I=function(Ie){for(var Ge=g[Ie],Fe=0;Fe<Ge.length;Fe++){var ke=Ge[Fe];if(ke==null){Ge.splice(Fe,1),Fe--;continue}Ho(ke,{depth:Ie,index:Fe})}},C=function(){for(var Ie=0;Ie<g.length;Ie++)I(Ie)},F=function(Ie,Ge){for(var Fe=ya(Ie),ke=Ie.incomers().filter(function(k){return k.isNode()&&a.has(k)}),ze=-1,je=Ie.id(),Ze=0;Ze<ke.length;Ze++){var Ye=ke[Ze],ct=ya(Ye);ze=Math.max(ze,ct.depth)}if(Fe.depth<=ze){if(!e.acyclic&&Ge[je])return null;var De=ze+1;return E(Ie,De),Ge[je]=De,!0}return!1};if(s&&o){var z=[],M={},X=function(Ie){return z.push(Ie)},B=function(){return z.shift()};for(n.forEach(function(Re){return z.push(Re)});z.length>0;){var re=B(),q=F(re,M);if(q)re.outgoers().filter(function(Re){return Re.isNode()&&a.has(Re)}).forEach(X);else if(q===null){ft("Detected double maximal shift for node `"+re.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}C();var Z=0;if(e.avoidOverlap)for(var ie=0;ie<n.length;ie++){var ue=n[ie],ge=ue.layoutDimensions(e),se=ge.w,ve=ge.h;Z=Math.max(Z,se,ve)}var ye={},Te=function(Ie){if(ye[Ie.id()])return ye[Ie.id()];for(var Ge=ya(Ie).depth,Fe=Ie.neighborhood(),ke=0,ze=0,je=0;je<Fe.length;je++){var Ze=Fe[je];if(!(Ze.isEdge()||Ze.isParent()||!n.has(Ze))){var Ye=ya(Ze);if(Ye!=null){var ct=Ye.index,De=Ye.depth;if(!(ct==null||De==null)){var k=g[De].length;De<Ge&&(ke+=ct/k,ze++)}}}}return ze=Math.max(1,ze),ke=ke/ze,ze===0&&(ke=0),ye[Ie.id()]=ke,ke},be=function(Ie,Ge){var Fe=Te(Ie),ke=Te(Ge),ze=Fe-ke;return ze===0?ji(Ie.id(),Ge.id()):ze};e.depthSort!==void 0&&(be=e.depthSort);for(var me=0;me<g.length;me++)g[me].sort(be),I(me);for(var ae=[],xe=0;xe<N.length;xe++)ae.push(N[xe]);g.unshift(ae),C();for(var Ce=0,Oe=0;Oe<g.length;Oe++)Ce=Math.max(g[Oe].length,Ce);var Me={x:l.x1+l.w/2,y:l.x1+l.h/2},He=g.reduce(function(Re,Ie){return Math.max(Re,Ie.length)},0),We=function(Ie){var Ge=ya(Ie),Fe=Ge.depth,ke=Ge.index,ze=g[Fe].length,je=Math.max(l.w/((e.grid?He:ze)+1),Z),Ze=Math.max(l.h/(g.length+1),Z),Ye=Math.min(l.w/2/g.length,l.h/2/g.length);if(Ye=Math.max(Ye,Z),e.circle){var De=Ye*Fe+Ye-(g.length>0&&g[0].length<=3?Ye/2:0),k=2*Math.PI/g[Fe].length*ke;return Fe===0&&g[0].length===1&&(De=1),{x:Me.x+De*Math.cos(k),y:Me.y+De*Math.sin(k)}}else{var ct={x:Me.x+(ke+1-(ze+1)/2)*je,y:(Fe+1)*Ze};return ct}};return a.nodes().layoutPositions(this,e,We),this};var Hd={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function Wo(t){this.options=Ue({},Hd,t)}Wo.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=Yt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),f,h=0,c=0;c<i.length;c++){var d=i[c],v=d.layoutDimensions(e),p=v.w,y=v.h;h=Math.max(h,p,y)}if(R(e.radius)?f=e.radius:i.length<=1?f=0:f=Math.min(s.h,s.w)/2-h,i.length>1&&e.avoidOverlap){h*=1.75;var g=Math.cos(u)-Math.cos(0),m=Math.sin(u)-Math.sin(0),b=Math.sqrt(h*h/(g*g+m*m));f=Math.max(b,f)}var E=function(A,x){var I=e.startAngle+x*u*(n?1:-1),C=f*Math.cos(I),F=f*Math.sin(I),z={x:o.x+C,y:o.y+F};return z};return a.nodes().layoutPositions(this,e,E),this};var Xd={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function qo(t){this.options=Ue({},Xd,t)}qo.prototype.run=function(){for(var t=this.options,e=t,r=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=t.cy,n=e.eles,i=n.nodes().not(":parent"),s=Yt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,f=0;f<i.length;f++){var h=i[f],c=void 0;c=e.concentric(h),l.push({value:c,node:h}),h._private.scratch.concentric=c}i.updateStyle();for(var d=0;d<i.length;d++){var v=i[d],p=v.layoutDimensions(e);u=Math.max(u,p.w,p.h)}l.sort(function(Re,Ie){return Ie.value-Re.value});for(var y=e.levelWidth(i),g=[[]],m=g[0],b=0;b<l.length;b++){var E=l[b];if(m.length>0){var N=Math.abs(m[0].value-E.value);N>=y&&(m=[],g.push(m))}m.push(E)}var A=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=g.length>0&&g[0].length>1,I=Math.min(s.w,s.h)/2-A,C=I/(g.length+x?1:0);A=Math.min(A,C)}for(var F=0,z=0;z<g.length;z++){var M=g[z],X=e.sweep===void 0?2*Math.PI-2*Math.PI/M.length:e.sweep,B=M.dTheta=X/Math.max(1,M.length-1);if(M.length>1&&e.avoidOverlap){var re=Math.cos(B)-Math.cos(0),q=Math.sin(B)-Math.sin(0),Z=Math.sqrt(A*A/(re*re+q*q));F=Math.max(Z,F)}M.r=F,F+=A}if(e.equidistant){for(var ie=0,ue=0,ge=0;ge<g.length;ge++){var se=g[ge],ve=se.r-ue;ie=Math.max(ie,ve)}ue=0;for(var ye=0;ye<g.length;ye++){var Te=g[ye];ye===0&&(ue=Te.r),Te.r=ue,ue+=ie}}for(var be={},me=0;me<g.length;me++)for(var ae=g[me],xe=ae.dTheta,Ce=ae.r,Oe=0;Oe<ae.length;Oe++){var Me=ae[Oe],He=e.startAngle+(r?1:-1)*xe*Oe,We={x:o.x+Ce*Math.cos(He),y:o.y+Ce*Math.sin(He)};be[Me.node.id()]=We}return n.nodes().layoutPositions(this,e,function(Re){var Ie=Re.id();return be[Ie]}),this};var Pi,Wd={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,r){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Yn(t){this.options=Ue({},Wd,t),this.options.layout=this}Yn.prototype.run=function(){var t=this.options,e=t.cy,r=this;r.stopped=!1,(t.animate===!0||t.animate===!1)&&r.emit({type:"layoutstart",layout:r}),t.debug===!0?Pi=!0:Pi=!1;var a=qd(e,r,t);Pi&&Qd(a),t.randomize&&Jd(a);var n=gr(),i=function(){jd(a,e,t),t.fit===!0&&e.fit(t.padding)},s=function(c){return!(r.stopped||c>=t.numIter||(eg(a,t),a.temperature=a.temperature*t.coolingFactor,a.temperature<t.minTemp))},o=function(){if(t.animate===!0||t.animate===!1)i(),r.one("layoutstop",t.stop),r.emit({type:"layoutstop",layout:r});else{var c=t.eles.nodes(),d=Ko(a,t,c);c.layoutPositions(r,t,d)}},l=0,u=!0;if(t.animate===!0){var f=function h(){for(var c=0;u&&c<t.refresh;)u=s(l),l++,c++;if(!u)Qo(a,t),o();else{var d=gr();d-n>=t.animationThreshold&&i(),hn(h)}};f()}else{for(;u;)u=s(l),l++;Qo(a,t),o()}return this},Yn.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},Yn.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var qd=function(e,r,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=Yt(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},f=0;f<l.length;f++)for(var h=l[f],c=0;c<h.length;c++){var d=h[c];u[d.id()]=f}for(var f=0;f<o.nodeSize;f++){var v=i[f],p=v.layoutDimensions(a),y={};y.isLocked=v.locked(),y.id=v.data("id"),y.parentId=v.data("parent"),y.cmptId=u[v.id()],y.children=[],y.positionX=v.position("x"),y.positionY=v.position("y"),y.offsetX=0,y.offsetY=0,y.height=p.w,y.width=p.h,y.maxX=y.positionX+y.width/2,y.minX=y.positionX-y.width/2,y.maxY=y.positionY+y.height/2,y.minY=y.positionY-y.height/2,y.padLeft=parseFloat(v.style("padding")),y.padRight=parseFloat(v.style("padding")),y.padTop=parseFloat(v.style("padding")),y.padBottom=parseFloat(v.style("padding")),y.nodeRepulsion=Y(a.nodeRepulsion)?a.nodeRepulsion(v):a.nodeRepulsion,o.layoutNodes.push(y),o.idToIndex[y.id]=f}for(var g=[],m=0,b=-1,E=[],f=0;f<o.nodeSize;f++){var v=o.layoutNodes[f],N=v.parentId;N!=null?o.layoutNodes[o.idToIndex[N]].children.push(v.id):(g[++b]=v.id,E.push(v.id))}for(o.graphSet.push(E);m<=b;){var A=g[m++],x=o.idToIndex[A],d=o.layoutNodes[x],I=d.children;if(I.length>0){o.graphSet.push(I);for(var f=0;f<I.length;f++)g[++b]=I[f]}}for(var f=0;f<o.graphSet.length;f++)for(var C=o.graphSet[f],c=0;c<C.length;c++){var F=o.idToIndex[C[c]];o.indexToGraph[F]=f}for(var f=0;f<o.edgeSize;f++){var z=n[f],M={};M.id=z.data("id"),M.sourceId=z.data("source"),M.targetId=z.data("target");var X=Y(a.idealEdgeLength)?a.idealEdgeLength(z):a.idealEdgeLength,B=Y(a.edgeElasticity)?a.edgeElasticity(z):a.edgeElasticity,re=o.idToIndex[M.sourceId],q=o.idToIndex[M.targetId],Z=o.indexToGraph[re],ie=o.indexToGraph[q];if(Z!=ie){for(var ue=Kd(M.sourceId,M.targetId,o),ge=o.graphSet[ue],se=0,y=o.layoutNodes[re];ge.indexOf(y.id)===-1;)y=o.layoutNodes[o.idToIndex[y.parentId]],se++;for(y=o.layoutNodes[q];ge.indexOf(y.id)===-1;)y=o.layoutNodes[o.idToIndex[y.parentId]],se++;X*=se*a.nestingFactor}M.idealLength=X,M.elasticity=B,o.layoutEdges.push(M)}return o},Kd=function(e,r,a){var n=Zd(e,r,0,a);return 2>n.count?0:n.graph},Zd=function t(e,r,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(r))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],f=n.layoutNodes[u].children;if(f.length!==0){var h=n.indexToGraph[n.idToIndex[f[0]]],c=t(e,r,h,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},Qd,Jd=function(e,r){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},Ko=function(e,r,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return r.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(r.boundingBox){var u=(l.positionX-i.x1)/i.w,f=(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+f*n.h}}else return{x:l.positionX,y:l.positionY}}},jd=function(e,r,a){var n=a.layout,i=a.eles.nodes(),s=Ko(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},eg=function(e,r,a){tg(e,r),ng(e),ig(e,r),sg(e),og(e)},tg=function(e,r){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];rg(o,u,e,r)}},Zo=function(e){return-e+2*e*Math.random()},rg=function(e,r,a,n){var i=e.cmptId,s=r.cmptId;if(!(i!==s&&!a.isCompound)){var o=r.positionX-e.positionX,l=r.positionY-e.positionY,u=1;o===0&&l===0&&(o=Zo(u),l=Zo(u));var f=ag(e,r,o,l);if(f>0)var h=n.nodeOverlap*f,c=Math.sqrt(o*o+l*l),d=h*o/c,v=h*l/c;else var p=Hn(e,o,l),y=Hn(r,-1*o,-1*l),g=y.x-p.x,m=y.y-p.y,b=g*g+m*m,c=Math.sqrt(b),h=(e.nodeRepulsion+r.nodeRepulsion)/b,d=h*g/c,v=h*m/c;e.isLocked||(e.offsetX-=d,e.offsetY-=v),r.isLocked||(r.offsetX+=d,r.offsetY+=v)}},ag=function(e,r,a,n){if(a>0)var i=e.maxX-r.minX;else var i=r.maxX-e.minX;if(n>0)var s=e.maxY-r.minY;else var s=r.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},Hn=function(e,r,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/r,u=s/o,f={};return r===0&&0<a||r===0&&0>a?(f.x=n,f.y=i+s/2,f):0<r&&-1*u<=l&&l<=u?(f.x=n+o/2,f.y=i+o*a/2/r,f):0>r&&-1*u<=l&&l<=u?(f.x=n-o/2,f.y=i-o*a/2/r,f):0<a&&(l<=-1*u||l>=u)?(f.x=n+s*r/2/a,f.y=i+s/2,f):(0>a&&(l<=-1*u||l>=u)&&(f.x=n-s*r/2/a,f.y=i-s/2),f)},ng=function(e,r){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,f=l.positionY-s.positionY;if(!(u===0&&f===0)){var h=Hn(s,u,f),c=Hn(l,-1*u,-1*f),d=c.x-h.x,v=c.y-h.y,p=Math.sqrt(d*d+v*v),y=Math.pow(n.idealLength-p,2)/n.elasticity;if(p!==0)var g=y*d/p,m=y*v/p;else var g=0,m=0;s.isLocked||(s.offsetX+=g,s.offsetY+=m),l.isLocked||(l.offsetX-=g,l.offsetY-=m)}}},ig=function(e,r){if(r.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],f=e.layoutNodes[e.idToIndex[u.parentId]],o=f.positionX,l=f.positionY;for(var h=0;h<s;h++){var c=e.layoutNodes[e.idToIndex[i[h]]];if(!c.isLocked){var d=o-c.positionX,v=l-c.positionY,p=Math.sqrt(d*d+v*v);if(p>a){var y=r.gravity*d/p,g=r.gravity*v/p;c.offsetX+=y,c.offsetY+=g}}}}},sg=function(e,r){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var f=l.offsetX,h=l.offsetY,c=0;c<u.length;c++){var d=e.layoutNodes[e.idToIndex[u[c]]];d.offsetX+=f,d.offsetY+=h,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},og=function(e,r){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=ug(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,lg(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},ug=function(e,r,a){var n=Math.sqrt(e*e+r*r);if(n>a)var i={x:a*e/n,y:a*r/n};else var i={x:e,y:r};return i},lg=function t(e,r){var a=e.parentId;if(a!=null){var n=r.layoutNodes[r.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return t(n,r)}},Qo=function(e,r){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var f=n[i];if(f){f.x1=1/0,f.x2=-1/0,f.y1=1/0,f.y2=-1/0;for(var h=0;h<f.length;h++){var c=f[h];f.x1=Math.min(f.x1,c.positionX-c.width/2),f.x2=Math.max(f.x2,c.positionX+c.width/2),f.y1=Math.min(f.y1,c.positionY-c.height/2),f.y2=Math.max(f.y2,c.positionY+c.height/2)}f.w=f.x2-f.x1,f.h=f.y2-f.y1,u+=f.w*f.h}}n.sort(function(m,b){return b.w*b.h-m.w*m.h});for(var d=0,v=0,p=0,y=0,g=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var f=n[i];if(f){for(var h=0;h<f.length;h++){var c=f[h];c.isLocked||(c.positionX+=d-f.x1,c.positionY+=v-f.y1)}d+=f.w+r.componentSpacing,p+=f.w+r.componentSpacing,y=Math.max(y,f.h),p>g&&(v+=y+r.componentSpacing,d=0,p=0,y=0)}}},fg={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function Jo(t){this.options=Ue({},fg,t)}Jo.prototype.run=function(){var t=this.options,e=t,r=t.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=Yt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(ye){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),f=function(Te){if(Te==null)return Math.min(l,u);var be=Math.min(l,u);be==l?l=Te:u=Te},h=function(Te){if(Te==null)return Math.max(l,u);var be=Math.max(l,u);be==l?l=Te:u=Te},c=e.rows,d=e.cols!=null?e.cols:e.columns;if(c!=null&&d!=null)l=c,u=d;else if(c!=null&&d==null)l=c,u=Math.ceil(s/l);else if(c==null&&d!=null)u=d,l=Math.ceil(s/u);else if(u*l>s){var v=f(),p=h();(v-1)*p>=s?f(v-1):(p-1)*v>=s&&h(p-1)}else for(;u*l<s;){var y=f(),g=h();(g+1)*y>=s?h(g+1):f(y+1)}var m=i.w/u,b=i.h/l;if(e.condense&&(m=0,b=0),e.avoidOverlap)for(var E=0;E<n.length;E++){var N=n[E],A=N._private.position;(A.x==null||A.y==null)&&(A.x=0,A.y=0);var x=N.layoutDimensions(e),I=e.avoidOverlapPadding,C=x.w+I,F=x.h+I;m=Math.max(m,C),b=Math.max(b,F)}for(var z={},M=function(Te,be){return!!z["c-"+Te+"-"+be]},X=function(Te,be){z["c-"+Te+"-"+be]=!0},B=0,re=0,q=function(){re++,re>=u&&(re=0,B++)},Z={},ie=0;ie<n.length;ie++){var ue=n[ie],ge=e.position(ue);if(ge&&(ge.row!==void 0||ge.col!==void 0)){var se={row:ge.row,col:ge.col};if(se.col===void 0)for(se.col=0;M(se.row,se.col);)se.col++;else if(se.row===void 0)for(se.row=0;M(se.row,se.col);)se.row++;Z[ue.id()]=se,X(se.row,se.col)}}var ve=function(Te,be){var me,ae;if(Te.locked()||Te.isParent())return!1;var xe=Z[Te.id()];if(xe)me=xe.col*m+m/2+i.x1,ae=xe.row*b+b/2+i.y1;else{for(;M(B,re);)q();me=re*m+m/2+i.x1,ae=B*b+b/2+i.y1,X(B,re),q()}return{x:me,y:ae}};n.layoutPositions(this,e,ve)}return this};var hg={ready:function(){},stop:function(){}};function Bi(t){this.options=Ue({},hg,t)}Bi.prototype.run=function(){var t=this.options,e=t.eles,r=this;return t.cy,r.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),r.one("layoutready",t.ready),r.emit("layoutready"),r.one("layoutstop",t.stop),r.emit("layoutstop"),this},Bi.prototype.stop=function(){return this};var cg={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function jo(t){this.options=Ue({},cg,t)}jo.prototype.run=function(){var t=this.options,e=t.eles,r=e.nodes(),a=Y(t.positions);function n(i){if(t.positions==null)return yf(i.position());if(a)return t.positions(i);var s=t.positions[i._private.data.id];return s??null}return r.layoutPositions(this,t,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var vg={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,r){return!0},ready:void 0,stop:void 0,transform:function(e,r){return r}};function eu(t){this.options=Ue({},vg,t)}eu.prototype.run=function(){var t=this.options,e=t.cy,r=t.eles,a=Yt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return r.nodes().layoutPositions(this,t,n),this};var dg=[{name:"breadthfirst",impl:Xo},{name:"circle",impl:Wo},{name:"concentric",impl:qo},{name:"cose",impl:Yn},{name:"grid",impl:Jo},{name:"null",impl:Bi},{name:"preset",impl:jo},{name:"random",impl:eu}];function tu(t){this.options=t,this.notifications=0}var ru=function(){},au=function(){throw new Error("A headless instance can not render images")};tu.prototype={recalculateRenderedStyle:ru,notify:function(){this.notifications++},init:ru,isHeadless:function(){return!0},png:au,jpg:au};var Fi={};Fi.arrowShapeWidth=.3,Fi.registerArrowShapes=function(){var t=this.arrowShapes={},e=this,r=function(u,f,h,c,d,v,p){var y=d.x-h/2-p,g=d.x+h/2+p,m=d.y-h/2-p,b=d.y+h/2+p,E=y<=u&&u<=g&&m<=f&&f<=b;return E},a=function(u,f,h,c,d){var v=u*Math.cos(c)-f*Math.sin(c),p=u*Math.sin(c)+f*Math.cos(c),y=v*h,g=p*h,m=y+d.x,b=g+d.y;return{x:m,y:b}},n=function(u,f,h,c){for(var d=[],v=0;v<u.length;v+=2){var p=u[v],y=u[v+1];d.push(a(p,y,f,h,c))}return d},i=function(u){for(var f=[],h=0;h<u.length;h++){var c=u[h];f.push(c.x,c.y)}return f},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,f){j(f)&&(f=t[f]),t[u]=Ue({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,d,v,p,y,g){var m=i(n(this.points,v+2*g,p,y)),b=Ht(c,d,m);return b},roughCollide:r,draw:function(c,d,v,p){var y=n(this.points,d,v,p);e.arrowShapeImpl("polygon")(c,y)},spacing:function(c){return 0},gap:s},f)};o("none",{collide:vn,roughCollide:vn,draw:ii,spacing:vs,gap:vs}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:t.triangle.points,controlPoint:[0,-.15],roughCollide:r,draw:function(u,f,h,c,d){var v=n(this.points,f,h,c),p=this.controlPoint,y=a(p[0],p[1],f,h,c);e.arrowShapeImpl(this.name)(u,v,y)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,f,h,c,d,v,p){var y=i(n(this.points,h+2*p,c,d)),g=i(n(this.pointsTee,h+2*p,c,d)),m=Ht(u,f,y)||Ht(u,f,g);return m},draw:function(u,f,h,c,d){var v=n(this.points,f,h,c),p=n(this.pointsTee,f,h,c);e.arrowShapeImpl(this.name)(u,v,p)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,f,h,c,d,v,p){var y=d,g=Math.pow(y.x-u,2)+Math.pow(y.y-f,2)<=Math.pow((h+2*p)*this.radius,2),m=i(n(this.points,h+2*p,c,d));return Ht(u,f,m)||g},draw:function(u,f,h,c,d){var v=n(this.pointsTr,f,h,c);e.arrowShapeImpl(this.name)(u,v,c.x,c.y,this.radius*f)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,f){var h=this.baseCrossLinePts.slice(),c=f/u,d=3,v=5;return h[d]=h[d]-c,h[v]=h[v]-c,h},collide:function(u,f,h,c,d,v,p){var y=i(n(this.points,h+2*p,c,d)),g=i(n(this.crossLinePts(h,v),h+2*p,c,d)),m=Ht(u,f,y)||Ht(u,f,g);return m},draw:function(u,f,h,c,d){var v=n(this.points,f,h,c),p=n(this.crossLinePts(f,d),f,h,c);e.arrowShapeImpl(this.name)(u,v,p)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,f,h,c,d,v,p){var y=d,g=Math.pow(y.x-u,2)+Math.pow(y.y-f,2)<=Math.pow((h+2*p)*this.radius,2);return g},draw:function(u,f,h,c,d){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*f)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var _r={};_r.projectIntoViewport=function(t,e){var r=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=r.pan(),l=r.zoom(),u=((t-n)/s-o.x)/l,f=((e-i)/s-o.y)/l;return[u,f]},_r.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var t=this.container,e=t.getBoundingClientRect(),r=this.cy.window().getComputedStyle(t),a=function(g){return parseFloat(r.getPropertyValue(g))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=t.clientWidth,o=t.clientHeight,l=n.left+n.right,u=n.top+n.bottom,f=i.left+i.right,h=e.width/(s+f),c=s-l,d=o-u,v=e.left+n.left+i.left,p=e.top+n.top+i.top;return this.containerBB=[v,p,c,d,h]},_r.invalidateContainerClientCoordsCache=function(){this.containerBB=null},_r.findNearestElement=function(t,e,r,a){return this.findNearestElements(t,e,r,a)[0]},_r.findNearestElements=function(t,e,r,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),f=(a?24:8)/l,h=(a?8:2)/l,c=(a?8:2)/l,d=1/0,v,p;r&&(s=s.interactive);function y(x,I){if(x.isNode()){if(p)return;p=x,o.push(x)}if(x.isEdge()&&(I==null||I<d))if(v){if(v.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&v.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var C=0;C<o.length;C++)if(o[C].isEdge()){o[C]=x,v=x,d=I??d;break}}}else o.push(x),v=x,d=I??d}function g(x){var I=x.outerWidth()+2*h,C=x.outerHeight()+2*h,F=I/2,z=C/2,M=x.position();if(M.x-F<=t&&t<=M.x+F&&M.y-z<=e&&e<=M.y+z){var X=i.nodeShapes[n.getNodeShape(x)];if(X.checkPoint(t,e,0,I,C,M.x,M.y))return y(x,0),!0}}function m(x){var I=x._private,C=I.rscratch,F=x.pstyle("width").pfValue,z=x.pstyle("arrow-scale").value,M=F/2+f,X=M*M,B=M*2,ie=I.source,ue=I.target,re;if(C.edgeType==="segments"||C.edgeType==="straight"||C.edgeType==="haystack"){for(var q=C.allpts,Z=0;Z+3<q.length;Z+=2)if(Nf(t,e,q[Z],q[Z+1],q[Z+2],q[Z+3],B)&&X>(re=Pf(t,e,q[Z],q[Z+1],q[Z+2],q[Z+3])))return y(x,re),!0}else if(C.edgeType==="bezier"||C.edgeType==="multibezier"||C.edgeType==="self"||C.edgeType==="compound"){for(var q=C.allpts,Z=0;Z+5<C.allpts.length;Z+=4)if(Mf(t,e,q[Z],q[Z+1],q[Z+2],q[Z+3],q[Z+4],q[Z+5],B)&&X>(re=kf(t,e,q[Z],q[Z+1],q[Z+2],q[Z+3],q[Z+4],q[Z+5])))return y(x,re),!0}for(var ie=ie||I.source,ue=ue||I.target,ge=n.getArrowWidth(F,z),se=[{name:"source",x:C.arrowStartX,y:C.arrowStartY,angle:C.srcArrowAngle},{name:"target",x:C.arrowEndX,y:C.arrowEndY,angle:C.tgtArrowAngle},{name:"mid-source",x:C.midX,y:C.midY,angle:C.midsrcArrowAngle},{name:"mid-target",x:C.midX,y:C.midY,angle:C.midtgtArrowAngle}],Z=0;Z<se.length;Z++){var ve=se[Z],ye=i.arrowShapes[x.pstyle(ve.name+"-arrow-shape").value],Te=x.pstyle("width").pfValue;if(ye.roughCollide(t,e,ge,ve.angle,{x:ve.x,y:ve.y},Te,f)&&ye.collide(t,e,ge,ve.angle,{x:ve.x,y:ve.y},Te,f))return y(x),!0}u&&o.length>0&&(g(ie),g(ue))}function b(x,I,C){return tr(x,I,C)}function E(x,I){var C=x._private,F=c,z;I?z=I+"-":z="",x.boundingBox();var M=C.labelBounds[I||"main"],X=x.pstyle(z+"label").value,B=x.pstyle("text-events").strValue==="yes";if(!(!B||!X)){var re=b(C.rscratch,"labelX",I),q=b(C.rscratch,"labelY",I),Z=b(C.rscratch,"labelAngle",I),ie=x.pstyle(z+"text-margin-x").pfValue,ue=x.pstyle(z+"text-margin-y").pfValue,ge=M.x1-F-ie,se=M.x2+F-ie,ve=M.y1-F-ue,ye=M.y2+F-ue;if(Z){var Te=Math.cos(Z),be=Math.sin(Z),me=function(We,Re){return We=We-re,Re=Re-q,{x:We*Te-Re*be+re,y:We*be+Re*Te+q}},ae=me(ge,ve),xe=me(ge,ye),Ce=me(se,ve),Oe=me(se,ye),Me=[ae.x+ie,ae.y+ue,Ce.x+ie,Ce.y+ue,Oe.x+ie,Oe.y+ue,xe.x+ie,xe.y+ue];if(Ht(t,e,Me))return y(x),!0}else if(ra(M,t,e))return y(x),!0}}for(var N=s.length-1;N>=0;N--){var A=s[N];A.isNode()?g(A)||E(A):m(A)||E(A)||E(A,"source")||E(A,"target")}return o},_r.getAllInBox=function(t,e,r,a){var n=this.getCachedZSortedEles().interactive,i=[],s=Math.min(t,r),o=Math.max(t,r),l=Math.min(e,a),u=Math.max(e,a);t=s,r=o,e=l,a=u;for(var f=Yt({x1:t,y1:e,x2:r,y2:a}),h=0;h<n.length;h++){var c=n[h];if(c.isNode()){var d=c,v=d.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});fi(f,v)&&!ws(v,f)&&i.push(d)}else{var p=c,y=p._private,g=y.rscratch;if(g.startX!=null&&g.startY!=null&&!ra(f,g.startX,g.startY)||g.endX!=null&&g.endY!=null&&!ra(f,g.endX,g.endY))continue;if(g.edgeType==="bezier"||g.edgeType==="multibezier"||g.edgeType==="self"||g.edgeType==="compound"||g.edgeType==="segments"||g.edgeType==="haystack"){for(var m=y.rstyle.bezierPts||y.rstyle.linePts||y.rstyle.haystackPts,b=!0,E=0;E<m.length;E++)if(!Of(f,m[E])){b=!1;break}b&&i.push(p)}else(g.edgeType==="haystack"||g.edgeType==="straight")&&i.push(p)}}return i};var Xn={};Xn.calculateArrowAngles=function(t){var e=t._private.rscratch,r=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,f,h,c,d,g,m;if(r?(f=e.haystackPts[0],h=e.haystackPts[1],c=e.haystackPts[2],d=e.haystackPts[3]):(f=e.arrowStartX,h=e.arrowStartY,c=e.arrowEndX,d=e.arrowEndY),g=e.midX,m=e.midY,i)l=f-e.segpts[0],u=h-e.segpts[1];else if(n||s||o||a){var v=e.allpts,p=Lt(v[0],v[2],v[4],.1),y=Lt(v[1],v[3],v[5],.1);l=f-p,u=h-y}else l=f-g,u=h-m;e.srcArrowAngle=pn(l,u);var g=e.midX,m=e.midY;if(r&&(g=(f+c)/2,m=(h+d)/2),l=c-f,u=d-h,i){var v=e.allpts;if(v.length/2%2===0){var b=v.length/2,E=b-2;l=v[b]-v[E],u=v[b+1]-v[E+1]}else{var b=v.length/2-1,E=b-2,N=b+2;l=v[b]-v[E],u=v[b+1]-v[E+1]}}else if(n||s||o){var v=e.allpts,A=e.ctrlpts,x,I,C,F;if(A.length/2%2===0){var z=v.length/2-1,M=z+2,X=M+2;x=Lt(v[z],v[M],v[X],0),I=Lt(v[z+1],v[M+1],v[X+1],0),C=Lt(v[z],v[M],v[X],1e-4),F=Lt(v[z+1],v[M+1],v[X+1],1e-4)}else{var M=v.length/2-1,z=M-2,X=M+2;x=Lt(v[z],v[M],v[X],.4999),I=Lt(v[z+1],v[M+1],v[X+1],.4999),C=Lt(v[z],v[M],v[X],.5),F=Lt(v[z+1],v[M+1],v[X+1],.5)}l=C-x,u=F-I}if(e.midtgtArrowAngle=pn(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var v=e.allpts;if(v.length/2%2!==0){var b=v.length/2-1,N=b+2;l=-(v[N]-v[b]),u=-(v[N+1]-v[b+1])}}if(e.midsrcArrowAngle=pn(l,u),i)l=c-e.segpts[e.segpts.length-2],u=d-e.segpts[e.segpts.length-1];else if(n||s||o||a){var v=e.allpts,B=v.length,p=Lt(v[B-6],v[B-4],v[B-2],.9),y=Lt(v[B-5],v[B-3],v[B-1],.9);l=c-p,u=d-y}else l=c-g,u=d-m;e.tgtArrowAngle=pn(l,u)},Xn.getArrowWidth=Xn.getArrowHeight=function(t,e){var r=this.arrowWidthCache=this.arrowWidthCache||{},a=r[t+", "+e];return a||(a=Math.max(Math.pow(t*13.37,.9),29)*e,r[t+", "+e]=a,a)};var _t={};_t.findHaystackPoints=function(t){for(var e=0;e<t.length;e++){var r=t[e],a=r._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),f=s.width(),h=o.width(),c=s.height(),d=o.height(),v=r.pstyle("haystack-radius").value,p=v/2;n.haystackPts=n.allpts=[n.source.x*f*p+l.x,n.source.y*c*p+l.y,n.target.x*h*p+u.x,n.target.y*d*p+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(r),this.calculateArrowAngles(r),this.recalculateEdgeLabelProjections(r),this.calculateLabelAngles(r)}},_t.findSegmentsPoints=function(t,e){var r=t._private.rscratch,a=e.posPts,n=e.intersectionPts,i=e.vectorNormInverse,s=t.pstyle("edge-distances").value,o=t.pstyle("segment-weights"),l=t.pstyle("segment-distances"),u=Math.min(o.pfValue.length,l.pfValue.length);r.edgeType="segments",r.segpts=[];for(var f=0;f<u;f++){var h=o.pfValue[f],c=l.pfValue[f],d=1-h,v=h,p=s==="node-position"?a:n,y={x:p.x1*d+p.x2*v,y:p.y1*d+p.y2*v};r.segpts.push(y.x+i.x*c,y.y+i.y*c)}},_t.findLoopPoints=function(t,e,r,a){var n=t._private.rscratch,i=e.dirCounts,s=e.srcPos,o=t.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=t.pstyle("loop-direction").pfValue,f=t.pstyle("loop-sweep").pfValue,h=t.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=r,d=h;a&&(c=0,d=l);var v=u-Math.PI/2,p=v-f/2,y=v+f/2,g=u+"_"+f;c=i[g]===void 0?i[g]=0:++i[g],n.ctrlpts=[s.x+Math.cos(p)*1.4*d*(c/3+1),s.y+Math.sin(p)*1.4*d*(c/3+1),s.x+Math.cos(y)*1.4*d*(c/3+1),s.y+Math.sin(y)*1.4*d*(c/3+1)]},_t.findCompoundLoopPoints=function(t,e,r,a){var n=t._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,f=e.tgtH,h=t.pstyle("control-point-step-size").pfValue,c=t.pstyle("control-point-distances"),d=c?c.pfValue[0]:void 0,v=r,p=h;a&&(v=0,p=d);var y=50,g={x:i.x-o/2,y:i.y-l/2},m={x:s.x-u/2,y:s.y-f/2},b={x:Math.min(g.x,m.x),y:Math.min(g.y,m.y)},E=.5,N=Math.max(E,Math.log(o*.01)),A=Math.max(E,Math.log(u*.01));n.ctrlpts=[b.x,b.y-(1+Math.pow(y,1.12)/100)*p*(v/3+1)*N,b.x-(1+Math.pow(y,1.12)/100)*p*(v/3+1)*A,b.y]},_t.findStraightEdgePoints=function(t){t._private.rscratch.edgeType="straight"},_t.findBezierPoints=function(t,e,r,a,n){var i=t._private.rscratch,s=e.vectorNormInverse,o=e.posPts,l=e.intersectionPts,u=t.pstyle("edge-distances").value,f=t.pstyle("control-point-step-size").pfValue,h=t.pstyle("control-point-distances"),c=t.pstyle("control-point-weights"),d=h&&c?Math.min(h.value.length,c.value.length):1,v=h?h.pfValue[0]:void 0,p=c.value[0],y=a;i.edgeType=y?"multibezier":"bezier",i.ctrlpts=[];for(var g=0;g<d;g++){var m=(.5-e.eles.length/2+r)*f*(n?-1:1),b=void 0,E=bs(m);y&&(v=h?h.pfValue[g]:f,p=c.value[g]),a?b=v:b=v!==void 0?E*v:void 0;var N=b!==void 0?b:m,A=1-p,x=p,I=u==="node-position"?o:l,C={x:I.x1*A+I.x2*x,y:I.y1*A+I.y2*x};i.ctrlpts.push(C.x+s.x*N,C.y+s.y*N)}},_t.findTaxiPoints=function(t,e){var r=t._private.rscratch;r.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",f=e.posPts,h=e.srcW,c=e.srcH,d=e.tgtW,v=e.tgtH,p=t.pstyle("edge-distances").value,y=p!=="node-position",g=t.pstyle("taxi-direction").value,m=g,b=t.pstyle("taxi-turn"),E=b.units==="%",N=b.pfValue,A=N<0,x=t.pstyle("taxi-turn-min-distance").pfValue,I=y?(h+d)/2:0,C=y?(c+v)/2:0,F=f.x2-f.x1,z=f.y2-f.y1,M=function(Le,Ve){return Le>0?Math.max(Le-Ve,0):Math.min(Le+Ve,0)},X=M(F,I),B=M(z,C),re=!1;m===u?g=Math.abs(X)>Math.abs(B)?n:a:m===l||m===o?(g=a,re=!0):(m===i||m===s)&&(g=n,re=!0);var q=g===a,Z=q?B:X,ie=q?z:F,ue=bs(ie),ge=!1;!(re&&(E||A))&&(m===o&&ie<0||m===l&&ie>0||m===i&&ie>0||m===s&&ie<0)&&(ue*=-1,Z=ue*Math.abs(Z),ge=!0);var se;if(E){var ve=N<0?1+N:N;se=ve*Z}else{var ye=N<0?Z:0;se=ye+N*ue}var Te=function(Le){return Math.abs(Le)<x||Math.abs(Le)>=Math.abs(Z)},be=Te(se),me=Te(Math.abs(Z)-Math.abs(se)),ae=be||me;if(ae&&!ge)if(q){var xe=Math.abs(ie)<=c/2,Ce=Math.abs(F)<=d/2;if(xe){var Oe=(f.x1+f.x2)/2,Me=f.y1,He=f.y2;r.segpts=[Oe,Me,Oe,He]}else if(Ce){var We=(f.y1+f.y2)/2,Re=f.x1,Ie=f.x2;r.segpts=[Re,We,Ie,We]}else r.segpts=[f.x1,f.y2]}else{var Ge=Math.abs(ie)<=h/2,Fe=Math.abs(z)<=v/2;if(Ge){var ke=(f.y1+f.y2)/2,ze=f.x1,je=f.x2;r.segpts=[ze,ke,je,ke]}else if(Fe){var Ze=(f.x1+f.x2)/2,Ye=f.y1,ct=f.y2;r.segpts=[Ze,Ye,Ze,ct]}else r.segpts=[f.x2,f.y1]}else if(q){var De=f.y1+se+(y?c/2*ue:0),k=f.x1,he=f.x2;r.segpts=[k,De,he,De]}else{var Se=f.x1+se+(y?h/2*ue:0),Ee=f.y1,we=f.y2;r.segpts=[Se,Ee,Se,we]}},_t.tryToCorrectInvalidPoints=function(t,e){var r=t._private.rscratch;if(r.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,f=e.tgtShape,h=!R(r.startX)||!R(r.startY),c=!R(r.arrowStartX)||!R(r.arrowStartY),d=!R(r.endX)||!R(r.endY),v=!R(r.arrowEndX)||!R(r.arrowEndY),p=3,y=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth,g=p*y,m=Br({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.startX,y:r.startY}),b=m<g,E=Br({x:r.ctrlpts[0],y:r.ctrlpts[1]},{x:r.endX,y:r.endY}),N=E<g,A=!1;if(h||c||b){A=!0;var x={x:r.ctrlpts[0]-a.x,y:r.ctrlpts[1]-a.y},I=Math.sqrt(x.x*x.x+x.y*x.y),C={x:x.x/I,y:x.y/I},F=Math.max(i,s),z={x:r.ctrlpts[0]+C.x*2*F,y:r.ctrlpts[1]+C.y*2*F},M=u.intersectLine(a.x,a.y,i,s,z.x,z.y,0);b?(r.ctrlpts[0]=r.ctrlpts[0]+C.x*(g-m),r.ctrlpts[1]=r.ctrlpts[1]+C.y*(g-m)):(r.ctrlpts[0]=M[0]+C.x*g,r.ctrlpts[1]=M[1]+C.y*g)}if(d||v||N){A=!0;var X={x:r.ctrlpts[0]-n.x,y:r.ctrlpts[1]-n.y},B=Math.sqrt(X.x*X.x+X.y*X.y),re={x:X.x/B,y:X.y/B},q=Math.max(i,s),Z={x:r.ctrlpts[0]+re.x*2*q,y:r.ctrlpts[1]+re.y*2*q},ie=f.intersectLine(n.x,n.y,o,l,Z.x,Z.y,0);N?(r.ctrlpts[0]=r.ctrlpts[0]+re.x*(g-E),r.ctrlpts[1]=r.ctrlpts[1]+re.y*(g-E)):(r.ctrlpts[0]=ie[0]+re.x*g,r.ctrlpts[1]=ie[1]+re.y*g)}A&&this.findEndpoints(t)}},_t.storeAllpts=function(t){var e=t._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var r=0;r+1<e.ctrlpts.length;r+=2)e.allpts.push(e.ctrlpts[r],e.ctrlpts[r+1]),r+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[r]+e.ctrlpts[r+2])/2,(e.ctrlpts[r+1]+e.ctrlpts[r+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=Lt(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=Lt(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments")if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.segpts.length%4===0){var i=e.segpts.length/2,s=i-2;e.midX=(e.segpts[s]+e.segpts[i])/2,e.midY=(e.segpts[s+1]+e.segpts[i+1])/2}else{var o=e.segpts.length/2-1;e.midX=e.segpts[o],e.midY=e.segpts[o+1]}},_t.checkForInvalidEdgeWarning=function(t){var e=t[0]._private.rscratch;e.nodesOverlap||R(e.startX)&&R(e.startY)&&R(e.endX)&&R(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,ft("Edge `"+t.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},_t.findEdgeControlPoints=function(t){var e=this;if(!(!t||t.length===0)){for(var r=this,a=r.cy,n=a.hasCompoundNodes(),i={map:new lr,get:function(x){var I=this.map.get(x[0]);return I!=null?I.get(x[1]):null},set:function(x,I){var C=this.map.get(x[0]);C==null&&(C=new lr,this.map.set(x[0],C)),C.set(x[1],I)}},s=[],o=[],l=0;l<t.length;l++){var u=t[l],f=u._private,h=u.pstyle("curve-style").value;if(!(u.removed()||!u.takesUpSpace())){if(h==="haystack"){o.push(u);continue}var c=h==="unbundled-bezier"||h==="segments"||h==="straight"||h==="straight-triangle"||h==="taxi",d=h==="unbundled-bezier"||h==="bezier",v=f.source,p=f.target,y=v.poolIndex(),g=p.poolIndex(),m=[y,g].sort(),b=i.get(m);b==null&&(b={eles:[]},i.set(m,b),s.push(m)),b.eles.push(u),c&&(b.hasUnbundled=!0),d&&(b.hasBezier=!0)}}for(var E=function(x){var I=s[x],C=i.get(I),F=void 0;if(!C.hasUnbundled){var z=C.eles[0].parallelEdges().filter(function(ct){return ct.isBundledBezier()});si(C.eles),z.forEach(function(ct){return C.eles.push(ct)}),C.eles.sort(function(ct,De){return ct.poolIndex()-De.poolIndex()})}var M=C.eles[0],X=M.source(),B=M.target();if(X.poolIndex()>B.poolIndex()){var re=X;X=B,B=re}var q=C.srcPos=X.position(),Z=C.tgtPos=B.position(),ie=C.srcW=X.outerWidth(),ue=C.srcH=X.outerHeight(),ge=C.tgtW=B.outerWidth(),se=C.tgtH=B.outerHeight(),ve=C.srcShape=r.nodeShapes[e.getNodeShape(X)],ye=C.tgtShape=r.nodeShapes[e.getNodeShape(B)];C.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var Te=0;Te<C.eles.length;Te++){var be=C.eles[Te],me=be[0]._private.rscratch,ae=be.pstyle("curve-style").value,xe=ae==="unbundled-bezier"||ae==="segments"||ae==="taxi",Ce=!X.same(be.source());if(!C.calculatedIntersection&&X!==B&&(C.hasBezier||C.hasUnbundled)){C.calculatedIntersection=!0;var Oe=ve.intersectLine(q.x,q.y,ie,ue,Z.x,Z.y,0),Me=C.srcIntn=Oe,He=ye.intersectLine(Z.x,Z.y,ge,se,q.x,q.y,0),We=C.tgtIntn=He,Re=C.intersectionPts={x1:Oe[0],x2:He[0],y1:Oe[1],y2:He[1]},Ie=C.posPts={x1:q.x,x2:Z.x,y1:q.y,y2:Z.y},Ge=He[1]-Oe[1],Fe=He[0]-Oe[0],ke=Math.sqrt(Fe*Fe+Ge*Ge),ze=C.vector={x:Fe,y:Ge},je=C.vectorNorm={x:ze.x/ke,y:ze.y/ke},Ze={x:-je.y,y:je.x};C.nodesOverlap=!R(ke)||ye.checkPoint(Oe[0],Oe[1],0,ge,se,Z.x,Z.y)||ve.checkPoint(He[0],He[1],0,ie,ue,q.x,q.y),C.vectorNormInverse=Ze,F={nodesOverlap:C.nodesOverlap,dirCounts:C.dirCounts,calculatedIntersection:!0,hasBezier:C.hasBezier,hasUnbundled:C.hasUnbundled,eles:C.eles,srcPos:Z,tgtPos:q,srcW:ge,srcH:se,tgtW:ie,tgtH:ue,srcIntn:We,tgtIntn:Me,srcShape:ye,tgtShape:ve,posPts:{x1:Ie.x2,y1:Ie.y2,x2:Ie.x1,y2:Ie.y1},intersectionPts:{x1:Re.x2,y1:Re.y2,x2:Re.x1,y2:Re.y1},vector:{x:-ze.x,y:-ze.y},vectorNorm:{x:-je.x,y:-je.y},vectorNormInverse:{x:-Ze.x,y:-Ze.y}}}var Ye=Ce?F:C;me.nodesOverlap=Ye.nodesOverlap,me.srcIntn=Ye.srcIntn,me.tgtIntn=Ye.tgtIntn,n&&(X.isParent()||X.isChild()||B.isParent()||B.isChild())&&(X.parents().anySame(B)||B.parents().anySame(X)||X.same(B)&&X.isParent())?e.findCompoundLoopPoints(be,Ye,Te,xe):X===B?e.findLoopPoints(be,Ye,Te,xe):ae==="segments"?e.findSegmentsPoints(be,Ye):ae==="taxi"?e.findTaxiPoints(be,Ye):ae==="straight"||!xe&&C.eles.length%2===1&&Te===Math.floor(C.eles.length/2)?e.findStraightEdgePoints(be):e.findBezierPoints(be,Ye,Te,xe,Ce),e.findEndpoints(be),e.tryToCorrectInvalidPoints(be,Ye),e.checkForInvalidEdgeWarning(be),e.storeAllpts(be),e.storeEdgeProjections(be),e.calculateArrowAngles(be),e.recalculateEdgeLabelProjections(be),e.calculateLabelAngles(be)}},N=0;N<s.length;N++)E(N);this.findHaystackPoints(o)}};function nu(t){var e=[];if(t!=null){for(var r=0;r<t.length;r+=2){var a=t[r],n=t[r+1];e.push({x:a,y:n})}return e}}_t.getSegmentPoints=function(t){var e=t[0]._private.rscratch,r=e.edgeType;if(r==="segments")return this.recalculateRenderedStyle(t),nu(e.segpts)},_t.getControlPoints=function(t){var e=t[0]._private.rscratch,r=e.edgeType;if(r==="bezier"||r==="multibezier"||r==="self"||r==="compound")return this.recalculateRenderedStyle(t),nu(e.ctrlpts)},_t.getEdgeMidpoint=function(t){var e=t[0]._private.rscratch;return this.recalculateRenderedStyle(t),{x:e.midX,y:e.midY}};var Ja={};Ja.manualEndptToPx=function(t,e){var r=this,a=t.position(),n=t.outerWidth(),i=t.outerHeight();if(e.value.length===2){var s=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(s[0]=s[0]*n),e.units[1]==="%"&&(s[1]=s[1]*i),s[0]+=a.x,s[1]+=a.y,s}else{var o=e.pfValue[0];o=-Math.PI/2+o;var l=2*Math.max(n,i),u=[a.x+Math.cos(o)*l,a.y+Math.sin(o)*l];return r.nodeShapes[this.getNodeShape(t)].intersectLine(a.x,a.y,n,i,u[0],u[1],0)}},Ja.findEndpoints=function(t){var e=this,r,a=t.source()[0],n=t.target()[0],i=a.position(),s=n.position(),o=t.pstyle("target-arrow-shape").value,l=t.pstyle("source-arrow-shape").value,u=t.pstyle("target-distance-from-node").pfValue,f=t.pstyle("source-distance-from-node").pfValue,h=t.pstyle("curve-style").value,c=t._private.rscratch,d=c.edgeType,v=h==="taxi",p=d==="self"||d==="compound",y=d==="bezier"||d==="multibezier"||p,g=d!=="bezier",m=d==="straight"||d==="segments",b=d==="segments",E=y||g||m,N=p||v,A=t.pstyle("source-endpoint"),x=N?"outside-to-node":A.value,I=t.pstyle("target-endpoint"),C=N?"outside-to-node":I.value;c.srcManEndpt=A,c.tgtManEndpt=I;var F,z,M,X;if(y){var B=[c.ctrlpts[0],c.ctrlpts[1]],re=g?[c.ctrlpts[c.ctrlpts.length-2],c.ctrlpts[c.ctrlpts.length-1]]:B;F=re,z=B}else if(m){var q=b?c.segpts.slice(0,2):[s.x,s.y],Z=b?c.segpts.slice(c.segpts.length-2):[i.x,i.y];F=Z,z=q}if(C==="inside-to-node")r=[s.x,s.y];else if(I.units)r=this.manualEndptToPx(n,I);else if(C==="outside-to-line")r=c.tgtIntn;else if(C==="outside-to-node"||C==="outside-to-node-or-label"?M=F:(C==="outside-to-line"||C==="outside-to-line-or-label")&&(M=[i.x,i.y]),r=e.nodeShapes[this.getNodeShape(n)].intersectLine(s.x,s.y,n.outerWidth(),n.outerHeight(),M[0],M[1],0),C==="outside-to-node-or-label"||C==="outside-to-line-or-label"){var ie=n._private.rscratch,ue=ie.labelWidth,ge=ie.labelHeight,se=ie.labelX,ve=ie.labelY,ye=ue/2,Te=ge/2,be=n.pstyle("text-valign").value;be==="top"?ve-=Te:be==="bottom"&&(ve+=Te);var me=n.pstyle("text-halign").value;me==="left"?se-=ye:me==="right"&&(se+=ye);var ae=Fa(M[0],M[1],[se-ye,ve-Te,se+ye,ve-Te,se+ye,ve+Te,se-ye,ve+Te],s.x,s.y);if(ae.length>0){var xe=i,Ce=Fr(xe,ea(r)),Oe=Fr(xe,ea(ae)),Me=Ce;if(Oe<Ce&&(r=ae,Me=Oe),ae.length>2){var He=Fr(xe,{x:ae[2],y:ae[3]});He<Me&&(r=[ae[2],ae[3]])}}}var We=mn(r,F,e.arrowShapes[o].spacing(t)+u),Re=mn(r,F,e.arrowShapes[o].gap(t)+u);if(c.endX=Re[0],c.endY=Re[1],c.arrowEndX=We[0],c.arrowEndY=We[1],x==="inside-to-node")r=[i.x,i.y];else if(A.units)r=this.manualEndptToPx(a,A);else if(x==="outside-to-line")r=c.srcIntn;else if(x==="outside-to-node"||x==="outside-to-node-or-label"?X=z:(x==="outside-to-line"||x==="outside-to-line-or-label")&&(X=[s.x,s.y]),r=e.nodeShapes[this.getNodeShape(a)].intersectLine(i.x,i.y,a.outerWidth(),a.outerHeight(),X[0],X[1],0),x==="outside-to-node-or-label"||x==="outside-to-line-or-label"){var Ie=a._private.rscratch,Ge=Ie.labelWidth,Fe=Ie.labelHeight,ke=Ie.labelX,ze=Ie.labelY,je=Ge/2,Ze=Fe/2,Ye=a.pstyle("text-valign").value;Ye==="top"?ze-=Ze:Ye==="bottom"&&(ze+=Ze);var ct=a.pstyle("text-halign").value;ct==="left"?ke-=je:ct==="right"&&(ke+=je);var De=Fa(X[0],X[1],[ke-je,ze-Ze,ke+je,ze-Ze,ke+je,ze+Ze,ke-je,ze+Ze],i.x,i.y);if(De.length>0){var k=s,he=Fr(k,ea(r)),Se=Fr(k,ea(De)),Ee=he;if(Se<he&&(r=[De[0],De[1]],Ee=Se),De.length>2){var we=Fr(k,{x:De[2],y:De[3]});we<Ee&&(r=[De[2],De[3]])}}}var qe=mn(r,z,e.arrowShapes[l].spacing(t)+f),Le=mn(r,z,e.arrowShapes[l].gap(t)+f);c.startX=Le[0],c.startY=Le[1],c.arrowStartX=qe[0],c.arrowStartY=qe[1],E&&(!R(c.startX)||!R(c.startY)||!R(c.endX)||!R(c.endY)?c.badLine=!0:c.badLine=!1)},Ja.getSourceEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}},Ja.getTargetEndpoint=function(t){var e=t[0]._private.rscratch;switch(this.recalculateRenderedStyle(t),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var zi={};function gg(t,e,r){for(var a=function(u,f,h,c){return Lt(u,f,h,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<t.bezierProjPcts.length;s++){var o=t.bezierProjPcts[s];i.push({x:a(r[0],r[2],r[4],o),y:a(r[1],r[3],r[5],o)})}}zi.storeEdgeProjections=function(t){var e=t._private,r=e.rscratch,a=r.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<r.allpts.length;n+=4)gg(this,t,r.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<r.allpts.length;n+=2)i.push({x:r.allpts[n],y:r.allpts[n+1]});else if(a==="haystack"){var s=r.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(t.pstyle("width").pfValue,t.pstyle("arrow-scale").value)*this.arrowShapeWidth},zi.recalculateEdgeProjections=function(t){this.findEdgeControlPoints(t)};var hr={};hr.recalculateNodeLabelProjection=function(t){var e=t.pstyle("label").strValue;if(!Pe(e)){var r,a,n=t._private,i=t.width(),s=t.height(),o=t.padding(),l=t.position(),u=t.pstyle("text-halign").strValue,f=t.pstyle("text-valign").strValue,h=n.rscratch,c=n.rstyle;switch(u){case"left":r=l.x-i/2-o;break;case"right":r=l.x+i/2+o;break;default:r=l.x}switch(f){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}h.labelX=r,h.labelY=a,c.labelX=r,c.labelY=a,this.calculateLabelAngles(t),this.applyLabelDimensions(t)}};var iu=function(e,r){var a=Math.atan(r/e);return e===0&&a<0&&(a=a*-1),a},su=function(e,r){var a=r.x-e.x,n=r.y-e.y;return iu(a,n)},pg=function(e,r,a,n){var i=Pa(0,n-.001,1),s=Pa(0,n+.001,1),o=ta(e,r,a,i),l=ta(e,r,a,s);return su(o,l)};hr.recalculateEdgeLabelProjections=function(t){var e,r=t._private,a=r.rscratch,n=this,i={mid:t.pstyle("label").strValue,source:t.pstyle("source-label").strValue,target:t.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(h,c,d){Tr(r.rscratch,h,c,d),Tr(r.rstyle,h,c,d)};s("labelX",null,e.x),s("labelY",null,e.y);var o=iu(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function f(){if(f.cache)return f.cache;for(var h=[],c=0;c+5<a.allpts.length;c+=4){var d={x:a.allpts[c],y:a.allpts[c+1]},v={x:a.allpts[c+2],y:a.allpts[c+3]},p={x:a.allpts[c+4],y:a.allpts[c+5]};h.push({p0:d,p1:v,p2:p,startDist:0,length:0,segments:[]})}var y=r.rstyle.bezierPts,g=n.bezierProjPcts.length;function m(x,I,C,F,z){var M=Br(I,C),X=x.segments[x.segments.length-1],B={p0:I,p1:C,t0:F,t1:z,startDist:X?X.startDist+X.length:0,length:M};x.segments.push(B),x.length+=M}for(var b=0;b<h.length;b++){var E=h[b],N=h[b-1];N&&(E.startDist=N.startDist+N.length),m(E,E.p0,y[b*g],0,n.bezierProjPcts[0]);for(var A=0;A<g-1;A++)m(E,y[b*g+A],y[b*g+A+1],n.bezierProjPcts[A],n.bezierProjPcts[A+1]);m(E,y[b*g+g-1],E.p2,n.bezierProjPcts[g-1],1)}return f.cache=h},u=function(h){var c,d=h==="source";if(i[h]){var v=t.pstyle(h+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var p=l(),y,g=0,m=0,b=0;b<p.length;b++){for(var E=p[d?b:p.length-1-b],N=0;N<E.segments.length;N++){var A=E.segments[d?N:E.segments.length-1-N],x=b===p.length-1&&N===E.segments.length-1;if(g=m,m+=A.length,m>=v||x){y={cp:E,segment:A};break}}if(y)break}var I=y.cp,C=y.segment,F=(v-g)/C.length,z=C.t1-C.t0,M=d?C.t0+z*F:C.t1-z*F;M=Pa(0,M,1),e=ta(I.p0,I.p1,I.p2,M),c=pg(I.p0,I.p1,I.p2,M);break}case"straight":case"segments":case"haystack":{for(var X=0,B,re,q,Z,ie=a.allpts.length,ue=0;ue+3<ie&&(d?(q={x:a.allpts[ue],y:a.allpts[ue+1]},Z={x:a.allpts[ue+2],y:a.allpts[ue+3]}):(q={x:a.allpts[ie-2-ue],y:a.allpts[ie-1-ue]},Z={x:a.allpts[ie-4-ue],y:a.allpts[ie-3-ue]}),B=Br(q,Z),re=X,X+=B,!(X>=v));ue+=2);var ge=v-re,se=ge/B;se=Pa(0,se,1),e=Cf(q,Z,se),c=su(q,Z);break}}s("labelX",h,e.x),s("labelY",h,e.y),s("labelAutoAngle",h,c)}};u("source"),u("target"),this.applyLabelDimensions(t)}},hr.applyLabelDimensions=function(t){this.applyPrefixedLabelDimensions(t),t.isEdge()&&(this.applyPrefixedLabelDimensions(t,"source"),this.applyPrefixedLabelDimensions(t,"target"))},hr.applyPrefixedLabelDimensions=function(t,e){var r=t._private,a=this.getLabelText(t,e),n=this.calculateLabelDimensions(t,a),i=t.pstyle("line-height").pfValue,s=t.pstyle("text-wrap").strValue,o=tr(r.rscratch,"labelWrapCachedLines",e)||[],l=s!=="wrap"?1:Math.max(o.length,1),u=n.height/l,f=u*i,h=n.width,c=n.height+(l-1)*(i-1)*u;Tr(r.rstyle,"labelWidth",e,h),Tr(r.rscratch,"labelWidth",e,h),Tr(r.rstyle,"labelHeight",e,c),Tr(r.rscratch,"labelHeight",e,c),Tr(r.rscratch,"labelLineHeight",e,f)},hr.getLabelText=function(t,e){var r=t._private,a=e?e+"-":"",n=t.pstyle(a+"label").strValue,i=t.pstyle("text-transform").value,s=function(ge,se){return se?(Tr(r.rscratch,ge,e,se),se):tr(r.rscratch,ge,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=t.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="​",f=n.split(`
`),h=t.pstyle("text-max-width").pfValue,c=t.pstyle("text-overflow-wrap").value,d=c==="anywhere",v=[],p=/[\s\u200b]+/,y=d?"":" ",g=0;g<f.length;g++){var m=f[g],b=this.calculateLabelDimensions(t,m),E=b.width;if(d){var N=m.split("").join(u);m=N}if(E>h){for(var A=m.split(p),x="",I=0;I<A.length;I++){var C=A[I],F=x.length===0?C:x+y+C,z=this.calculateLabelDimensions(t,F),M=z.width;M<=h?x+=C+y:(x&&v.push(x),x=C+y)}x.match(/^[\s\u200b]+$/)||v.push(x)}else v.push(m)}s("labelWrapCachedLines",v),n=s("labelWrapCachedText",v.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var X=t.pstyle("text-max-width").pfValue,B="",re="…",q=!1;if(this.calculateLabelDimensions(t,n).width<X)return n;for(var Z=0;Z<n.length;Z++){var ie=this.calculateLabelDimensions(t,B+n[Z]+re).width;if(ie>X)break;B+=n[Z],Z===n.length-1&&(q=!0)}return q||(B+=re),B}return n},hr.getLabelJustification=function(t){var e=t.pstyle("text-justification").strValue,r=t.pstyle("text-halign").strValue;if(e==="auto")if(t.isNode())switch(r){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e},hr.calculateLabelDimensions=function(t,e){var r=this,a=Pr(e,t._private.labelDimsKey),n=r.labelDimCache||(r.labelDimCache=[]),i=n[a];if(i!=null)return i;var s=0,o=t.pstyle("font-style").strValue,l=t.pstyle("font-size").pfValue,u=t.pstyle("font-family").strValue,f=t.pstyle("font-weight").strValue,h=this.labelCalcCanvas,c=this.labelCalcCanvasContext;if(!h){h=this.labelCalcCanvas=document.createElement("canvas"),c=this.labelCalcCanvasContext=h.getContext("2d");var d=h.style;d.position="absolute",d.left="-9999px",d.top="-9999px",d.zIndex="-1",d.visibility="hidden",d.pointerEvents="none"}c.font="".concat(o," ").concat(f," ").concat(l,"px ").concat(u);for(var v=0,p=0,y=e.split(`
`),g=0;g<y.length;g++){var m=y[g],b=c.measureText(m),E=Math.ceil(b.width),N=l;v=Math.max(E,v),p+=N}return v+=s,p+=s,n[a]={width:v,height:p}},hr.calculateLabelAngle=function(t,e){var r=t._private,a=r.rscratch,n=t.isEdge(),i=e?e+"-":"",s=t.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue},hr.calculateLabelAngles=function(t){var e=this,r=t.isEdge(),a=t._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(t),r&&(n.sourceLabelAngle=e.calculateLabelAngle(t,"source"),n.targetLabelAngle=e.calculateLabelAngle(t,"target"))};var ou={},uu=28,lu=!1;ou.getNodeShape=function(t){var e=this,r=t.pstyle("shape").value;if(r==="cutrectangle"&&(t.width()<uu||t.height()<uu))return lu||(ft("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),lu=!0),"rectangle";if(t.isParent())return r==="rectangle"||r==="roundrectangle"||r==="round-rectangle"||r==="cutrectangle"||r==="cut-rectangle"||r==="barrel"?r:"rectangle";if(r==="polygon"){var a=t.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return r};var Wn={};Wn.registerCalculationListeners=function(){var t=this.cy,e=t.collection(),r=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],f=u._private,h=f.rstyle;h.clean=!1,h.cleanConnected=!1}};r.binder(t).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=r.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],f=u._private.rstyle;u.isNode()&&!f.cleanConnected&&(a(u.connectedEdges()),f.cleanConnected=!0)}if(o)for(var h=0;h<o.length;h++){var c=o[h];c(s,e)}r.recalculateRenderedStyle(e),e=t.collection()}};r.flushRenderedStyleQueue=function(){n(!0)},r.beforeRender(n,r.beforeRenderPriorities.eleCalcs)},Wn.onUpdateEleCalcs=function(t){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(t)},Wn.recalculateRenderedStyle=function(t,e){var r=function(E){return E._private.rstyle.cleanConnected},a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<t.length;i++){var s=t[i],o=s._private,l=o.rstyle;s.isEdge()&&(!r(s.source())||!r(s.target()))&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var f=n[u],h=f._private,c=h.rstyle,d=f.position();this.recalculateNodeLabelProjection(f),c.nodeX=d.x,c.nodeY=d.y,c.nodeW=f.pstyle("width").pfValue,c.nodeH=f.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var v=0;v<a.length;v++){var p=a[v],y=p._private,g=y.rstyle,m=y.rscratch;g.srcX=m.arrowStartX,g.srcY=m.arrowStartY,g.tgtX=m.arrowEndX,g.tgtY=m.arrowEndY,g.midX=m.midX,g.midY=m.midY,g.labelAngle=m.labelAngle,g.sourceLabelAngle=m.sourceLabelAngle,g.targetLabelAngle=m.targetLabelAngle}}};var qn={};qn.updateCachedGrabbedEles=function(){var t=this.cachedZSortedEles;if(t){t.drag=[],t.nondrag=[];for(var e=[],r=0;r<t.length;r++){var a=t[r],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?t.drag.push(a):t.nondrag.push(a)}for(var r=0;r<e.length;r++){var a=e[r];t.drag.push(a)}}},qn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null},qn.getCachedZSortedEles=function(t){if(t||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(Io),e.interactive=e.filter(function(r){return r.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var fu={};[_r,Xn,_t,Ja,zi,hr,ou,Wn,qn].forEach(function(t){Ue(fu,t)});var hu={};hu.getCachedImage=function(t,e,r){var a=this,n=a.imageCache=a.imageCache||{},i=n[t];if(i)return i.image.complete||i.image.addEventListener("load",r),i.image;i=n[t]=n[t]||{};var s=i.image=new Image;s.addEventListener("load",r),s.addEventListener("error",function(){s.error=!0});var o="data:",l=t.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=t,s};var ma={};ma.registerBinding=function(t,e,r,a){var n=Array.prototype.slice.apply(arguments,[1]),i=this.binder(t);return i.on.apply(i,n)},ma.binder=function(t){var e=this,r=e.cy.window(),a=t===r||t===r.document||t===r.document.body||$e(t);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});r.addEventListener("test",null,i)}catch{}e.supportsPassiveEvents=n}var s=function(l,u,f){var h=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(h[2]={capture:f??!1,passive:!1,once:!1}),e.bindings.push({target:t,args:h}),(t.addEventListener||t.on).apply(t,h),this};return{on:s,addEventListener:s,addListener:s,bind:s}},ma.nodeIsDraggable=function(t){return t&&t.isNode()&&!t.locked()&&t.grabbable()},ma.nodeIsGrabbable=function(t){return this.nodeIsDraggable(t)&&t.interactive()},ma.load=function(){var t=this,e=t.cy.window(),r=function(k){return k.selected()},a=function(k,he,Se,Ee){k==null&&(k=t.cy);for(var we=0;we<he.length;we++){var qe=he[we];k.emit({originalEvent:Se,type:qe,position:Ee})}},n=function(k){return k.shiftKey||k.metaKey||k.ctrlKey},i=function(k,he){var Se=!0;if(t.cy.hasCompoundNodes()&&k&&k.pannable())for(var Ee=0;he&&Ee<he.length;Ee++){var k=he[Ee];if(k.isNode()&&k.isParent()&&!k.pannable()){Se=!1;break}}else Se=!0;return Se},s=function(k){k[0]._private.grabbed=!0},o=function(k){k[0]._private.grabbed=!1},l=function(k){k[0]._private.rscratch.inDragLayer=!0},u=function(k){k[0]._private.rscratch.inDragLayer=!1},f=function(k){k[0]._private.rscratch.isGrabTarget=!0},h=function(k){k[0]._private.rscratch.isGrabTarget=!1},c=function(k,he){var Se=he.addToList,Ee=Se.has(k);!Ee&&k.grabbable()&&!k.locked()&&(Se.merge(k),s(k))},d=function(k,he){if(k.cy().hasCompoundNodes()&&!(he.inDragLayer==null&&he.addToList==null)){var Se=k.descendants();he.inDragLayer&&(Se.forEach(l),Se.connectedEdges().forEach(l)),he.addToList&&c(Se,he)}},v=function(k,he){he=he||{};var Se=k.cy().hasCompoundNodes();he.inDragLayer&&(k.forEach(l),k.neighborhood().stdFilter(function(Ee){return!Se||Ee.isEdge()}).forEach(l)),he.addToList&&k.forEach(function(Ee){c(Ee,he)}),d(k,he),g(k,{inDragLayer:he.inDragLayer}),t.updateCachedGrabbedEles()},p=v,y=function(k){k&&(t.getCachedZSortedEles().forEach(function(he){o(he),u(he),h(he)}),t.updateCachedGrabbedEles())},g=function(k,he){if(!(he.inDragLayer==null&&he.addToList==null)&&k.cy().hasCompoundNodes()){var Se=k.ancestors().orphans();if(!Se.same(k)){var Ee=Se.descendants().spawnSelf().merge(Se).unmerge(k).unmerge(k.descendants()),we=Ee.connectedEdges();he.inDragLayer&&(we.forEach(l),Ee.forEach(l)),he.addToList&&Ee.forEach(function(qe){c(qe,he)})}}},m=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},b=typeof MutationObserver<"u",E=typeof ResizeObserver<"u";b?(t.removeObserver=new MutationObserver(function(De){for(var k=0;k<De.length;k++){var he=De[k],Se=he.removedNodes;if(Se)for(var Ee=0;Ee<Se.length;Ee++){var we=Se[Ee];if(we===t.container){t.destroy();break}}}}),t.container.parentNode&&t.removeObserver.observe(t.container.parentNode,{childList:!0})):t.registerBinding(t.container,"DOMNodeRemoved",function(De){t.destroy()});var N=fn(function(){t.cy.resize()},100);b&&(t.styleObserver=new MutationObserver(N),t.styleObserver.observe(t.container,{attributes:!0})),t.registerBinding(e,"resize",N),E&&(t.resizeObserver=new ResizeObserver(N),t.resizeObserver.observe(t.container));var A=function(k,he){for(;k!=null;)he(k),k=k.parentNode},x=function(){t.invalidateContainerClientCoordsCache()};A(t.container,function(De){t.registerBinding(De,"transitionend",x),t.registerBinding(De,"animationend",x),t.registerBinding(De,"scroll",x)}),t.registerBinding(t.container,"contextmenu",function(De){De.preventDefault()});var I=function(){return t.selection[4]!==0},C=function(k){for(var he=t.findContainerClientCoords(),Se=he[0],Ee=he[1],we=he[2],qe=he[3],Le=k.touches?k.touches:[k],Ve=!1,ot=0;ot<Le.length;ot++){var gt=Le[ot];if(Se<=gt.clientX&&gt.clientX<=Se+we&&Ee<=gt.clientY&&gt.clientY<=Ee+qe){Ve=!0;break}}if(!Ve)return!1;for(var Qe=t.container,ut=k.target,Ke=ut.parentNode,Je=!1;Ke;){if(Ke===Qe){Je=!0;break}Ke=Ke.parentNode}return!!Je};t.registerBinding(t.container,"mousedown",function(k){if(C(k)){k.preventDefault(),m(),t.hoverData.capture=!0,t.hoverData.which=k.which;var he=t.cy,Se=[k.clientX,k.clientY],Ee=t.projectIntoViewport(Se[0],Se[1]),we=t.selection,qe=t.findNearestElements(Ee[0],Ee[1],!0,!1),Le=qe[0],Ve=t.dragData.possibleDragElements;t.hoverData.mdownPos=Ee,t.hoverData.mdownGPos=Se;var ot=function(){t.hoverData.tapholdCancelled=!1,clearTimeout(t.hoverData.tapholdTimeout),t.hoverData.tapholdTimeout=setTimeout(function(){if(!t.hoverData.tapholdCancelled){var Dt=t.hoverData.down;Dt?Dt.emit({originalEvent:k,type:"taphold",position:{x:Ee[0],y:Ee[1]}}):he.emit({originalEvent:k,type:"taphold",position:{x:Ee[0],y:Ee[1]}})}},t.tapholdDuration)};if(k.which==3){t.hoverData.cxtStarted=!0;var gt={originalEvent:k,type:"cxttapstart",position:{x:Ee[0],y:Ee[1]}};Le?(Le.activate(),Le.emit(gt),t.hoverData.down=Le):he.emit(gt),t.hoverData.downTime=new Date().getTime(),t.hoverData.cxtDragged=!1}else if(k.which==1){Le&&Le.activate();{if(Le!=null&&t.nodeIsGrabbable(Le)){var Qe=function(Dt){return{originalEvent:k,type:Dt,position:{x:Ee[0],y:Ee[1]}}},ut=function(Dt){Dt.emit(Qe("grab"))};if(f(Le),!Le.selected())Ve=t.dragData.possibleDragElements=he.collection(),p(Le,{addToList:Ve}),Le.emit(Qe("grabon")).emit(Qe("grab"));else{Ve=t.dragData.possibleDragElements=he.collection();var Ke=he.$(function(Je){return Je.isNode()&&Je.selected()&&t.nodeIsGrabbable(Je)});v(Ke,{addToList:Ve}),Le.emit(Qe("grabon")),Ke.forEach(ut)}t.redrawHint("eles",!0),t.redrawHint("drag",!0)}t.hoverData.down=Le,t.hoverData.downs=qe,t.hoverData.downTime=new Date().getTime()}a(Le,["mousedown","tapstart","vmousedown"],k,{x:Ee[0],y:Ee[1]}),Le==null?(we[4]=1,t.data.bgActivePosistion={x:Ee[0],y:Ee[1]},t.redrawHint("select",!0),t.redraw()):Le.pannable()&&(we[4]=1),ot()}we[0]=we[2]=Ee[0],we[1]=we[3]=Ee[1]}},!1),t.registerBinding(e,"mousemove",function(k){var he=t.hoverData.capture;if(!(!he&&!C(k))){var Se=!1,Ee=t.cy,we=Ee.zoom(),qe=[k.clientX,k.clientY],Le=t.projectIntoViewport(qe[0],qe[1]),Ve=t.hoverData.mdownPos,ot=t.hoverData.mdownGPos,gt=t.selection,Qe=null;!t.hoverData.draggingEles&&!t.hoverData.dragging&&!t.hoverData.selecting&&(Qe=t.findNearestElement(Le[0],Le[1],!0,!1));var ut=t.hoverData.last,Ke=t.hoverData.down,Je=[Le[0]-gt[2],Le[1]-gt[3]],Dt=t.dragData.possibleDragElements,Rt;if(ot){var nr=qe[0]-ot[0],ir=nr*nr,kt=qe[1]-ot[1],Jt=kt*kt,Ut=ir+Jt;t.hoverData.isOverThresholdDrag=Rt=Ut>=t.desktopTapThreshold2}var Er=n(k);Rt&&(t.hoverData.tapholdCancelled=!0);var Rr=function(){var vr=t.hoverData.dragDelta=t.hoverData.dragDelta||[];vr.length===0?(vr.push(Je[0]),vr.push(Je[1])):(vr[0]+=Je[0],vr[1]+=Je[1])};Se=!0,a(Qe,["mousemove","vmousemove","tapdrag"],k,{x:Le[0],y:Le[1]});var Ta=function(){t.data.bgActivePosistion=void 0,t.hoverData.selecting||Ee.emit({originalEvent:k,type:"boxstart",position:{x:Le[0],y:Le[1]}}),gt[4]=1,t.hoverData.selecting=!0,t.redrawHint("select",!0),t.redraw()};if(t.hoverData.which===3){if(Rt){var Xr={originalEvent:k,type:"cxtdrag",position:{x:Le[0],y:Le[1]}};Ke?Ke.emit(Xr):Ee.emit(Xr),t.hoverData.cxtDragged=!0,(!t.hoverData.cxtOver||Qe!==t.hoverData.cxtOver)&&(t.hoverData.cxtOver&&t.hoverData.cxtOver.emit({originalEvent:k,type:"cxtdragout",position:{x:Le[0],y:Le[1]}}),t.hoverData.cxtOver=Qe,Qe&&Qe.emit({originalEvent:k,type:"cxtdragover",position:{x:Le[0],y:Le[1]}}))}}else if(t.hoverData.dragging){if(Se=!0,Ee.panningEnabled()&&Ee.userPanningEnabled()){var Ca;if(t.hoverData.justStartedPan){var Jn=t.hoverData.mdownPos;Ca={x:(Le[0]-Jn[0])*we,y:(Le[1]-Jn[1])*we},t.hoverData.justStartedPan=!1}else Ca={x:Je[0]*we,y:Je[1]*we};Ee.panBy(Ca),Ee.emit("dragpan"),t.hoverData.dragged=!0}Le=t.projectIntoViewport(k.clientX,k.clientY)}else if(gt[4]==1&&(Ke==null||Ke.pannable())){if(Rt){if(!t.hoverData.dragging&&Ee.boxSelectionEnabled()&&(Er||!Ee.panningEnabled()||!Ee.userPanningEnabled()))Ta();else if(!t.hoverData.selecting&&Ee.panningEnabled()&&Ee.userPanningEnabled()){var Wr=i(Ke,t.hoverData.downs);Wr&&(t.hoverData.dragging=!0,t.hoverData.justStartedPan=!0,gt[4]=0,t.data.bgActivePosistion=ea(Ve),t.redrawHint("select",!0),t.redraw())}Ke&&Ke.pannable()&&Ke.active()&&Ke.unactivate()}}else{if(Ke&&Ke.pannable()&&Ke.active()&&Ke.unactivate(),(!Ke||!Ke.grabbed())&&Qe!=ut&&(ut&&a(ut,["mouseout","tapdragout"],k,{x:Le[0],y:Le[1]}),Qe&&a(Qe,["mouseover","tapdragover"],k,{x:Le[0],y:Le[1]}),t.hoverData.last=Qe),Ke)if(Rt){if(Ee.boxSelectionEnabled()&&Er)Ke&&Ke.grabbed()&&(y(Dt),Ke.emit("freeon"),Dt.emit("free"),t.dragData.didDrag&&(Ke.emit("dragfreeon"),Dt.emit("dragfree"))),Ta();else if(Ke&&Ke.grabbed()&&t.nodeIsDraggable(Ke)){var Xt=!t.dragData.didDrag;Xt&&t.redrawHint("eles",!0),t.dragData.didDrag=!0,t.hoverData.draggingEles||v(Dt,{inDragLayer:!0});var $t={x:0,y:0};if(R(Je[0])&&R(Je[1])&&($t.x+=Je[0],$t.y+=Je[1],Xt)){var Wt=t.hoverData.dragDelta;Wt&&R(Wt[0])&&R(Wt[1])&&($t.x+=Wt[0],$t.y+=Wt[1])}t.hoverData.draggingEles=!0,Dt.silentShift($t).emit("position drag"),t.redrawHint("drag",!0),t.redraw()}}else Rr();Se=!0}if(gt[2]=Le[0],gt[3]=Le[1],Se)return k.stopPropagation&&k.stopPropagation(),k.preventDefault&&k.preventDefault(),!1}},!1);var F,z,M;t.registerBinding(e,"mouseup",function(k){var he=t.hoverData.capture;if(he){t.hoverData.capture=!1;var Se=t.cy,Ee=t.projectIntoViewport(k.clientX,k.clientY),we=t.selection,qe=t.findNearestElement(Ee[0],Ee[1],!0,!1),Le=t.dragData.possibleDragElements,Ve=t.hoverData.down,ot=n(k);if(t.data.bgActivePosistion&&(t.redrawHint("select",!0),t.redraw()),t.hoverData.tapholdCancelled=!0,t.data.bgActivePosistion=void 0,Ve&&Ve.unactivate(),t.hoverData.which===3){var gt={originalEvent:k,type:"cxttapend",position:{x:Ee[0],y:Ee[1]}};if(Ve?Ve.emit(gt):Se.emit(gt),!t.hoverData.cxtDragged){var Qe={originalEvent:k,type:"cxttap",position:{x:Ee[0],y:Ee[1]}};Ve?Ve.emit(Qe):Se.emit(Qe)}t.hoverData.cxtDragged=!1,t.hoverData.which=null}else if(t.hoverData.which===1){if(a(qe,["mouseup","tapend","vmouseup"],k,{x:Ee[0],y:Ee[1]}),!t.dragData.didDrag&&!t.hoverData.dragged&&!t.hoverData.selecting&&!t.hoverData.isOverThresholdDrag&&(a(Ve,["click","tap","vclick"],k,{x:Ee[0],y:Ee[1]}),z=!1,k.timeStamp-M<=Se.multiClickDebounceTime()?(F&&clearTimeout(F),z=!0,M=null,a(Ve,["dblclick","dbltap","vdblclick"],k,{x:Ee[0],y:Ee[1]})):(F=setTimeout(function(){z||a(Ve,["oneclick","onetap","voneclick"],k,{x:Ee[0],y:Ee[1]})},Se.multiClickDebounceTime()),M=k.timeStamp)),Ve==null&&!t.dragData.didDrag&&!t.hoverData.selecting&&!t.hoverData.dragged&&!n(k)&&(Se.$(r).unselect(["tapunselect"]),Le.length>0&&t.redrawHint("eles",!0),t.dragData.possibleDragElements=Le=Se.collection()),qe==Ve&&!t.dragData.didDrag&&!t.hoverData.selecting&&qe!=null&&qe._private.selectable&&(t.hoverData.dragging||(Se.selectionType()==="additive"||ot?qe.selected()?qe.unselect(["tapunselect"]):qe.select(["tapselect"]):ot||(Se.$(r).unmerge(qe).unselect(["tapunselect"]),qe.select(["tapselect"]))),t.redrawHint("eles",!0)),t.hoverData.selecting){var ut=Se.collection(t.getAllInBox(we[0],we[1],we[2],we[3]));t.redrawHint("select",!0),ut.length>0&&t.redrawHint("eles",!0),Se.emit({type:"boxend",originalEvent:k,position:{x:Ee[0],y:Ee[1]}});var Ke=function(Rt){return Rt.selectable()&&!Rt.selected()};Se.selectionType()==="additive"||ot||Se.$(r).unmerge(ut).unselect(),ut.emit("box").stdFilter(Ke).select().emit("boxselect"),t.redraw()}if(t.hoverData.dragging&&(t.hoverData.dragging=!1,t.redrawHint("select",!0),t.redrawHint("eles",!0),t.redraw()),!we[4]){t.redrawHint("drag",!0),t.redrawHint("eles",!0);var Je=Ve&&Ve.grabbed();y(Le),Je&&(Ve.emit("freeon"),Le.emit("free"),t.dragData.didDrag&&(Ve.emit("dragfreeon"),Le.emit("dragfree")))}}we[4]=0,t.hoverData.down=null,t.hoverData.cxtStarted=!1,t.hoverData.draggingEles=!1,t.hoverData.selecting=!1,t.hoverData.isOverThresholdDrag=!1,t.dragData.didDrag=!1,t.hoverData.dragged=!1,t.hoverData.dragDelta=[],t.hoverData.mdownPos=null,t.hoverData.mdownGPos=null}},!1);var X=function(k){if(!t.scrollingPage){var he=t.cy,Se=he.zoom(),Ee=he.pan(),we=t.projectIntoViewport(k.clientX,k.clientY),qe=[we[0]*Se+Ee.x,we[1]*Se+Ee.y];if(t.hoverData.draggingEles||t.hoverData.dragging||t.hoverData.cxtStarted||I()){k.preventDefault();return}if(he.panningEnabled()&&he.userPanningEnabled()&&he.zoomingEnabled()&&he.userZoomingEnabled()){k.preventDefault(),t.data.wheelZooming=!0,clearTimeout(t.data.wheelTimeout),t.data.wheelTimeout=setTimeout(function(){t.data.wheelZooming=!1,t.redrawHint("eles",!0),t.redraw()},150);var Le;k.deltaY!=null?Le=k.deltaY/-250:k.wheelDeltaY!=null?Le=k.wheelDeltaY/1e3:Le=k.wheelDelta/1e3,Le=Le*t.wheelSensitivity;var Ve=k.deltaMode===1;Ve&&(Le*=33);var ot=he.zoom()*Math.pow(10,Le);k.type==="gesturechange"&&(ot=t.gestureStartZoom*k.scale),he.zoom({level:ot,renderedPosition:{x:qe[0],y:qe[1]}}),he.emit(k.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};t.registerBinding(t.container,"wheel",X,!0),t.registerBinding(e,"scroll",function(k){t.scrollingPage=!0,clearTimeout(t.scrollingPageTimeout),t.scrollingPageTimeout=setTimeout(function(){t.scrollingPage=!1},250)},!0),t.registerBinding(t.container,"gesturestart",function(k){t.gestureStartZoom=t.cy.zoom(),t.hasTouchStarted||k.preventDefault()},!0),t.registerBinding(t.container,"gesturechange",function(De){t.hasTouchStarted||X(De)},!0),t.registerBinding(t.container,"mouseout",function(k){var he=t.projectIntoViewport(k.clientX,k.clientY);t.cy.emit({originalEvent:k,type:"mouseout",position:{x:he[0],y:he[1]}})},!1),t.registerBinding(t.container,"mouseover",function(k){var he=t.projectIntoViewport(k.clientX,k.clientY);t.cy.emit({originalEvent:k,type:"mouseover",position:{x:he[0],y:he[1]}})},!1);var B,re,q,Z,ie,ue,ge,se,ve,ye,Te,be,me,ae=function(k,he,Se,Ee){return Math.sqrt((Se-k)*(Se-k)+(Ee-he)*(Ee-he))},xe=function(k,he,Se,Ee){return(Se-k)*(Se-k)+(Ee-he)*(Ee-he)},Ce;t.registerBinding(t.container,"touchstart",Ce=function(k){if(t.hasTouchStarted=!0,!!C(k)){m(),t.touchData.capture=!0,t.data.bgActivePosistion=void 0;var he=t.cy,Se=t.touchData.now,Ee=t.touchData.earlier;if(k.touches[0]){var we=t.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);Se[0]=we[0],Se[1]=we[1]}if(k.touches[1]){var we=t.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);Se[2]=we[0],Se[3]=we[1]}if(k.touches[2]){var we=t.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);Se[4]=we[0],Se[5]=we[1]}if(k.touches[1]){t.touchData.singleTouchMoved=!0,y(t.dragData.touchDragEles);var qe=t.findContainerClientCoords();ve=qe[0],ye=qe[1],Te=qe[2],be=qe[3],B=k.touches[0].clientX-ve,re=k.touches[0].clientY-ye,q=k.touches[1].clientX-ve,Z=k.touches[1].clientY-ye,me=0<=B&&B<=Te&&0<=q&&q<=Te&&0<=re&&re<=be&&0<=Z&&Z<=be;var Le=he.pan(),Ve=he.zoom();ie=ae(B,re,q,Z),ue=xe(B,re,q,Z),ge=[(B+q)/2,(re+Z)/2],se=[(ge[0]-Le.x)/Ve,(ge[1]-Le.y)/Ve];var ot=200,gt=ot*ot;if(ue<gt&&!k.touches[2]){var Qe=t.findNearestElement(Se[0],Se[1],!0,!0),ut=t.findNearestElement(Se[2],Se[3],!0,!0);Qe&&Qe.isNode()?(Qe.activate().emit({originalEvent:k,type:"cxttapstart",position:{x:Se[0],y:Se[1]}}),t.touchData.start=Qe):ut&&ut.isNode()?(ut.activate().emit({originalEvent:k,type:"cxttapstart",position:{x:Se[0],y:Se[1]}}),t.touchData.start=ut):he.emit({originalEvent:k,type:"cxttapstart",position:{x:Se[0],y:Se[1]}}),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!0,t.touchData.cxtDragged=!1,t.data.bgActivePosistion=void 0,t.redraw();return}}if(k.touches[2])he.boxSelectionEnabled()&&k.preventDefault();else if(!k.touches[1]){if(k.touches[0]){var Ke=t.findNearestElements(Se[0],Se[1],!0,!0),Je=Ke[0];if(Je!=null&&(Je.activate(),t.touchData.start=Je,t.touchData.starts=Ke,t.nodeIsGrabbable(Je))){var Dt=t.dragData.touchDragEles=he.collection(),Rt=null;t.redrawHint("eles",!0),t.redrawHint("drag",!0),Je.selected()?(Rt=he.$(function(Ut){return Ut.selected()&&t.nodeIsGrabbable(Ut)}),v(Rt,{addToList:Dt})):p(Je,{addToList:Dt}),f(Je);var nr=function(Er){return{originalEvent:k,type:Er,position:{x:Se[0],y:Se[1]}}};Je.emit(nr("grabon")),Rt?Rt.forEach(function(Ut){Ut.emit(nr("grab"))}):Je.emit(nr("grab"))}a(Je,["touchstart","tapstart","vmousedown"],k,{x:Se[0],y:Se[1]}),Je==null&&(t.data.bgActivePosistion={x:we[0],y:we[1]},t.redrawHint("select",!0),t.redraw()),t.touchData.singleTouchMoved=!1,t.touchData.singleTouchStartTime=+new Date,clearTimeout(t.touchData.tapholdTimeout),t.touchData.tapholdTimeout=setTimeout(function(){t.touchData.singleTouchMoved===!1&&!t.pinching&&!t.touchData.selecting&&a(t.touchData.start,["taphold"],k,{x:Se[0],y:Se[1]})},t.tapholdDuration)}}if(k.touches.length>=1){for(var ir=t.touchData.startPosition=[null,null,null,null,null,null],kt=0;kt<Se.length;kt++)ir[kt]=Ee[kt]=Se[kt];var Jt=k.touches[0];t.touchData.startGPosition=[Jt.clientX,Jt.clientY]}}},!1);var Oe;t.registerBinding(window,"touchmove",Oe=function(k){var he=t.touchData.capture;if(!(!he&&!C(k))){var Se=t.selection,Ee=t.cy,we=t.touchData.now,qe=t.touchData.earlier,Le=Ee.zoom();if(k.touches[0]){var Ve=t.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);we[0]=Ve[0],we[1]=Ve[1]}if(k.touches[1]){var Ve=t.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);we[2]=Ve[0],we[3]=Ve[1]}if(k.touches[2]){var Ve=t.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);we[4]=Ve[0],we[5]=Ve[1]}var ot=t.touchData.startGPosition,gt;if(he&&k.touches[0]&&ot){for(var Qe=[],ut=0;ut<we.length;ut++)Qe[ut]=we[ut]-qe[ut];var Ke=k.touches[0].clientX-ot[0],Je=Ke*Ke,Dt=k.touches[0].clientY-ot[1],Rt=Dt*Dt,nr=Je+Rt;gt=nr>=t.touchTapThreshold2}if(he&&t.touchData.cxt){k.preventDefault();var ir=k.touches[0].clientX-ve,kt=k.touches[0].clientY-ye,Jt=k.touches[1].clientX-ve,Ut=k.touches[1].clientY-ye,Er=xe(ir,kt,Jt,Ut),Rr=Er/ue,Ta=150,Xr=Ta*Ta,Ca=1.5,Jn=Ca*Ca;if(Rr>=Jn||Er>=Xr){t.touchData.cxt=!1,t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Wr={originalEvent:k,type:"cxttapend",position:{x:we[0],y:we[1]}};t.touchData.start?(t.touchData.start.unactivate().emit(Wr),t.touchData.start=null):Ee.emit(Wr)}}if(he&&t.touchData.cxt){var Wr={originalEvent:k,type:"cxtdrag",position:{x:we[0],y:we[1]}};t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.touchData.start?t.touchData.start.emit(Wr):Ee.emit(Wr),t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxtDragged=!0;var Xt=t.findNearestElement(we[0],we[1],!0,!0);(!t.touchData.cxtOver||Xt!==t.touchData.cxtOver)&&(t.touchData.cxtOver&&t.touchData.cxtOver.emit({originalEvent:k,type:"cxtdragout",position:{x:we[0],y:we[1]}}),t.touchData.cxtOver=Xt,Xt&&Xt.emit({originalEvent:k,type:"cxtdragover",position:{x:we[0],y:we[1]}}))}else if(he&&k.touches[2]&&Ee.boxSelectionEnabled())k.preventDefault(),t.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,t.touchData.selecting||Ee.emit({originalEvent:k,type:"boxstart",position:{x:we[0],y:we[1]}}),t.touchData.selecting=!0,t.touchData.didSelect=!0,Se[4]=1,!Se||Se.length===0||Se[0]===void 0?(Se[0]=(we[0]+we[2]+we[4])/3,Se[1]=(we[1]+we[3]+we[5])/3,Se[2]=(we[0]+we[2]+we[4])/3+1,Se[3]=(we[1]+we[3]+we[5])/3+1):(Se[2]=(we[0]+we[2]+we[4])/3,Se[3]=(we[1]+we[3]+we[5])/3),t.redrawHint("select",!0),t.redraw();else if(he&&k.touches[1]&&!t.touchData.didSelect&&Ee.zoomingEnabled()&&Ee.panningEnabled()&&Ee.userZoomingEnabled()&&Ee.userPanningEnabled()){k.preventDefault(),t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var $t=t.dragData.touchDragEles;if($t){t.redrawHint("drag",!0);for(var Wt=0;Wt<$t.length;Wt++){var jn=$t[Wt]._private;jn.grabbed=!1,jn.rscratch.inDragLayer=!1}}var vr=t.touchData.start,ir=k.touches[0].clientX-ve,kt=k.touches[0].clientY-ye,Jt=k.touches[1].clientX-ve,Ut=k.touches[1].clientY-ye,Bu=ae(ir,kt,Jt,Ut),hp=Bu/ie;if(me){var cp=ir-B,vp=kt-re,dp=Jt-q,gp=Ut-Z,pp=(cp+dp)/2,yp=(vp+gp)/2,an=Ee.zoom(),Xi=an*hp,ei=Ee.pan(),Fu=se[0]*an+ei.x,zu=se[1]*an+ei.y,mp={x:-Xi/an*(Fu-ei.x-pp)+Fu,y:-Xi/an*(zu-ei.y-yp)+zu};if(vr&&vr.active()){var $t=t.dragData.touchDragEles;y($t),t.redrawHint("drag",!0),t.redrawHint("eles",!0),vr.unactivate().emit("freeon"),$t.emit("free"),t.dragData.didDrag&&(vr.emit("dragfreeon"),$t.emit("dragfree"))}Ee.viewport({zoom:Xi,pan:mp,cancelOnFailedZoom:!0}),Ee.emit("pinchzoom"),ie=Bu,B=ir,re=kt,q=Jt,Z=Ut,t.pinching=!0}if(k.touches[0]){var Ve=t.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);we[0]=Ve[0],we[1]=Ve[1]}if(k.touches[1]){var Ve=t.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);we[2]=Ve[0],we[3]=Ve[1]}if(k.touches[2]){var Ve=t.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);we[4]=Ve[0],we[5]=Ve[1]}}else if(k.touches[0]&&!t.touchData.didSelect){var sr=t.touchData.start,Wi=t.touchData.last,Xt;if(!t.hoverData.draggingEles&&!t.swipePanning&&(Xt=t.findNearestElement(we[0],we[1],!0,!0)),he&&sr!=null&&k.preventDefault(),he&&sr!=null&&t.nodeIsDraggable(sr))if(gt){var $t=t.dragData.touchDragEles,Gu=!t.dragData.didDrag;Gu&&v($t,{inDragLayer:!0}),t.dragData.didDrag=!0;var nn={x:0,y:0};if(R(Qe[0])&&R(Qe[1])&&(nn.x+=Qe[0],nn.y+=Qe[1],Gu)){t.redrawHint("eles",!0);var or=t.touchData.dragDelta;or&&R(or[0])&&R(or[1])&&(nn.x+=or[0],nn.y+=or[1])}t.hoverData.draggingEles=!0,$t.silentShift(nn).emit("position drag"),t.redrawHint("drag",!0),t.touchData.startPosition[0]==qe[0]&&t.touchData.startPosition[1]==qe[1]&&t.redrawHint("eles",!0),t.redraw()}else{var or=t.touchData.dragDelta=t.touchData.dragDelta||[];or.length===0?(or.push(Qe[0]),or.push(Qe[1])):(or[0]+=Qe[0],or[1]+=Qe[1])}if(a(sr||Xt,["touchmove","tapdrag","vmousemove"],k,{x:we[0],y:we[1]}),(!sr||!sr.grabbed())&&Xt!=Wi&&(Wi&&Wi.emit({originalEvent:k,type:"tapdragout",position:{x:we[0],y:we[1]}}),Xt&&Xt.emit({originalEvent:k,type:"tapdragover",position:{x:we[0],y:we[1]}})),t.touchData.last=Xt,he)for(var Wt=0;Wt<we.length;Wt++)we[Wt]&&t.touchData.startPosition[Wt]&&gt&&(t.touchData.singleTouchMoved=!0);if(he&&(sr==null||sr.pannable())&&Ee.panningEnabled()&&Ee.userPanningEnabled()){var bp=i(sr,t.touchData.starts);bp&&(k.preventDefault(),t.data.bgActivePosistion||(t.data.bgActivePosistion=ea(t.touchData.startPosition)),t.swipePanning?(Ee.panBy({x:Qe[0]*Le,y:Qe[1]*Le}),Ee.emit("dragpan")):gt&&(t.swipePanning=!0,Ee.panBy({x:Ke*Le,y:Dt*Le}),Ee.emit("dragpan"),sr&&(sr.unactivate(),t.redrawHint("select",!0),t.touchData.start=null)));var Ve=t.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);we[0]=Ve[0],we[1]=Ve[1]}}for(var ut=0;ut<we.length;ut++)qe[ut]=we[ut];he&&k.touches.length>0&&!t.hoverData.draggingEles&&!t.swipePanning&&t.data.bgActivePosistion!=null&&(t.data.bgActivePosistion=void 0,t.redrawHint("select",!0),t.redraw())}},!1);var Me;t.registerBinding(e,"touchcancel",Me=function(k){var he=t.touchData.start;t.touchData.capture=!1,he&&he.unactivate()});var He,We,Re,Ie;if(t.registerBinding(e,"touchend",He=function(k){var he=t.touchData.start,Se=t.touchData.capture;if(Se)k.touches.length===0&&(t.touchData.capture=!1),k.preventDefault();else return;var Ee=t.selection;t.swipePanning=!1,t.hoverData.draggingEles=!1;var we=t.cy,qe=we.zoom(),Le=t.touchData.now,Ve=t.touchData.earlier;if(k.touches[0]){var ot=t.projectIntoViewport(k.touches[0].clientX,k.touches[0].clientY);Le[0]=ot[0],Le[1]=ot[1]}if(k.touches[1]){var ot=t.projectIntoViewport(k.touches[1].clientX,k.touches[1].clientY);Le[2]=ot[0],Le[3]=ot[1]}if(k.touches[2]){var ot=t.projectIntoViewport(k.touches[2].clientX,k.touches[2].clientY);Le[4]=ot[0],Le[5]=ot[1]}he&&he.unactivate();var gt;if(t.touchData.cxt){if(gt={originalEvent:k,type:"cxttapend",position:{x:Le[0],y:Le[1]}},he?he.emit(gt):we.emit(gt),!t.touchData.cxtDragged){var Qe={originalEvent:k,type:"cxttap",position:{x:Le[0],y:Le[1]}};he?he.emit(Qe):we.emit(Qe)}t.touchData.start&&(t.touchData.start._private.grabbed=!1),t.touchData.cxt=!1,t.touchData.start=null,t.redraw();return}if(!k.touches[2]&&we.boxSelectionEnabled()&&t.touchData.selecting){t.touchData.selecting=!1;var ut=we.collection(t.getAllInBox(Ee[0],Ee[1],Ee[2],Ee[3]));Ee[0]=void 0,Ee[1]=void 0,Ee[2]=void 0,Ee[3]=void 0,Ee[4]=0,t.redrawHint("select",!0),we.emit({type:"boxend",originalEvent:k,position:{x:Le[0],y:Le[1]}});var Ke=function(Xr){return Xr.selectable()&&!Xr.selected()};ut.emit("box").stdFilter(Ke).select().emit("boxselect"),ut.nonempty()&&t.redrawHint("eles",!0),t.redraw()}if(he!=null&&he.unactivate(),k.touches[2])t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);else if(!k.touches[1]){if(!k.touches[0]){if(!k.touches[0]){t.data.bgActivePosistion=void 0,t.redrawHint("select",!0);var Je=t.dragData.touchDragEles;if(he!=null){var Dt=he._private.grabbed;y(Je),t.redrawHint("drag",!0),t.redrawHint("eles",!0),Dt&&(he.emit("freeon"),Je.emit("free"),t.dragData.didDrag&&(he.emit("dragfreeon"),Je.emit("dragfree"))),a(he,["touchend","tapend","vmouseup","tapdragout"],k,{x:Le[0],y:Le[1]}),he.unactivate(),t.touchData.start=null}else{var Rt=t.findNearestElement(Le[0],Le[1],!0,!0);a(Rt,["touchend","tapend","vmouseup","tapdragout"],k,{x:Le[0],y:Le[1]})}var nr=t.touchData.startPosition[0]-Le[0],ir=nr*nr,kt=t.touchData.startPosition[1]-Le[1],Jt=kt*kt,Ut=ir+Jt,Er=Ut*qe*qe;t.touchData.singleTouchMoved||(he||we.$(":selected").unselect(["tapunselect"]),a(he,["tap","vclick"],k,{x:Le[0],y:Le[1]}),We=!1,k.timeStamp-Ie<=we.multiClickDebounceTime()?(Re&&clearTimeout(Re),We=!0,Ie=null,a(he,["dbltap","vdblclick"],k,{x:Le[0],y:Le[1]})):(Re=setTimeout(function(){We||a(he,["onetap","voneclick"],k,{x:Le[0],y:Le[1]})},we.multiClickDebounceTime()),Ie=k.timeStamp)),he!=null&&!t.dragData.didDrag&&he._private.selectable&&Er<t.touchTapThreshold2&&!t.pinching&&(we.selectionType()==="single"?(we.$(r).unmerge(he).unselect(["tapunselect"]),he.select(["tapselect"])):he.selected()?he.unselect(["tapunselect"]):he.select(["tapselect"]),t.redrawHint("eles",!0)),t.touchData.singleTouchMoved=!0}}}for(var Rr=0;Rr<Le.length;Rr++)Ve[Rr]=Le[Rr];t.dragData.didDrag=!1,k.touches.length===0&&(t.touchData.dragDelta=[],t.touchData.startPosition=[null,null,null,null,null,null],t.touchData.startGPosition=null,t.touchData.didSelect=!1),k.touches.length<2&&(k.touches.length===1&&(t.touchData.startGPosition=[k.touches[0].clientX,k.touches[0].clientY]),t.pinching=!1,t.redrawHint("eles",!0),t.redraw())},!1),typeof TouchEvent>"u"){var Ge=[],Fe=function(k){return{clientX:k.clientX,clientY:k.clientY,force:1,identifier:k.pointerId,pageX:k.pageX,pageY:k.pageY,radiusX:k.width/2,radiusY:k.height/2,screenX:k.screenX,screenY:k.screenY,target:k.target}},ke=function(k){return{event:k,touch:Fe(k)}},ze=function(k){Ge.push(ke(k))},je=function(k){for(var he=0;he<Ge.length;he++){var Se=Ge[he];if(Se.event.pointerId===k.pointerId){Ge.splice(he,1);return}}},Ze=function(k){var he=Ge.filter(function(Se){return Se.event.pointerId===k.pointerId})[0];he.event=k,he.touch=Fe(k)},Ye=function(k){k.touches=Ge.map(function(he){return he.touch})},ct=function(k){return k.pointerType==="mouse"||k.pointerType===4};t.registerBinding(t.container,"pointerdown",function(De){ct(De)||(De.preventDefault(),ze(De),Ye(De),Ce(De))}),t.registerBinding(t.container,"pointerup",function(De){ct(De)||(je(De),Ye(De),He(De))}),t.registerBinding(t.container,"pointercancel",function(De){ct(De)||(je(De),Ye(De),Me(De))}),t.registerBinding(t.container,"pointermove",function(De){ct(De)||(De.preventDefault(),Ze(De),Ye(De),Oe(De))})}};var mr={};mr.generatePolygon=function(t,e){return this.nodeShapes[t]={renderer:this,name:t,points:e,draw:function(a,n,i,s,o){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u){return Fa(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u){return pr(a,n,this.points,l,u,s,o,[0,-1],i)}}},mr.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,r,a,n,i){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o){return Ff(i,s,e,r,a/2+o,n/2+o)},checkPoint:function(e,r,a,n,i,s,o){return zr(e,r,n,i,s,o,a)}}},mr.generateRoundPolygon=function(t,e){for(var r=new Array(e.length*2),a=0;a<e.length/2;a++){var n=a*2,i=void 0;a<e.length/2-1?i=(a+1)*2:i=0,r[a*4]=e[n],r[a*4+1]=e[n+1];var s=e[i]-e[n],o=e[i+1]-e[n+1],l=Math.sqrt(s*s+o*o);r[a*4+2]=s/l,r[a*4+3]=o/l}return this.nodeShapes[t]={renderer:this,name:t,points:r,draw:function(f,h,c,d,v){this.renderer.nodeShapeImpl("round-polygon",f,h,c,d,v,this.points)},intersectLine:function(f,h,c,d,v,p,y){return zf(v,p,this.points,f,h,c,d)},checkPoint:function(f,h,c,d,v,p,y){return Bf(f,h,this.points,p,y,d,v)}}},mr.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:Vt(4,0),draw:function(e,r,a,n,i){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o){return xs(i,s,e,r,a,n,o)},checkPoint:function(e,r,a,n,i,s,o){var l=za(n,i),u=l*2;return!!(pr(e,r,this.points,s,o,n,i-u,[0,-1],a)||pr(e,r,this.points,s,o,n-u,i,[0,-1],a)||zr(e,r,u,u,s-n/2+l,o-i/2+l,a)||zr(e,r,u,u,s+n/2-l,o-i/2+l,a)||zr(e,r,u,u,s+n/2-l,o+i/2-l,a)||zr(e,r,u,u,s-n/2+l,o+i/2-l,a))}}},mr.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:Ss(),points:Vt(4,0),draw:function(e,r,a,n,i){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},generateCutTrianglePts:function(e,r,a,n){var i=this.cornerLength,s=r/2,o=e/2,l=a-o,u=a+o,f=n-s,h=n+s;return{topLeft:[l,f+i,l+i,f,l+i,f+i],topRight:[u-i,f,u,f+i,u-i,f+i],bottomRight:[u,h-i,u-i,h,u-i,h-i],bottomLeft:[l+i,h,l,h-i,l+i,h-i]}},intersectLine:function(e,r,a,n,i,s,o){var l=this.generateCutTrianglePts(a+2*o,n+2*o,e,r),u=[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]);return Fa(i,s,u,e,r)},checkPoint:function(e,r,a,n,i,s,o){if(pr(e,r,this.points,s,o,n,i-2*this.cornerLength,[0,-1],a)||pr(e,r,this.points,s,o,n-2*this.cornerLength,i,[0,-1],a))return!0;var l=this.generateCutTrianglePts(n,i,s,o);return Ht(e,r,l.topLeft)||Ht(e,r,l.topRight)||Ht(e,r,l.bottomRight)||Ht(e,r,l.bottomLeft)}}},mr.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:Vt(4,0),draw:function(e,r,a,n,i){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o){var l=.15,u=.5,f=.85,h=this.generateBarrelBezierPts(a+2*o,n+2*o,e,r),c=function(p){var y=ta({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},l),g=ta({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},u),m=ta({x:p[0],y:p[1]},{x:p[2],y:p[3]},{x:p[4],y:p[5]},f);return[p[0],p[1],y.x,y.y,g.x,g.y,m.x,m.y,p[4],p[5]]},d=[].concat(c(h.topLeft),c(h.topRight),c(h.bottomRight),c(h.bottomLeft));return Fa(i,s,d,e,r)},generateBarrelBezierPts:function(e,r,a,n){var i=r/2,s=e/2,o=a-s,l=a+s,u=n-i,f=n+i,h=di(e,r),c=h.heightOffset,d=h.widthOffset,v=h.ctrlPtOffsetPct*e,p={topLeft:[o,u+c,o+v,u,o+d,u],topRight:[l-d,u,l-v,u,l,u+c],bottomRight:[l,f-c,l-v,f,l-d,f],bottomLeft:[o+d,f,o+v,f,o,f-c]};return p.topLeft.isTop=!0,p.topRight.isTop=!0,p.bottomLeft.isBottom=!0,p.bottomRight.isBottom=!0,p},checkPoint:function(e,r,a,n,i,s,o){var l=di(n,i),u=l.heightOffset,f=l.widthOffset;if(pr(e,r,this.points,s,o,n,i-2*u,[0,-1],a)||pr(e,r,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var h=this.generateBarrelBezierPts(n,i,s,o),c=function(x,I,C){var F=C[4],z=C[2],M=C[0],X=C[5],B=C[1],re=Math.min(F,M),q=Math.max(F,M),Z=Math.min(X,B),ie=Math.max(X,B);if(re<=x&&x<=q&&Z<=I&&I<=ie){var ue=Gf(F,z,M),ge=If(ue[0],ue[1],ue[2],x),se=ge.filter(function(ve){return 0<=ve&&ve<=1});if(se.length>0)return se[0]}return null},d=Object.keys(h),v=0;v<d.length;v++){var p=d[v],y=h[p],g=c(e,r,y);if(g!=null){var m=y[5],b=y[3],E=y[1],N=Lt(m,b,E,g);if(y.isTop&&N<=r||y.isBottom&&r<=N)return!0}}return!1}}},mr.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:Vt(4,0),draw:function(e,r,a,n,i){this.renderer.nodeShapeImpl(this.name,e,r,a,n,i)},intersectLine:function(e,r,a,n,i,s,o){var l=e-(a/2+o),u=r-(n/2+o),f=u,h=e+(a/2+o),c=Cr(i,s,e,r,l,u,h,f,!1);return c.length>0?c:xs(i,s,e,r,a,n,o)},checkPoint:function(e,r,a,n,i,s,o){var l=za(n,i),u=2*l;if(pr(e,r,this.points,s,o,n,i-u,[0,-1],a)||pr(e,r,this.points,s,o,n-u,i,[0,-1],a))return!0;var f=n/2+2*a,h=i/2+2*a,c=[s-f,o-h,s-f,o,s+f,o,s+f,o-h];return!!(Ht(e,r,c)||zr(e,r,u,u,s+n/2-l,o+i/2-l,a)||zr(e,r,u,u,s-n/2+l,o+i/2-l,a))}}},mr.registerNodeShapes=function(){var t=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",Vt(3,0)),this.generateRoundPolygon("round-triangle",Vt(3,0)),this.generatePolygon("rectangle",Vt(4,0)),t.square=t.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var r=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",r),this.generateRoundPolygon("round-diamond",r)}this.generatePolygon("pentagon",Vt(5,0)),this.generateRoundPolygon("round-pentagon",Vt(5,0)),this.generatePolygon("hexagon",Vt(6,0)),this.generateRoundPolygon("round-hexagon",Vt(6,0)),this.generatePolygon("heptagon",Vt(7,0)),this.generateRoundPolygon("round-heptagon",Vt(7,0)),this.generatePolygon("octagon",Vt(8,0)),this.generateRoundPolygon("round-octagon",Vt(8,0));var a=new Array(20);{var n=ci(5,0),i=ci(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=Ds(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}t.makePolygon=function(u){var f=u.join("$"),h="polygon-"+f,c;return(c=this[h])?c:e.generatePolygon(h,u)}};var ja={};ja.timeToRender=function(){return this.redrawTotalTime/this.redrawCount},ja.redraw=function(t){t=t||ps();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=t},ja.beforeRender=function(t,e){if(!this.destroyed){e==null&&Tt("Priority is not optional for beforeRender");var r=this.beforeRenderCallbacks;r.push({fn:t,priority:e}),r.sort(function(a,n){return n.priority-a.priority})}};var cu=function(e,r,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(r,a)};ja.startRenderLoop=function(){var t=this,e=t.cy;if(!t.renderLoopStarted){t.renderLoopStarted=!0;var r=function a(n){if(!t.destroyed){if(!e.batching())if(t.requestedFrame&&!t.skipFrame){cu(t,!0,n);var i=gr();t.render(t.renderOptions);var s=t.lastDrawTime=gr();t.averageRedrawTime===void 0&&(t.averageRedrawTime=s-i),t.redrawCount===void 0&&(t.redrawCount=0),t.redrawCount++,t.redrawTotalTime===void 0&&(t.redrawTotalTime=0);var o=s-i;t.redrawTotalTime+=o,t.lastRedrawTime=o,t.averageRedrawTime=t.averageRedrawTime/2+o/2,t.requestedFrame=!1}else cu(t,!1,n);t.skipFrame=!1,hn(a)}};hn(r)}};var yg=function(e){this.init(e)},vu=yg,ba=vu.prototype;ba.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],ba.init=function(t){var e=this;e.options=t,e.cy=t.cy;var r=e.container=t.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(r.className.indexOf(o)<0&&(r.className=(r.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var f=a.getComputedStyle(r),h=f.getPropertyValue("position");h==="static"&&ft("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=t.showFps,e.debug=t.debug,e.hideEdgesOnViewport=t.hideEdgesOnViewport,e.textureOnViewport=t.textureOnViewport,e.wheelSensitivity=t.wheelSensitivity,e.motionBlurEnabled=t.motionBlur,e.forcedPixelRatio=R(t.pixelRatio)?t.pixelRatio:null,e.motionBlur=t.motionBlur,e.motionBlurOpacity=t.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=t.desktopTapThreshold,e.desktopTapThreshold2=t.desktopTapThreshold*t.desktopTapThreshold,e.touchTapThreshold=t.touchTapThreshold,e.touchTapThreshold2=t.touchTapThreshold*t.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()},ba.notify=function(t,e){var r=this,a=r.cy;if(!this.destroyed){if(t==="init"){r.load();return}if(t==="destroy"){r.destroy();return}(t==="add"||t==="remove"||t==="move"&&a.hasCompoundNodes()||t==="load"||t==="zorder"||t==="mount")&&r.invalidateCachedZSortedEles(),t==="viewport"&&r.redrawHint("select",!0),(t==="load"||t==="resize"||t==="mount")&&(r.invalidateContainerClientCoordsCache(),r.matchCanvasSize(r.container)),r.redrawHint("eles",!0),r.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}},ba.destroy=function(){var t=this;t.destroyed=!0,t.cy.stopAnimationLoop();for(var e=0;e<t.bindings.length;e++){var r=t.bindings[e],a=r,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(t.bindings=[],t.beforeRenderCallbacks=[],t.onUpdateEleCalcsFns=[],t.removeObserver&&t.removeObserver.disconnect(),t.styleObserver&&t.styleObserver.disconnect(),t.resizeObserver&&t.resizeObserver.disconnect(),t.labelCalcDiv)try{document.body.removeChild(t.labelCalcDiv)}catch{}},ba.isHeadless=function(){return!1},[Fi,fu,hu,ma,mr,ja].forEach(function(t){Ue(ba,t)});var Gi=1e3/60,du={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=fn(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,f){var h=gr(),c=n.averageRedrawTime,d=n.lastRedrawTime,v=[],p=n.cy.extent(),y=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var g=gr(),m=g-h,b=g-f;if(d<Gi){var E=Gi-(u?c:0);if(b>=e.deqFastCost*E)break}else if(u){if(m>=e.deqCost*d||m>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*Gi)break;var N=e.deq(a,y,p);if(N.length>0)for(var A=0;A<N.length;A++)v.push(N[A]);else break}v.length>0&&(e.onDeqd(a,v),!u&&e.shouldRedraw(a,v,y,p)&&i())},o=e.priority||ii;n.beforeRender(s,o(a))}}}},mg=function(){function t(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:vn;ce(this,t),this.idsByKey=new lr,this.keyForId=new lr,this.cachesByLvl=new lr,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=r}return O(t,[{key:"getIdsFor",value:function(r){r==null&&Tt("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(r);return n||(n=new jr,a.set(r,n)),n}},{key:"addIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).add(a)}},{key:"deleteIdForKey",value:function(r,a){r!=null&&this.getIdsFor(r).delete(a)}},{key:"getNumberOfIdsForKey",value:function(r){return r==null?0:this.getIdsFor(r).size}},{key:"updateKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(r){var a=r.id(),n=this.keyForId.get(a),i=this.getKey(r);return n!==i}},{key:"isInvalid",value:function(r){return this.keyHasChangedFor(r)||this.doesEleInvalidateKey(r)}},{key:"getCachesAt",value:function(r){var a=this.cachesByLvl,n=this.lvls,i=a.get(r);return i||(i=new lr,a.set(r,i),n.push(r)),i}},{key:"getCache",value:function(r,a){return this.getCachesAt(a).get(r)}},{key:"get",value:function(r,a){var n=this.getKey(r),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(r),i}},{key:"getForCachedKey",value:function(r,a){var n=this.keyForId.get(r.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(r,a){return this.getCachesAt(a).has(r)}},{key:"has",value:function(r,a){var n=this.getKey(r);return this.hasCache(n,a)}},{key:"setCache",value:function(r,a,n){n.key=r,this.getCachesAt(a).set(r,n)}},{key:"set",value:function(r,a,n){var i=this.getKey(r);this.setCache(i,a,n),this.updateKeyMappingFor(r)}},{key:"deleteCache",value:function(r,a){this.getCachesAt(a).delete(r)}},{key:"delete",value:function(r,a){var n=this.getKey(r);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(r){var a=this;this.lvls.forEach(function(n){return a.deleteCache(r,n)})}},{key:"invalidate",value:function(r){var a=r.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(r);var i=this.doesEleInvalidateKey(r);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}]),t}(),gu=25,Kn=50,Zn=-4,$i=3,bg=7.99,Eg=8,wg=1024,xg=1024,Tg=1024,Cg=.2,Dg=.8,Sg=10,Lg=.15,Ag=.1,Og=.9,Ng=.9,Mg=100,Ig=1,Ea={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},Rg=At({getKey:null,doesEleInvalidateKey:vn,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:cs,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),en=function(e,r){var a=this;a.renderer=e,a.onDequeues=[];var n=Rg(r);Ue(a,n),a.lookup=new mg(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},St=en.prototype;St.reasons=Ea,St.getTextureQueue=function(t){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[t]=e.eleImgCaches[t]||[]},St.getRetiredTextureQueue=function(t){var e=this,r=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=r[t]=r[t]||[];return a},St.getElementQueue=function(){var t=this,e=t.eleCacheQueue=t.eleCacheQueue||new ka(function(r,a){return a.reqs-r.reqs});return e},St.getElementKeyToQueue=function(){var t=this,e=t.eleKeyToCacheQueue=t.eleKeyToCacheQueue||{};return e},St.getElement=function(t,e,r,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!t.visible()||t.removed()||!i.allowEdgeTxrCaching&&t.isEdge()||!i.allowParentTxrCaching&&t.isParent())return null;if(a==null&&(a=Math.ceil(ui(o*r))),a<Zn)a=Zn;else if(o>=bg||a>$i)return null;var u=Math.pow(2,a),f=e.h*u,h=e.w*u,c=s.eleTextBiggerThanMin(t,u);if(!this.isVisible(t,c))return null;var d=l.get(t,a);if(d&&d.invalidated&&(d.invalidated=!1,d.texture.invalidatedWidth-=d.width),d)return d;var v;if(f<=gu?v=gu:f<=Kn?v=Kn:v=Math.ceil(f/Kn)*Kn,f>Tg||h>xg)return null;var p=i.getTextureQueue(v),y=p[p.length-2],g=function(){return i.recycleTexture(v,h)||i.addTexture(v,h)};y||(y=p[p.length-1]),y||(y=g()),y.width-y.usedWidth<h&&(y=g());for(var m=function(q){return q&&q.scaledLabelShown===c},b=n&&n===Ea.dequeue,E=n&&n===Ea.highQuality,N=n&&n===Ea.downscale,A,x=a+1;x<=$i;x++){var I=l.get(t,x);if(I){A=I;break}}var C=A&&A.level===a+1?A:null,F=function(){y.context.drawImage(C.texture.canvas,C.x,0,C.width,C.height,y.usedWidth,0,h,f)};if(y.context.setTransform(1,0,0,1,0,0),y.context.clearRect(y.usedWidth,0,h,v),m(C))F();else if(m(A))if(E){for(var z=A.level;z>a;z--)C=i.getElement(t,e,r,z,Ea.downscale);F()}else return i.queueElement(t,A.level-1),A;else{var M;if(!b&&!E&&!N)for(var X=a-1;X>=Zn;X--){var B=l.get(t,X);if(B){M=B;break}}if(m(M))return i.queueElement(t,a),M;y.context.translate(y.usedWidth,0),y.context.scale(u,u),this.drawElement(y.context,t,e,c,!1),y.context.scale(1/u,1/u),y.context.translate(-y.usedWidth,0)}return d={x:y.usedWidth,texture:y,level:a,scale:u,width:h,height:f,scaledLabelShown:c},y.usedWidth+=Math.ceil(h+Eg),y.eleCaches.push(d),l.set(t,a,d),i.checkTextureFullness(y),d},St.invalidateElements=function(t){for(var e=0;e<t.length;e++)this.invalidateElement(t[e])},St.invalidateElement=function(t){var e=this,r=e.lookup,a=[],n=r.isInvalid(t);if(n){for(var i=Zn;i<=$i;i++){var s=r.getForCachedKey(t,i);s&&a.push(s)}var o=r.invalidate(t);if(o)for(var l=0;l<a.length;l++){var u=a[l],f=u.texture;f.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(f)}e.removeFromQueue(t)}},St.checkTextureUtility=function(t){t.invalidatedWidth>=Cg*t.width&&this.retireTexture(t)},St.checkTextureFullness=function(t){var e=this,r=e.getTextureQueue(t.height);t.usedWidth/t.width>Dg&&t.fullnessChecks>=Sg?xr(r,t):t.fullnessChecks++},St.retireTexture=function(t){var e=this,r=t.height,a=e.getTextureQueue(r),n=this.lookup;xr(a,t),t.retired=!0;for(var i=t.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}si(i);var l=e.getRetiredTextureQueue(r);l.push(t)},St.addTexture=function(t,e){var r=this,a=r.getTextureQueue(t),n={};return a.push(n),n.eleCaches=[],n.height=t,n.width=Math.max(wg,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=r.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n},St.recycleTexture=function(t,e){for(var r=this,a=r.getTextureQueue(t),n=r.getRetiredTextureQueue(t),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,si(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),xr(n,s),a.push(s),s}},St.queueElement=function(t,e){var r=this,a=r.getElementQueue(),n=r.getElementKeyToQueue(),i=this.getKey(t),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(t),s.reqs++,a.updateItem(s);else{var o={eles:t.spawn().merge(t),level:e,reqs:1,key:i};a.push(o),n[i]=o}},St.dequeue=function(t){for(var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<Ig&&r.size()>0;s++){var o=r.pop(),l=o.key,u=o.eles[0],f=i.hasCache(u,o.level);if(a[l]=null,f)continue;n.push(o);var h=e.getBoundingBox(u);e.getElement(u,h,t,o.level,Ea.dequeue)}return n},St.removeFromQueue=function(t){var e=this,r=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(t),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=ni,r.updateItem(i),r.pop(),a[n]=null):i.eles.unmerge(t))},St.onDequeue=function(t){this.onDequeues.push(t)},St.offDequeue=function(t){xr(this.onDequeues,t)},St.setupDequeueing=du.setupDequeueing({deqRedrawThreshold:Mg,deqCost:Lg,deqAvgCost:Ag,deqNoDrawCost:Og,deqFastCost:Ng,deq:function(e,r,a){return e.dequeue(r,a)},onDeqd:function(e,r){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(r)}},shouldRedraw:function(e,r,a,n){for(var i=0;i<r.length;i++)for(var s=r[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(fi(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var kg=1,tn=-4,Qn=2,Pg=3.99,Bg=50,Fg=50,zg=.15,Gg=.1,$g=.9,Vg=.9,_g=1,pu=250,Ug=4e3*4e3,Yg=!0,yu=function(e){var r=this,a=r.renderer=e,n=a.cy;r.layersByLevel={},r.firstGet=!0,r.lastInvalidationTime=gr()-2*pu,r.skipping=!1,r.eleTxrDeqs=n.collection(),r.scheduleElementRefinement=fn(function(){r.refineElementTextures(r.eleTxrDeqs),r.eleTxrDeqs.unmerge(r.eleTxrDeqs)},Fg),a.beforeRender(function(s,o){o-r.lastInvalidationTime<=pu?r.skipping=!0:r.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};r.layersQueue=new ka(i),r.setupDequeueing()},It=yu.prototype,mu=0,Hg=Math.pow(2,53)-1;It.makeLayer=function(t,e){var r=Math.pow(2,e),a=Math.ceil(t.w*r),n=Math.ceil(t.h*r),i=this.renderer.makeOffscreenCanvas(a,n),s={id:mu=++mu%Hg,bb:t,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(r,r),o.translate(l,u),s},It.getLayers=function(t,e,r){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,r==null){if(r=Math.ceil(ui(s*e)),r<tn)r=tn;else if(s>=Pg||r>Qn)return null}a.validateLayersElesOrdering(r,t);var l=a.layersByLevel,u=Math.pow(2,r),f=l[r]=l[r]||[],h,c=a.levelIsComplete(r,t),d,v=function(){var F=function(re){if(a.validateLayersElesOrdering(re,t),a.levelIsComplete(re,t))return d=l[re],!0},z=function(re){if(!d)for(var q=r+re;tn<=q&&q<=Qn&&!F(q);q+=re);};z(1),z(-1);for(var M=f.length-1;M>=0;M--){var X=f[M];X.invalid&&xr(f,X)}};if(!c)v();else return f;var p=function(){if(!h){h=Yt();for(var F=0;F<t.length;F++)Lf(h,t[F].boundingBox())}return h},y=function(F){F=F||{};var z=F.after;p();var M=h.w*u*(h.h*u);if(M>Ug)return null;var X=a.makeLayer(h,r);if(z!=null){var B=f.indexOf(z)+1;f.splice(B,0,X)}else(F.insert===void 0||F.insert)&&f.unshift(X);return X};if(a.skipping&&!o)return null;for(var g=null,m=t.length/kg,b=!o,E=0;E<t.length;E++){var N=t[E],A=N._private.rscratch,x=A.imgLayerCaches=A.imgLayerCaches||{},I=x[r];if(I){g=I;continue}if((!g||g.eles.length>=m||!ws(g.bb,N.boundingBox()))&&(g=y({insert:!0,after:g}),!g))return null;d||b?a.queueLayer(g,N):a.drawEleInLayer(g,N,r,e),g.eles.push(N),x[r]=g}return d||(b?null:f)},It.getEleLevelForLayerLevel=function(t,e){return t},It.drawEleInLayer=function(t,e,r,a){var n=this,i=this.renderer,s=t.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(r=n.getEleLevelForLayerLevel(r,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,r,Yg),i.setImgSmoothing(s,!0))},It.levelIsComplete=function(t,e){var r=this,a=r.layersByLevel[t];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length},It.validateLayersElesOrdering=function(t,e){var r=this.layersByLevel[t];if(r)for(var a=0;a<r.length;a++){for(var n=r[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}},It.updateElementsInLayers=function(t,e){for(var r=this,a=Ae(t[0]),n=0;n<t.length;n++)for(var i=a?null:t[n],s=a?t[n]:t[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=tn;u<=Qn;u++){var f=l[u];f&&(i&&r.getEleLevelForLayerLevel(f.level)!==i.level||e(f,s,i))}},It.haveLayers=function(){for(var t=this,e=!1,r=tn;r<=Qn;r++){var a=t.layersByLevel[r];if(a&&a.length>0){e=!0;break}}return e},It.invalidateElements=function(t){var e=this;t.length!==0&&(e.lastInvalidationTime=gr(),!(t.length===0||!e.haveLayers())&&e.updateElementsInLayers(t,function(a,n,i){e.invalidateLayer(a)}))},It.invalidateLayer=function(t){if(this.lastInvalidationTime=gr(),!t.invalid){var e=t.level,r=t.eles,a=this.layersByLevel[e];xr(a,t),t.elesQueue=[],t.invalid=!0,t.replacement&&(t.replacement.invalid=!0);for(var n=0;n<r.length;n++){var i=r[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}},It.refineElementTextures=function(t){var e=this;e.updateElementsInLayers(t,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})},It.enqueueElementRefinement=function(t){this.eleTxrDeqs.merge(t),this.scheduleElementRefinement()},It.queueLayer=function(t,e){var r=this,a=r.layersQueue,n=t.elesQueue,i=n.hasId=n.hasId||{};if(!t.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}t.reqs?(t.reqs++,a.updateItem(t)):(t.reqs=1,a.push(t))}},It.dequeue=function(t){for(var e=this,r=e.layersQueue,a=[],n=0;n<_g&&r.size()!==0;){var i=r.peek();if(i.replacement){r.pop();continue}if(i.replaces&&i!==i.replaces.replacement){r.pop();continue}if(i.invalid){r.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,t),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(r.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a},It.applyLayerReplacement=function(t){var e=this,r=e.layersByLevel[t.level],a=t.replaces,n=r.indexOf(a);if(!(n<0||a.invalid)){r[n]=t;for(var i=0;i<t.eles.length;i++){var s=t.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[t.level]=t)}e.requestRedraw()}},It.requestRedraw=fn(function(){var t=this.renderer;t.redrawHint("eles",!0),t.redrawHint("drag",!0),t.redraw()},100),It.setupDequeueing=du.setupDequeueing({deqRedrawThreshold:Bg,deqCost:zg,deqAvgCost:Gg,deqNoDrawCost:$g,deqFastCost:Vg,deq:function(e,r){return e.dequeue(r)},onDeqd:ii,shouldRedraw:cs,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var bu={},Eu;function Xg(t,e){for(var r=0;r<e.length;r++){var a=e[r];t.lineTo(a.x,a.y)}}function Wg(t,e,r){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),t.lineTo(i.x,i.y)}t.quadraticCurveTo(r.x,r.y,a.x,a.y)}function wu(t,e,r){t.beginPath&&t.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];t.lineTo(i.x,i.y)}var s=r,o=r[0];t.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];t.lineTo(i.x,i.y)}t.closePath&&t.closePath()}function qg(t,e,r,a,n){t.beginPath&&t.beginPath(),t.arc(r,a,n,0,Math.PI*2,!1);var i=e,s=i[0];t.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];t.lineTo(l.x,l.y)}t.closePath&&t.closePath()}function Kg(t,e,r,a){t.arc(e,r,a,0,Math.PI*2,!1)}bu.arrowShapeImpl=function(t){return(Eu||(Eu={polygon:Xg,"triangle-backcurve":Wg,"triangle-tee":wu,"circle-triangle":qg,"triangle-cross":wu,circle:Kg}))[t]};var cr={};cr.drawElement=function(t,e,r,a,n,i){var s=this;e.isNode()?s.drawNode(t,e,r,a,n,i):s.drawEdge(t,e,r,a,n,i)},cr.drawElementOverlay=function(t,e){var r=this;e.isNode()?r.drawNodeOverlay(t,e):r.drawEdgeOverlay(t,e)},cr.drawElementUnderlay=function(t,e){var r=this;e.isNode()?r.drawNodeUnderlay(t,e):r.drawEdgeUnderlay(t,e)},cr.drawCachedElementPortion=function(t,e,r,a,n,i,s,o){var l=this,u=r.getBoundingBox(e);if(!(u.w===0||u.h===0)){var f=r.getElement(e,u,a,n,i);if(f!=null){var h=o(l,e);if(h===0)return;var c=s(l,e),d=u.x1,v=u.y1,p=u.w,y=u.h,g,m,b,E,N;if(c!==0){var A=r.getRotationPoint(e);b=A.x,E=A.y,t.translate(b,E),t.rotate(c),N=l.getImgSmoothing(t),N||l.setImgSmoothing(t,!0);var x=r.getRotationOffset(e);g=x.x,m=x.y}else g=d,m=v;var I;h!==1&&(I=t.globalAlpha,t.globalAlpha=I*h),t.drawImage(f.texture.canvas,f.x,0,f.width,f.height,g,m,p,y),h!==1&&(t.globalAlpha=I),c!==0&&(t.rotate(-c),t.translate(-b,-E),N||l.setImgSmoothing(t,!1))}else r.drawElement(t,e)}};var Zg=function(){return 0},Qg=function(e,r){return e.getTextAngle(r,null)},Jg=function(e,r){return e.getTextAngle(r,"source")},jg=function(e,r){return e.getTextAngle(r,"target")},ep=function(e,r){return r.effectiveOpacity()},Vi=function(e,r){return r.pstyle("text-opacity").pfValue*r.effectiveOpacity()};cr.drawCachedElement=function(t,e,r,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,f=o.slbTxrCache,h=o.tlbTxrCache,c=e.boundingBox(),d=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||fi(c,a))){var v=e.isEdge(),p=e.element()._private.rscratch.badLine;s.drawElementUnderlay(t,e),s.drawCachedElementPortion(t,e,l,r,n,d,Zg,ep),(!v||!p)&&s.drawCachedElementPortion(t,e,u,r,n,d,Qg,Vi),v&&!p&&(s.drawCachedElementPortion(t,e,f,r,n,d,Jg,Vi),s.drawCachedElementPortion(t,e,h,r,n,d,jg,Vi)),s.drawElementOverlay(t,e)}},cr.drawElements=function(t,e){for(var r=this,a=0;a<e.length;a++){var n=e[a];r.drawElement(t,n)}},cr.drawCachedElements=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(t,s,r,a)}},cr.drawCachedNodes=function(t,e,r,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(t,s,r,a)}},cr.drawLayeredElements=function(t,e,r,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,r);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||t.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(t,e,r,a)};var br={};br.drawEdge=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;r&&(l=r,t.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,f=i?e.pstyle("line-opacity").value:1,h=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,d=e.pstyle("width").pfValue,v=e.pstyle("line-cap").value,p=u*f,y=u*f,g=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;h==="straight-triangle"?(s.eleStrokeStyle(t,e,M),s.drawEdgeTrianglePath(e,t,o.allpts)):(t.lineWidth=d,t.lineCap=v,s.eleStrokeStyle(t,e,M),s.drawEdgePath(e,t,o.allpts,c),t.lineCap="butt")},m=function(){n&&s.drawEdgeOverlay(t,e)},b=function(){n&&s.drawEdgeUnderlay(t,e)},E=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y;s.drawArrowheads(t,e,M)},N=function(){s.drawElementText(t,e,null,a)};t.lineJoin="round";var A=e.pstyle("ghost").value==="yes";if(A){var x=e.pstyle("ghost-offset-x").pfValue,I=e.pstyle("ghost-offset-y").pfValue,C=e.pstyle("ghost-opacity").value,F=p*C;t.translate(x,I),g(F),E(F),t.translate(-x,-I)}b(),g(),E(),m(),N(),r&&t.translate(l.x1,l.y1)}};var xu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,f=a.pstyle("".concat(e,"-color")).value;r.lineWidth=u,o.edgeType==="self"&&!s?r.lineCap="butt":r.lineCap="round",i.colorStrokeStyle(r,f[0],f[1],f[2],n),i.drawEdgePath(a,r,o.allpts,"solid")}}}};br.drawEdgeOverlay=xu("overlay"),br.drawEdgeUnderlay=xu("underlay"),br.drawEdgePath=function(t,e,r,a){var n=t._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=t.pstyle("line-dash-pattern").pfValue,f=t.pstyle("line-dash-offset").pfValue;if(l){var h=r.join("$"),c=n.pathCacheKey&&n.pathCacheKey===h;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=h,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=f;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(r[0],r[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var d=2;d+3<r.length;d+=4)e.quadraticCurveTo(r[d],r[d+1],r[d+2],r[d+3]);break;case"straight":case"segments":case"haystack":for(var v=2;v+1<r.length;v+=2)e.lineTo(r[v],r[v+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])},br.drawEdgeTrianglePath=function(t,e,r){e.fillStyle=e.strokeStyle;for(var a=t.pstyle("width").pfValue,n=0;n+1<r.length;n+=2){var i=[r[n+2]-r[n],r[n+3]-r[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(r[n]-l[0],r[n+1]-l[1]),e.lineTo(r[n]+l[0],r[n+1]+l[1]),e.lineTo(r[n+2],r[n+3]),e.closePath(),e.fill()}},br.drawArrowheads=function(t,e,r){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(t,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,r),this.drawArrowhead(t,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,r),this.drawArrowhead(t,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,r),n||this.drawArrowhead(t,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,r)},br.drawArrowhead=function(t,e,r,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(r+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(r+"-arrow-fill").value==="hollow"?"both":"filled",f=e.pstyle(r+"-arrow-fill").value,h=e.pstyle("width").pfValue,c=e.pstyle("opacity").value;s===void 0&&(s=c);var d=t.globalCompositeOperation;(s!==1||f==="hollow")&&(t.globalCompositeOperation="destination-out",o.colorFillStyle(t,255,255,255,1),o.colorStrokeStyle(t,255,255,255,1),o.drawArrowShape(e,t,u,h,l,a,n,i),t.globalCompositeOperation=d);var v=e.pstyle(r+"-arrow-color").value;o.colorFillStyle(t,v[0],v[1],v[2],s),o.colorStrokeStyle(t,v[0],v[1],v[2],s),o.drawArrowShape(e,t,f,h,l,a,n,i)}}},br.drawArrowShape=function(t,e,r,a,n,i,s,o){var l=this,u=this.usePaths()&&n!=="triangle-cross",f=!1,h,c=e,d={x:i,y:s},v=t.pstyle("arrow-scale").value,p=this.getArrowWidth(a,v),y=l.arrowShapes[n];if(u){var g=l.arrowPathCache=l.arrowPathCache||[],m=Pr(n),b=g[m];b!=null?(h=e=b,f=!0):(h=e=new Path2D,g[m]=h)}f||(e.beginPath&&e.beginPath(),u?y.draw(e,1,0,{x:0,y:0},1):y.draw(e,p,o,d,a),e.closePath&&e.closePath()),e=c,u&&(e.translate(i,s),e.rotate(o),e.scale(p,p)),(r==="filled"||r==="both")&&(u?e.fill(h):e.fill()),(r==="hollow"||r==="both")&&(e.lineWidth=(y.matchEdgeWidth?a:1)/(u?p:1),e.lineJoin="miter",u?e.stroke(h):e.stroke()),u&&(e.scale(1/p,1/p),e.rotate(-o),e.translate(-i,-s))};var _i={};_i.safeDrawImage=function(t,e,r,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{t.drawImage(e,r,a,n,i,s,o,l,u)}catch(f){ft(f)}},_i.drawInscribedImage=function(t,e,r,a,n){var i=this,s=r.position(),o=s.x,l=s.y,u=r.cy().style(),f=u.getIndexedStyle.bind(u),h=f(r,"background-fit","value",a),c=f(r,"background-repeat","value",a),d=r.width(),v=r.height(),p=r.padding()*2,y=d+(f(r,"background-width-relative-to","value",a)==="inner"?0:p),g=v+(f(r,"background-height-relative-to","value",a)==="inner"?0:p),m=r._private.rscratch,b=f(r,"background-clip","value",a),E=b==="node",N=f(r,"background-image-opacity","value",a)*n,A=f(r,"background-image-smoothing","value",a),x=e.width||e.cachedW,I=e.height||e.cachedH;(x==null||I==null)&&(document.body.appendChild(e),x=e.cachedW=e.width||e.offsetWidth,I=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var C=x,F=I;if(f(r,"background-width","value",a)!=="auto"&&(f(r,"background-width","units",a)==="%"?C=f(r,"background-width","pfValue",a)*y:C=f(r,"background-width","pfValue",a)),f(r,"background-height","value",a)!=="auto"&&(f(r,"background-height","units",a)==="%"?F=f(r,"background-height","pfValue",a)*g:F=f(r,"background-height","pfValue",a)),!(C===0||F===0)){if(h==="contain"){var z=Math.min(y/C,g/F);C*=z,F*=z}else if(h==="cover"){var z=Math.max(y/C,g/F);C*=z,F*=z}var M=o-y/2,X=f(r,"background-position-x","units",a),B=f(r,"background-position-x","pfValue",a);X==="%"?M+=(y-C)*B:M+=B;var re=f(r,"background-offset-x","units",a),q=f(r,"background-offset-x","pfValue",a);re==="%"?M+=(y-C)*q:M+=q;var Z=l-g/2,ie=f(r,"background-position-y","units",a),ue=f(r,"background-position-y","pfValue",a);ie==="%"?Z+=(g-F)*ue:Z+=ue;var ge=f(r,"background-offset-y","units",a),se=f(r,"background-offset-y","pfValue",a);ge==="%"?Z+=(g-F)*se:Z+=se,m.pathCache&&(M-=o,Z-=l,o=0,l=0);var ve=t.globalAlpha;t.globalAlpha=N;var ye=i.getImgSmoothing(t),Te=!1;if(A==="no"&&ye?(i.setImgSmoothing(t,!1),Te=!0):A==="yes"&&!ye&&(i.setImgSmoothing(t,!0),Te=!0),c==="no-repeat")E&&(t.save(),m.pathCache?t.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(r)].draw(t,o,l,y,g),t.clip())),i.safeDrawImage(t,e,0,0,x,I,M,Z,C,F),E&&t.restore();else{var be=t.createPattern(e,c);t.fillStyle=be,i.nodeShapes[i.getNodeShape(r)].draw(t,o,l,y,g),t.translate(M,Z),t.fill(),t.translate(-M,-Z)}t.globalAlpha=ve,Te&&i.setImgSmoothing(t,ye)}};var Ur={};Ur.eleTextBiggerThanMin=function(t,e){if(!e){var r=t.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(ui(r*a));e=Math.pow(2,n)}var i=t.pstyle("font-size").pfValue*e,s=t.pstyle("min-zoomed-font-size").pfValue;return!(i<s)},Ur.drawElementText=function(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);t.textAlign=l,t.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,f=e.pstyle("label"),h=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!f||!f.value)&&(!h||!h.value)&&(!c||!c.value))return;t.textAlign="center",t.textBaseline="bottom"}var d=!r,v;r&&(v=r,t.translate(-v.x1,-v.y1)),n==null?(s.drawText(t,e,null,d,i),e.isEdge()&&(s.drawText(t,e,"source",d,i),s.drawText(t,e,"target",d,i))):s.drawText(t,e,n,d,i),r&&t.translate(v.x1,v.y1)},Ur.getFontCache=function(t){var e;this.fontCaches=this.fontCaches||[];for(var r=0;r<this.fontCaches.length;r++)if(e=this.fontCaches[r],e.context===t)return e;return e={context:t},this.fontCaches.push(e),e},Ur.setupTextStyle=function(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=r?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,f=e.pstyle("text-outline-color").value;t.font=a+" "+s+" "+n+" "+i,t.lineJoin="round",this.colorFillStyle(t,u[0],u[1],u[2],o),this.colorStrokeStyle(t,f[0],f[1],f[2],l)};function tp(t,e,r,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5;t.beginPath(),t.moveTo(e+i,r),t.lineTo(e+a-i,r),t.quadraticCurveTo(e+a,r,e+a,r+i),t.lineTo(e+a,r+n-i),t.quadraticCurveTo(e+a,r+n,e+a-i,r+n),t.lineTo(e+i,r+n),t.quadraticCurveTo(e,r+n,e,r+n-i),t.lineTo(e,r+i),t.quadraticCurveTo(e,r,e+i,r),t.closePath(),t.fill()}Ur.getTextAngle=function(t,e){var r,a=t._private,n=a.rscratch,i=e?e+"-":"",s=t.pstyle(i+"text-rotation"),o=tr(n,"labelAngle",e);return s.strValue==="autorotate"?r=t.isEdge()?o:0:s.strValue==="none"?r=0:r=s.pfValue,r},Ur.drawText=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){r==="main"&&(r=null);var l=tr(s,"labelX",r),u=tr(s,"labelY",r),f,h,c=this.getLabelText(e,r);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(t,e,n);var d=r?r+"-":"",v=tr(s,"labelWidth",r),p=tr(s,"labelHeight",r),y=e.pstyle(d+"text-margin-x").pfValue,g=e.pstyle(d+"text-margin-y").pfValue,m=e.isEdge(),b=e.pstyle("text-halign").value,E=e.pstyle("text-valign").value;m&&(b="center",E="center"),l+=y,u+=g;var N;switch(a?N=this.getTextAngle(e,r):N=0,N!==0&&(f=l,h=u,t.translate(f,h),t.rotate(N),l=0,u=0),E){case"top":break;case"center":u+=p/2;break;case"bottom":u+=p;break}var A=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,I=e.pstyle("text-border-width").pfValue,C=e.pstyle("text-background-padding").pfValue;if(A>0||I>0&&x>0){var F=l-C;switch(b){case"left":F-=v;break;case"center":F-=v/2;break}var z=u-p-C,M=v+2*C,X=p+2*C;if(A>0){var B=t.fillStyle,re=e.pstyle("text-background-color").value;t.fillStyle="rgba("+re[0]+","+re[1]+","+re[2]+","+A*o+")";var q=e.pstyle("text-background-shape").strValue;q.indexOf("round")===0?tp(t,F,z,M,X,2):t.fillRect(F,z,M,X),t.fillStyle=B}if(I>0&&x>0){var Z=t.strokeStyle,ie=t.lineWidth,ue=e.pstyle("text-border-color").value,ge=e.pstyle("text-border-style").value;if(t.strokeStyle="rgba("+ue[0]+","+ue[1]+","+ue[2]+","+x*o+")",t.lineWidth=I,t.setLineDash)switch(ge){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"double":t.lineWidth=I/4,t.setLineDash([]);break;case"solid":t.setLineDash([]);break}if(t.strokeRect(F,z,M,X),ge==="double"){var se=I/2;t.strokeRect(F+se,z+se,M-se*2,X-se*2)}t.setLineDash&&t.setLineDash([]),t.lineWidth=ie,t.strokeStyle=Z}}var ve=2*e.pstyle("text-outline-width").pfValue;if(ve>0&&(t.lineWidth=ve),e.pstyle("text-wrap").value==="wrap"){var ye=tr(s,"labelWrapCachedLines",r),Te=tr(s,"labelLineHeight",r),be=v/2,me=this.getLabelJustification(e);switch(me==="auto"||(b==="left"?me==="left"?l+=-v:me==="center"&&(l+=-be):b==="center"?me==="left"?l+=-be:me==="right"&&(l+=be):b==="right"&&(me==="center"?l+=be:me==="right"&&(l+=v))),E){case"top":u-=(ye.length-1)*Te;break;case"center":case"bottom":u-=(ye.length-1)*Te;break}for(var ae=0;ae<ye.length;ae++)ve>0&&t.strokeText(ye[ae],l,u),t.fillText(ye[ae],l,u),u+=Te}else ve>0&&t.strokeText(c,l,u),t.fillText(c,l,u);N!==0&&(t.rotate(-N),t.translate(-f,-h))}}};var wa={};wa.drawNode=function(t,e,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,f=u.rscratch,h=e.position();if(!(!R(h.x)||!R(h.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,d=s.usePaths(),v,p=!1,y=e.padding();o=e.width()+2*y,l=e.height()+2*y;var g;r&&(g=r,t.translate(-g.x1,-g.y1));for(var m=e.pstyle("background-image"),b=m.value,E=new Array(b.length),N=new Array(b.length),A=0,x=0;x<b.length;x++){var I=b[x],C=E[x]=I!=null&&I!=="none";if(C){var F=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);A++,N[x]=s.getCachedImage(I,F,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var z=e.pstyle("background-blacken").value,M=e.pstyle("border-width").pfValue,X=e.pstyle("background-opacity").value*c,B=e.pstyle("border-color").value,re=e.pstyle("border-style").value,q=e.pstyle("border-opacity").value*c;t.lineJoin="miter";var Z=function(){var ke=arguments.length>0&&arguments[0]!==void 0?arguments[0]:X;s.eleFillStyle(t,e,ke)},ie=function(){var ke=arguments.length>0&&arguments[0]!==void 0?arguments[0]:q;s.colorStrokeStyle(t,B[0],B[1],B[2],ke)},ue=e.pstyle("shape").strValue,ge=e.pstyle("shape-polygon-points").pfValue;if(d){t.translate(h.x,h.y);var se=s.nodePathCache=s.nodePathCache||[],ve=fs(ue==="polygon"?ue+","+ge.join(","):ue,""+l,""+o),ye=se[ve];ye!=null?(v=ye,p=!0,f.pathCache=v):(v=new Path2D,se[ve]=f.pathCache=v)}var Te=function(){if(!p){var ke=h;d&&(ke={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(v||t,ke.x,ke.y,o,l)}d?t.fill(v):t.fill()},be=function(){for(var ke=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,ze=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,je=u.backgrounding,Ze=0,Ye=0;Ye<N.length;Ye++){var ct=e.cy().style().getIndexedStyle(e,"background-image-containment","value",Ye);if(ze&&ct==="over"||!ze&&ct==="inside"){Ze++;continue}E[Ye]&&N[Ye].complete&&!N[Ye].error&&(Ze++,s.drawInscribedImage(t,N[Ye],e,Ye,ke))}u.backgrounding=Ze!==A,je!==u.backgrounding&&e.updateStyle(!1)},me=function(){var ke=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,ze=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(t,e,ze),ke&&(d||s.nodeShapes[s.getNodeShape(e)].draw(t,h.x,h.y,o,l)))},ae=function(){var ke=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,ze=(z>0?z:-z)*ke,je=z>0?0:255;z!==0&&(s.colorFillStyle(t,je,je,je,ze),d?t.fill(v):t.fill())},xe=function(){if(M>0){if(t.lineWidth=M,t.lineCap="butt",t.setLineDash)switch(re){case"dotted":t.setLineDash([1,1]);break;case"dashed":t.setLineDash([4,2]);break;case"solid":case"double":t.setLineDash([]);break}if(d?t.stroke(v):t.stroke(),re==="double"){t.lineWidth=M/3;var ke=t.globalCompositeOperation;t.globalCompositeOperation="destination-out",d?t.stroke(v):t.stroke(),t.globalCompositeOperation=ke}t.setLineDash&&t.setLineDash([])}},Ce=function(){n&&s.drawNodeOverlay(t,e,h,o,l)},Oe=function(){n&&s.drawNodeUnderlay(t,e,h,o,l)},Me=function(){s.drawElementText(t,e,null,a)},He=e.pstyle("ghost").value==="yes";if(He){var We=e.pstyle("ghost-offset-x").pfValue,Re=e.pstyle("ghost-offset-y").pfValue,Ie=e.pstyle("ghost-opacity").value,Ge=Ie*c;t.translate(We,Re),Z(Ie*X),Te(),be(Ge,!0),ie(Ie*q),xe(),me(z!==0||M!==0),be(Ge,!1),ae(Ge),t.translate(-We,-Re)}d&&t.translate(-h.x,-h.y),Oe(),d&&t.translate(h.x,h.y),Z(),Te(),be(c,!0),ie(),xe(),me(z!==0||M!==0),be(c,!1),ae(),d&&t.translate(-h.x,-h.y),Me(),Ce(),r&&t.translate(g.x1,g.y1)}};var Tu=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(r,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,f=a.pstyle("".concat(e,"-color")).value,h=a.pstyle("".concat(e,"-shape")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var c=a.padding();i=a.width()+2*c,s=a.height()+2*c}o.colorFillStyle(r,f[0],f[1],f[2],u),o.nodeShapes[h].draw(r,n.x,n.y,i+l*2,s+l*2),r.fill()}}}};wa.drawNodeOverlay=Tu("overlay"),wa.drawNodeUnderlay=Tu("underlay"),wa.hasPie=function(t){return t=t[0],t._private.hasPie},wa.drawPie=function(t,e,r,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=a.x,o=a.y,l=e.width(),u=e.height(),f=Math.min(l,u)/2,h=0,c=this.usePaths();c&&(s=0,o=0),i.units==="%"?f=f*i.pfValue:i.pfValue!==void 0&&(f=i.pfValue/2);for(var d=1;d<=n.pieBackgroundN;d++){var v=e.pstyle("pie-"+d+"-background-size").value,p=e.pstyle("pie-"+d+"-background-color").value,y=e.pstyle("pie-"+d+"-background-opacity").value*r,g=v/100;g+h>1&&(g=1-h);var m=1.5*Math.PI+2*Math.PI*h,b=2*Math.PI*g,E=m+b;v===0||h>=1||h+g>1||(t.beginPath(),t.moveTo(s,o),t.arc(s,o,f,m,E),t.closePath(),this.colorFillStyle(t,p[0],p[1],p[2],y),t.fill(),h+=g)}};var Kt={},rp=100;Kt.getPixelRatio=function(){var t=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/e},Kt.paintCache=function(t){for(var e=this.paintCaches=this.paintCaches||[],r=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===t){r=!1;break}return r&&(a={context:t},e.push(a)),a},Kt.createGradientStyleFor=function(t,e,r,a,n){var i,s=this.usePaths(),o=r.pstyle(e+"-gradient-stop-colors").value,l=r.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(r.isEdge()){var u=r.sourceEndpoint(),f=r.targetEndpoint(),h=r.midpoint(),c=Br(u,h),d=Br(f,h);i=t.createRadialGradient(h.x,h.y,0,h.x,h.y,Math.max(c,d))}else{var v=s?{x:0,y:0}:r.position(),p=r.paddedWidth(),y=r.paddedHeight();i=t.createRadialGradient(v.x,v.y,0,v.x,v.y,Math.max(p,y))}else if(r.isEdge()){var g=r.sourceEndpoint(),m=r.targetEndpoint();i=t.createLinearGradient(g.x,g.y,m.x,m.y)}else{var b=s?{x:0,y:0}:r.position(),E=r.paddedWidth(),N=r.paddedHeight(),A=E/2,x=N/2,I=r.pstyle("background-gradient-direction").value;switch(I){case"to-bottom":i=t.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=t.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=t.createLinearGradient(b.x+A,b.y,b.x-A,b.y);break;case"to-right":i=t.createLinearGradient(b.x-A,b.y,b.x+A,b.y);break;case"to-bottom-right":case"to-right-bottom":i=t.createLinearGradient(b.x-A,b.y-x,b.x+A,b.y+x);break;case"to-top-right":case"to-right-top":i=t.createLinearGradient(b.x-A,b.y+x,b.x+A,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=t.createLinearGradient(b.x+A,b.y-x,b.x-A,b.y+x);break;case"to-top-left":case"to-left-top":i=t.createLinearGradient(b.x+A,b.y+x,b.x-A,b.y-x);break}}if(!i)return null;for(var C=l.length===o.length,F=o.length,z=0;z<F;z++)i.addColorStop(C?l[z]:z/(F-1),"rgba("+o[z][0]+","+o[z][1]+","+o[z][2]+","+n+")");return i},Kt.gradientFillStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"background",e,r,a);if(!n)return null;t.fillStyle=n},Kt.colorFillStyle=function(t,e,r,a,n){t.fillStyle="rgba("+e+","+r+","+a+","+n+")"},Kt.eleFillStyle=function(t,e,r){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(t,e,a,r);else{var n=e.pstyle("background-color").value;this.colorFillStyle(t,n[0],n[1],n[2],r)}},Kt.gradientStrokeStyle=function(t,e,r,a){var n=this.createGradientStyleFor(t,"line",e,r,a);if(!n)return null;t.strokeStyle=n},Kt.colorStrokeStyle=function(t,e,r,a,n){t.strokeStyle="rgba("+e+","+r+","+a+","+n+")"},Kt.eleStrokeStyle=function(t,e,r){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(t,e,a,r);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(t,n[0],n[1],n[2],r)}},Kt.matchCanvasSize=function(t){var e=this,r=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||t===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,f;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var h=r.canvasContainer;h.style.width=n+"px",h.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)f=r.canvases[c],f.width=l,f.height=u,f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)f=r.bufferCanvases[c],f.width=l,f.height=u,f.style.width=n+"px",f.style.height=i+"px";e.textureMult=1,s<=1&&(f=r.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,f.width=l*e.textureMult,f.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u}},Kt.renderTo=function(t,e,r,a){this.render({forcedContext:t,forcedZoom:e,forcedPan:r,drawAllLayers:!0,forcedPxRatio:a})},Kt.render=function(t){t=t||ps();var e=t.forcedContext,r=t.drawAllLayers,a=t.drawOnlyNodeLayer,n=t.forcedZoom,i=t.forcedPan,s=this,o=t.forcedPxRatio===void 0?this.getPixelRatio():t.forcedPxRatio,l=s.cy,u=s.data,f=u.canvasNeedsRedraw,h=s.textureOnViewport&&!e&&(s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming),c=t.motionBlur!==void 0?t.motionBlur:s.motionBlur,d=s.motionBlurPxRatio,v=l.hasCompoundNodes(),p=s.hoverData.draggingEles,y=!!(s.hoverData.selecting||s.touchData.selecting);c=c&&!e&&s.motionBlurEnabled&&!y;var g=c;e||(s.prevPxRatio!==o&&(s.invalidateContainerClientCoordsCache(),s.matchCanvasSize(s.container),s.redrawHint("eles",!0),s.redrawHint("drag",!0)),s.prevPxRatio=o),!e&&s.motionBlurTimeout&&clearTimeout(s.motionBlurTimeout),c&&(s.mbFrames==null&&(s.mbFrames=0),s.mbFrames++,s.mbFrames<3&&(g=!1),s.mbFrames>s.minMbLowQualFrames&&(s.motionBlurPxRatio=s.mbPxRBlurry)),s.clearingMotionBlur&&(s.motionBlurPxRatio=1),s.textureDrawLastFrame&&!h&&(f[s.NODE]=!0,f[s.SELECT_BOX]=!0);var m=l.style(),b=l.zoom(),E=n!==void 0?n:b,N=l.pan(),A={x:N.x,y:N.y},x={zoom:b,pan:{x:N.x,y:N.y}},I=s.prevViewport,C=I===void 0||x.zoom!==I.zoom||x.pan.x!==I.pan.x||x.pan.y!==I.pan.y;!C&&!(p&&!v)&&(s.motionBlurPxRatio=1),i&&(A=i),E*=o,A.x*=o,A.y*=o;var F=s.getCachedZSortedEles();function z(Re,Ie,Ge,Fe,ke){var ze=Re.globalCompositeOperation;Re.globalCompositeOperation="destination-out",s.colorFillStyle(Re,255,255,255,s.motionBlurTransparency),Re.fillRect(Ie,Ge,Fe,ke),Re.globalCompositeOperation=ze}function M(Re,Ie){var Ge,Fe,ke,ze;!s.clearingMotionBlur&&(Re===u.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]||Re===u.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG])?(Ge={x:N.x*d,y:N.y*d},Fe=b*d,ke=s.canvasWidth*d,ze=s.canvasHeight*d):(Ge=A,Fe=E,ke=s.canvasWidth,ze=s.canvasHeight),Re.setTransform(1,0,0,1,0,0),Ie==="motionBlur"?z(Re,0,0,ke,ze):!e&&(Ie===void 0||Ie)&&Re.clearRect(0,0,ke,ze),r||(Re.translate(Ge.x,Ge.y),Re.scale(Fe,Fe)),i&&Re.translate(i.x,i.y),n&&Re.scale(n,n)}if(h||(s.textureDrawLastFrame=!1),h){if(s.textureDrawLastFrame=!0,!s.textureCache){s.textureCache={},s.textureCache.bb=l.mutableElements().boundingBox(),s.textureCache.texture=s.data.bufferCanvases[s.TEXTURE_BUFFER];var X=s.data.bufferContexts[s.TEXTURE_BUFFER];X.setTransform(1,0,0,1,0,0),X.clearRect(0,0,s.canvasWidth*s.textureMult,s.canvasHeight*s.textureMult),s.render({forcedContext:X,drawOnlyNodeLayer:!0,forcedPxRatio:o*s.textureMult});var x=s.textureCache.viewport={zoom:l.zoom(),pan:l.pan(),width:s.canvasWidth,height:s.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}f[s.DRAG]=!1,f[s.NODE]=!1;var B=u.contexts[s.NODE],re=s.textureCache.texture,x=s.textureCache.viewport;B.setTransform(1,0,0,1,0,0),c?z(B,0,0,x.width,x.height):B.clearRect(0,0,x.width,x.height);var q=m.core("outside-texture-bg-color").value,Z=m.core("outside-texture-bg-opacity").value;s.colorFillStyle(B,q[0],q[1],q[2],Z),B.fillRect(0,0,x.width,x.height);var b=l.zoom();M(B,!1),B.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/o,x.height/x.zoom/o),B.drawImage(re,x.mpan.x,x.mpan.y,x.width/x.zoom/o,x.height/x.zoom/o)}else s.textureOnViewport&&!e&&(s.textureCache=null);var ie=l.extent(),ue=s.pinching||s.hoverData.dragging||s.swipePanning||s.data.wheelZooming||s.hoverData.draggingEles||s.cy.animated(),ge=s.hideEdgesOnViewport&&ue,se=[];if(se[s.NODE]=!f[s.NODE]&&c&&!s.clearedForMotionBlur[s.NODE]||s.clearingMotionBlur,se[s.NODE]&&(s.clearedForMotionBlur[s.NODE]=!0),se[s.DRAG]=!f[s.DRAG]&&c&&!s.clearedForMotionBlur[s.DRAG]||s.clearingMotionBlur,se[s.DRAG]&&(s.clearedForMotionBlur[s.DRAG]=!0),f[s.NODE]||r||a||se[s.NODE]){var ve=c&&!se[s.NODE]&&d!==1,B=e||(ve?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_NODE]:u.contexts[s.NODE]),ye=c&&!ve?"motionBlur":void 0;M(B,ye),ge?s.drawCachedNodes(B,F.nondrag,o,ie):s.drawLayeredElements(B,F.nondrag,o,ie),s.debug&&s.drawDebugPoints(B,F.nondrag),!r&&!c&&(f[s.NODE]=!1)}if(!a&&(f[s.DRAG]||r||se[s.DRAG])){var ve=c&&!se[s.DRAG]&&d!==1,B=e||(ve?s.data.bufferContexts[s.MOTIONBLUR_BUFFER_DRAG]:u.contexts[s.DRAG]);M(B,c&&!ve?"motionBlur":void 0),ge?s.drawCachedNodes(B,F.drag,o,ie):s.drawCachedElements(B,F.drag,o,ie),s.debug&&s.drawDebugPoints(B,F.drag),!r&&!c&&(f[s.DRAG]=!1)}if(s.showFps||!a&&f[s.SELECT_BOX]&&!r){var B=e||u.contexts[s.SELECT_BOX];if(M(B),s.selection[4]==1&&(s.hoverData.selecting||s.touchData.selecting)){var b=s.cy.zoom(),Te=m.core("selection-box-border-width").value/b;B.lineWidth=Te,B.fillStyle="rgba("+m.core("selection-box-color").value[0]+","+m.core("selection-box-color").value[1]+","+m.core("selection-box-color").value[2]+","+m.core("selection-box-opacity").value+")",B.fillRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]),Te>0&&(B.strokeStyle="rgba("+m.core("selection-box-border-color").value[0]+","+m.core("selection-box-border-color").value[1]+","+m.core("selection-box-border-color").value[2]+","+m.core("selection-box-opacity").value+")",B.strokeRect(s.selection[0],s.selection[1],s.selection[2]-s.selection[0],s.selection[3]-s.selection[1]))}if(u.bgActivePosistion&&!s.hoverData.selecting){var b=s.cy.zoom(),be=u.bgActivePosistion;B.fillStyle="rgba("+m.core("active-bg-color").value[0]+","+m.core("active-bg-color").value[1]+","+m.core("active-bg-color").value[2]+","+m.core("active-bg-opacity").value+")",B.beginPath(),B.arc(be.x,be.y,m.core("active-bg-size").pfValue/b,0,2*Math.PI),B.fill()}var me=s.lastRedrawTime;if(s.showFps&&me){me=Math.round(me);var ae=Math.round(1e3/me);B.setTransform(1,0,0,1,0,0),B.fillStyle="rgba(255, 0, 0, 0.75)",B.strokeStyle="rgba(255, 0, 0, 0.75)",B.lineWidth=1,B.fillText("1 frame = "+me+" ms = "+ae+" fps",0,20);var xe=60;B.strokeRect(0,30,250,20),B.fillRect(0,30,250*Math.min(ae/xe,1),20)}r||(f[s.SELECT_BOX]=!1)}if(c&&d!==1){var Ce=u.contexts[s.NODE],Oe=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_NODE],Me=u.contexts[s.DRAG],He=s.data.bufferCanvases[s.MOTIONBLUR_BUFFER_DRAG],We=function(Ie,Ge,Fe){Ie.setTransform(1,0,0,1,0,0),Fe||!g?Ie.clearRect(0,0,s.canvasWidth,s.canvasHeight):z(Ie,0,0,s.canvasWidth,s.canvasHeight);var ke=d;Ie.drawImage(Ge,0,0,s.canvasWidth*ke,s.canvasHeight*ke,0,0,s.canvasWidth,s.canvasHeight)};(f[s.NODE]||se[s.NODE])&&(We(Ce,Oe,se[s.NODE]),f[s.NODE]=!1),(f[s.DRAG]||se[s.DRAG])&&(We(Me,He,se[s.DRAG]),f[s.DRAG]=!1)}s.prevViewport=x,s.clearingMotionBlur&&(s.clearingMotionBlur=!1,s.motionBlurCleared=!0,s.motionBlur=!0),c&&(s.motionBlurTimeout=setTimeout(function(){s.motionBlurTimeout=null,s.clearedForMotionBlur[s.NODE]=!1,s.clearedForMotionBlur[s.DRAG]=!1,s.motionBlur=!1,s.clearingMotionBlur=!h,s.mbFrames=0,f[s.NODE]=!0,f[s.DRAG]=!0,s.redraw()},rp)),e||l.emit("render")};var Ir={};Ir.drawPolygonPath=function(t,e,r,a,n,i){var s=a/2,o=n/2;t.beginPath&&t.beginPath(),t.moveTo(e+s*i[0],r+o*i[1]);for(var l=1;l<i.length/2;l++)t.lineTo(e+s*i[l*2],r+o*i[l*2+1]);t.closePath()},Ir.drawRoundPolygonPath=function(t,e,r,a,n,i){var s=a/2,o=n/2,l=vi(a,n);t.beginPath&&t.beginPath();for(var u=0;u<i.length/4;u++){var f=void 0,h=void 0;u===0?f=i.length-2:f=u*4-2,h=u*4+2;var c=e+s*i[u*4],d=r+o*i[u*4+1],v=-i[f]*i[h]-i[f+1]*i[h+1],p=l/Math.tan(Math.acos(v)/2),y=c-p*i[f],g=d-p*i[f+1],m=c+p*i[h],b=d+p*i[h+1];u===0?t.moveTo(y,g):t.lineTo(y,g),t.arcTo(c,d,m,b,l)}t.closePath()},Ir.drawRoundRectanglePath=function(t,e,r,a,n){var i=a/2,s=n/2,o=za(a,n);t.beginPath&&t.beginPath(),t.moveTo(e,r-s),t.arcTo(e+i,r-s,e+i,r,o),t.arcTo(e+i,r+s,e,r+s,o),t.arcTo(e-i,r+s,e-i,r,o),t.arcTo(e-i,r-s,e,r-s,o),t.lineTo(e,r-s),t.closePath()},Ir.drawBottomRoundRectanglePath=function(t,e,r,a,n){var i=a/2,s=n/2,o=za(a,n);t.beginPath&&t.beginPath(),t.moveTo(e,r-s),t.lineTo(e+i,r-s),t.lineTo(e+i,r),t.arcTo(e+i,r+s,e,r+s,o),t.arcTo(e-i,r+s,e-i,r,o),t.lineTo(e-i,r-s),t.lineTo(e,r-s),t.closePath()},Ir.drawCutRectanglePath=function(t,e,r,a,n){var i=a/2,s=n/2,o=Ss();t.beginPath&&t.beginPath(),t.moveTo(e-i+o,r-s),t.lineTo(e+i-o,r-s),t.lineTo(e+i,r-s+o),t.lineTo(e+i,r+s-o),t.lineTo(e+i-o,r+s),t.lineTo(e-i+o,r+s),t.lineTo(e-i,r+s-o),t.lineTo(e-i,r-s+o),t.closePath()},Ir.drawBarrelPath=function(t,e,r,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=r-s,f=r+s,h=di(a,n),c=h.widthOffset,d=h.heightOffset,v=h.ctrlPtOffsetPct*c;t.beginPath&&t.beginPath(),t.moveTo(o,u+d),t.lineTo(o,f-d),t.quadraticCurveTo(o+v,f,o+c,f),t.lineTo(l-c,f),t.quadraticCurveTo(l-v,f,l,f-d),t.lineTo(l,u+d),t.quadraticCurveTo(l-v,u,l-c,u),t.lineTo(o+c,u),t.quadraticCurveTo(o+v,u,o,u+d),t.closePath()};for(var Cu=Math.sin(0),Du=Math.cos(0),Ui={},Yi={},Su=Math.PI/40,xa=0*Math.PI;xa<2*Math.PI;xa+=Su)Ui[xa]=Math.sin(xa),Yi[xa]=Math.cos(xa);Ir.drawEllipsePath=function(t,e,r,a,n){if(t.beginPath&&t.beginPath(),t.ellipse)t.ellipse(e,r,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=Su)i=e-o*Ui[u]*Cu+o*Yi[u]*Du,s=r+l*Yi[u]*Cu+l*Ui[u]*Du,u===0?t.moveTo(i,s):t.lineTo(i,s);t.closePath()};var rn={};rn.createBuffer=function(t,e){var r=document.createElement("canvas");return r.width=t,r.height=e,[r,r.getContext("2d")]},rn.bufferCanvasImage=function(t){var e=this.cy,r=e.mutableElements(),a=r.boundingBox(),n=this.findContainerClientCoords(),i=t.full?Math.ceil(a.w):n[2],s=t.full?Math.ceil(a.h):n[3],o=R(t.maxWidth)||R(t.maxHeight),l=this.getPixelRatio(),u=1;if(t.scale!==void 0)i*=t.scale,s*=t.scale,u=t.scale;else if(o){var f=1/0,h=1/0;R(t.maxWidth)&&(f=u*t.maxWidth/i),R(t.maxHeight)&&(h=u*t.maxHeight/s),u=Math.min(f,h),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var d=c.getContext("2d");if(i>0&&s>0){d.clearRect(0,0,i,s),d.globalCompositeOperation="source-over";var v=this.getCachedZSortedEles();if(t.full)d.translate(-a.x1*u,-a.y1*u),d.scale(u,u),this.drawElements(d,v),d.scale(1/u,1/u),d.translate(a.x1*u,a.y1*u);else{var p=e.pan(),y={x:p.x*u,y:p.y*u};u*=e.zoom(),d.translate(y.x,y.y),d.scale(u,u),this.drawElements(d,v),d.scale(1/u,1/u),d.translate(-y.x,-y.y)}t.bg&&(d.globalCompositeOperation="destination-over",d.fillStyle=t.bg,d.rect(0,0,i,s),d.fill())}return c};function ap(t,e){for(var r=atob(t),a=new ArrayBuffer(r.length),n=new Uint8Array(a),i=0;i<r.length;i++)n[i]=r.charCodeAt(i);return new Blob([a],{type:e})}function Lu(t){var e=t.indexOf(",");return t.substr(e+1)}function Au(t,e,r){var a=function(){return e.toDataURL(r,t.quality)};switch(t.output){case"blob-promise":return new sa(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},r,t.quality)}catch(s){i(s)}});case"blob":return ap(Lu(a()),r);case"base64":return Lu(a());case"base64uri":default:return a()}}rn.png=function(t){return Au(t,this.bufferCanvasImage(t),"image/png")},rn.jpg=function(t){return Au(t,this.bufferCanvasImage(t),"image/jpeg")};var Ou={};Ou.nodeShapeImpl=function(t,e,r,a,n,i,s){switch(t){case"ellipse":return this.drawEllipsePath(e,r,a,n,i);case"polygon":return this.drawPolygonPath(e,r,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,r,a,n,i,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,r,a,n,i);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,r,a,n,i);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,r,a,n,i);case"barrel":return this.drawBarrelPath(e,r,a,n,i)}};var np=Nu,st=Nu.prototype;st.CANVAS_LAYERS=3,st.SELECT_BOX=0,st.DRAG=1,st.NODE=2,st.BUFFER_COUNT=3,st.TEXTURE_BUFFER=0,st.MOTIONBLUR_BUFFER_NODE=1,st.MOTIONBLUR_BUFFER_DRAG=2;function Nu(t){var e=this;e.data={canvases:new Array(st.CANVAS_LAYERS),contexts:new Array(st.CANVAS_LAYERS),canvasNeedsRedraw:new Array(st.CANVAS_LAYERS),bufferCanvases:new Array(st.BUFFER_COUNT),bufferContexts:new Array(st.CANVAS_LAYERS)};var r="-webkit-tap-highlight-color",a="rgba(0,0,0,0)";e.data.canvasContainer=document.createElement("div");var n=e.data.canvasContainer.style;e.data.canvasContainer.style[r]=a,n.position="relative",n.zIndex="0",n.overflow="hidden";var i=t.cy.container();i.appendChild(e.data.canvasContainer),i.style[r]=a;var s={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};lt()&&(s["-ms-touch-action"]="none",s["touch-action"]="none");for(var o=0;o<st.CANVAS_LAYERS;o++){var l=e.data.canvases[o]=document.createElement("canvas");e.data.contexts[o]=l.getContext("2d"),Object.keys(s).forEach(function(me){l.style[me]=s[me]}),l.style.position="absolute",l.setAttribute("data-id","layer"+o),l.style.zIndex=String(st.CANVAS_LAYERS-o),e.data.canvasContainer.appendChild(l),e.data.canvasNeedsRedraw[o]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[st.NODE].setAttribute("data-id","layer"+st.NODE+"-node"),e.data.canvases[st.SELECT_BOX].setAttribute("data-id","layer"+st.SELECT_BOX+"-selectbox"),e.data.canvases[st.DRAG].setAttribute("data-id","layer"+st.DRAG+"-drag");for(var o=0;o<st.BUFFER_COUNT;o++)e.data.bufferCanvases[o]=document.createElement("canvas"),e.data.bufferContexts[o]=e.data.bufferCanvases[o].getContext("2d"),e.data.bufferCanvases[o].style.position="absolute",e.data.bufferCanvases[o].setAttribute("data-id","buffer"+o),e.data.bufferCanvases[o].style.zIndex=String(-o-1),e.data.bufferCanvases[o].style.visibility="hidden";e.pathsEnabled=!0;var u=Yt(),f=function(ae){return{x:(ae.x1+ae.x2)/2,y:(ae.y1+ae.y2)/2}},h=function(ae){return{x:-ae.w/2,y:-ae.h/2}},c=function(ae){var xe=ae[0]._private,Ce=xe.oldBackgroundTimestamp===xe.backgroundTimestamp;return!Ce},d=function(ae){return ae[0]._private.nodeKey},v=function(ae){return ae[0]._private.labelStyleKey},p=function(ae){return ae[0]._private.sourceLabelStyleKey},y=function(ae){return ae[0]._private.targetLabelStyleKey},g=function(ae,xe,Ce,Oe,Me){return e.drawElement(ae,xe,Ce,!1,!1,Me)},m=function(ae,xe,Ce,Oe,Me){return e.drawElementText(ae,xe,Ce,Oe,"main",Me)},b=function(ae,xe,Ce,Oe,Me){return e.drawElementText(ae,xe,Ce,Oe,"source",Me)},E=function(ae,xe,Ce,Oe,Me){return e.drawElementText(ae,xe,Ce,Oe,"target",Me)},N=function(ae){return ae.boundingBox(),ae[0]._private.bodyBounds},A=function(ae){return ae.boundingBox(),ae[0]._private.labelBounds.main||u},x=function(ae){return ae.boundingBox(),ae[0]._private.labelBounds.source||u},I=function(ae){return ae.boundingBox(),ae[0]._private.labelBounds.target||u},C=function(ae,xe){return xe},F=function(ae){return f(N(ae))},z=function(ae,xe,Ce){var Oe=ae?ae+"-":"";return{x:xe.x+Ce.pstyle(Oe+"text-margin-x").pfValue,y:xe.y+Ce.pstyle(Oe+"text-margin-y").pfValue}},M=function(ae,xe,Ce){var Oe=ae[0]._private.rscratch;return{x:Oe[xe],y:Oe[Ce]}},X=function(ae){return z("",M(ae,"labelX","labelY"),ae)},B=function(ae){return z("source",M(ae,"sourceLabelX","sourceLabelY"),ae)},re=function(ae){return z("target",M(ae,"targetLabelX","targetLabelY"),ae)},q=function(ae){return h(N(ae))},Z=function(ae){return h(x(ae))},ie=function(ae){return h(I(ae))},ue=function(ae){var xe=A(ae),Ce=h(A(ae));if(ae.isNode()){switch(ae.pstyle("text-halign").value){case"left":Ce.x=-xe.w;break;case"right":Ce.x=0;break}switch(ae.pstyle("text-valign").value){case"top":Ce.y=-xe.h;break;case"bottom":Ce.y=0;break}}return Ce},ge=e.data.eleTxrCache=new en(e,{getKey:d,doesEleInvalidateKey:c,drawElement:g,getBoundingBox:N,getRotationPoint:F,getRotationOffset:q,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),se=e.data.lblTxrCache=new en(e,{getKey:v,drawElement:m,getBoundingBox:A,getRotationPoint:X,getRotationOffset:ue,isVisible:C}),ve=e.data.slbTxrCache=new en(e,{getKey:p,drawElement:b,getBoundingBox:x,getRotationPoint:B,getRotationOffset:Z,isVisible:C}),ye=e.data.tlbTxrCache=new en(e,{getKey:y,drawElement:E,getBoundingBox:I,getRotationPoint:re,getRotationOffset:ie,isVisible:C}),Te=e.data.lyrTxrCache=new yu(e);e.onUpdateEleCalcs(function(ae,xe){ge.invalidateElements(xe),se.invalidateElements(xe),ve.invalidateElements(xe),ye.invalidateElements(xe),Te.invalidateElements(xe);for(var Ce=0;Ce<xe.length;Ce++){var Oe=xe[Ce]._private;Oe.oldBackgroundTimestamp=Oe.backgroundTimestamp}});var be=function(ae){for(var xe=0;xe<ae.length;xe++)Te.enqueueElementRefinement(ae[xe].ele)};ge.onDequeue(be),se.onDequeue(be),ve.onDequeue(be),ye.onDequeue(be)}st.redrawHint=function(t,e){var r=this;switch(t){case"eles":r.data.canvasNeedsRedraw[st.NODE]=e;break;case"drag":r.data.canvasNeedsRedraw[st.DRAG]=e;break;case"select":r.data.canvasNeedsRedraw[st.SELECT_BOX]=e;break}};var ip=typeof Path2D<"u";st.path2dEnabled=function(t){if(t===void 0)return this.pathsEnabled;this.pathsEnabled=!!t},st.usePaths=function(){return ip&&this.pathsEnabled},st.setImgSmoothing=function(t,e){t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled=e:(t.webkitImageSmoothingEnabled=e,t.mozImageSmoothingEnabled=e,t.msImageSmoothingEnabled=e)},st.getImgSmoothing=function(t){return t.imageSmoothingEnabled!=null?t.imageSmoothingEnabled:t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled},st.makeOffscreenCanvas=function(t,e){var r;return(typeof OffscreenCanvas>"u"?"undefined":ee(OffscreenCanvas))!=="undefined"?r=new OffscreenCanvas(t,e):(r=document.createElement("canvas"),r.width=t,r.height=e),r},[bu,cr,br,_i,Ur,wa,Kt,Ir,rn,Ou].forEach(function(t){Ue(st,t)});var sp=[{name:"null",impl:tu},{name:"base",impl:vu},{name:"canvas",impl:np}],op=[{type:"layout",extensions:dg},{type:"renderer",extensions:sp}],Mu={},Iu={};function Ru(t,e,r){var a=r,n=function(I){ft("Can not register `"+e+"` for `"+t+"` since `"+I+"` already exists in the prototype and can not be overridden")};if(t==="core"){if(Qa.prototype[e])return n(e);Qa.prototype[e]=r}else if(t==="collection"){if(Nt.prototype[e])return n(e);Nt.prototype[e]=r}else if(t==="layout"){for(var i=function(I){this.options=I,r.call(this,I),L(this._private)||(this._private={}),this._private.cy=I.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(r.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var f=r.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var I=this.animations;if(I)for(var C=0;C<I.length;C++)I[C].stop()}return f?f.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var h=function(I){return I._private.cy},c={addEventFields:function(I,C){C.layout=I,C.cy=h(I),C.target=I},bubble:function(){return!0},parent:function(I){return h(I)}};Ue(s,{createEmitter:function(){return this._private.emitter=new Mn(c,this),this},emitter:function(){return this._private.emitter},on:function(I,C){return this.emitter().on(I,C),this},one:function(I,C){return this.emitter().one(I,C),this},once:function(I,C){return this.emitter().one(I,C),this},removeListener:function(I,C){return this.emitter().removeListener(I,C),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(I,C){return this.emitter().emit(I,C),this}}),ht.eventAliasesOn(s),a=i}else if(t==="renderer"&&e!=="null"&&e!=="base"){var d=ku("renderer","base"),v=d.prototype,p=r,y=r.prototype,g=function(){d.apply(this,arguments),p.apply(this,arguments)},m=g.prototype;for(var b in v){var E=v[b],N=y[b]!=null;if(N)return n(b);m[b]=E}for(var A in y)m[A]=y[A];v.clientFunctions.forEach(function(x){m[x]=m[x]||function(){Tt("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=g}else if(t==="__proto__"||t==="constructor"||t==="prototype")return Tt(t+" is an illegal type to be registered, possibly lead to prototype pollutions");return es({map:Mu,keys:[t,e],value:a})}function ku(t,e){return ts({map:Mu,keys:[t,e]})}function up(t,e,r,a,n){return es({map:Iu,keys:[t,e,r,a],value:n})}function lp(t,e,r,a){return ts({map:Iu,keys:[t,e,r,a]})}var Hi=function(){if(arguments.length===2)return ku.apply(null,arguments);if(arguments.length===3)return Ru.apply(null,arguments);if(arguments.length===4)return lp.apply(null,arguments);if(arguments.length===5)return up.apply(null,arguments);Tt("Invalid extension access syntax")};Qa.prototype.extension=Hi,op.forEach(function(t){t.extensions.forEach(function(e){Ru(t.type,e.name,e.impl)})});var Pu=function t(){if(!(this instanceof t))return new t;this.length=0},Yr=Pu.prototype;Yr.instanceString=function(){return"stylesheet"},Yr.selector=function(t){var e=this.length++;return this[e]={selector:t,properties:[]},this},Yr.css=function(t,e){var r=this.length-1;if(j(t))this[r].properties.push({name:t,value:e});else if(L(t))for(var a=t,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=zt.properties[s]||zt.properties[yt(s)];if(l!=null){var u=l.name,f=o;this[r].properties.push({name:u,value:f})}}}return this},Yr.style=Yr.css,Yr.generateStyle=function(t){var e=new zt(t);return this.appendToStyle(e)},Yr.appendToStyle=function(t){for(var e=0;e<this.length;e++){var r=this[e],a=r.selector,n=r.properties;t.selector(a);for(var i=0;i<n.length;i++){var s=n[i];t.css(s.name,s.value)}}return t};var fp="3.26.0",Hr=function(e){if(e===void 0&&(e={}),L(e))return new Qa(e);if(j(e))return Hi.apply(Hi,arguments)};return Hr.use=function(t){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Hr),t.apply(null,e),this},Hr.warnings=function(t){return ds(t)},Hr.version=fp,Hr.stylesheet=Hr.Stylesheet=Pu,Hr})})(Uu);var Sp=Uu.exports;const Yu=_u(Sp);var Hu={exports:{}},qi={exports:{}},Ki={exports:{}},$u;function Lp(){return $u||($u=1,function(pe,le){(function(ce,H){pe.exports=H()})(Da,function(){return function(ee){var ce={};function H(O){if(ce[O])return ce[O].exports;var T=ce[O]={i:O,l:!1,exports:{}};return ee[O].call(T.exports,T,T.exports,H),T.l=!0,T.exports}return H.m=ee,H.c=ce,H.i=function(O){return O},H.d=function(O,T,w){H.o(O,T)||Object.defineProperty(O,T,{configurable:!1,enumerable:!0,get:w})},H.n=function(O){var T=O&&O.__esModule?function(){return O.default}:function(){return O};return H.d(T,"a",T),T},H.o=function(O,T){return Object.prototype.hasOwnProperty.call(O,T)},H.p="",H(H.s=26)}([function(ee,ce,H){function O(){}O.QUALITY=1,O.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,O.DEFAULT_INCREMENTAL=!1,O.DEFAULT_ANIMATION_ON_LAYOUT=!0,O.DEFAULT_ANIMATION_DURING_LAYOUT=!1,O.DEFAULT_ANIMATION_PERIOD=50,O.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,O.DEFAULT_GRAPH_MARGIN=15,O.NODE_DIMENSIONS_INCLUDE_LABELS=!1,O.SIMPLE_NODE_SIZE=40,O.SIMPLE_NODE_HALF_SIZE=O.SIMPLE_NODE_SIZE/2,O.EMPTY_COMPOUND_NODE_SIZE=40,O.MIN_EDGE_LENGTH=1,O.WORLD_BOUNDARY=1e6,O.INITIAL_WORLD_BOUNDARY=O.WORLD_BOUNDARY/1e3,O.WORLD_CENTER_X=1200,O.WORLD_CENTER_Y=900,ee.exports=O},function(ee,ce,H){var O=H(2),T=H(8),w=H(9);function S(U,P,K){O.call(this,K),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=K,this.bendpoints=[],this.source=U,this.target=P}S.prototype=Object.create(O.prototype);for(var G in O)S[G]=O[G];S.prototype.getSource=function(){return this.source},S.prototype.getTarget=function(){return this.target},S.prototype.isInterGraph=function(){return this.isInterGraph},S.prototype.getLength=function(){return this.length},S.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},S.prototype.getBendpoints=function(){return this.bendpoints},S.prototype.getLca=function(){return this.lca},S.prototype.getSourceInLca=function(){return this.sourceInLca},S.prototype.getTargetInLca=function(){return this.targetInLca},S.prototype.getOtherEnd=function(U){if(this.source===U)return this.target;if(this.target===U)return this.source;throw"Node is not incident with this edge"},S.prototype.getOtherEndInGraph=function(U,P){for(var K=this.getOtherEnd(U),D=P.getGraphManager().getRoot();;){if(K.getOwner()==P)return K;if(K.getOwner()==D)break;K=K.getOwner().getParent()}return null},S.prototype.updateLength=function(){var U=new Array(4);this.isOverlapingSourceAndTarget=T.getIntersection(this.target.getRect(),this.source.getRect(),U),this.isOverlapingSourceAndTarget||(this.lengthX=U[0]-U[2],this.lengthY=U[1]-U[3],Math.abs(this.lengthX)<1&&(this.lengthX=w.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=w.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},S.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=w.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=w.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},ee.exports=S},function(ee,ce,H){function O(T){this.vGraphObject=T}ee.exports=O},function(ee,ce,H){var O=H(2),T=H(10),w=H(13),S=H(0),G=H(16),U=H(4);function P(D,V,_,Q){_==null&&Q==null&&(Q=V),O.call(this,Q),D.graphManager!=null&&(D=D.graphManager),this.estimatedSize=T.MIN_VALUE,this.inclusionTreeDepth=T.MAX_VALUE,this.vGraphObject=Q,this.edges=[],this.graphManager=D,_!=null&&V!=null?this.rect=new w(V.x,V.y,_.width,_.height):this.rect=new w}P.prototype=Object.create(O.prototype);for(var K in O)P[K]=O[K];P.prototype.getEdges=function(){return this.edges},P.prototype.getChild=function(){return this.child},P.prototype.getOwner=function(){return this.owner},P.prototype.getWidth=function(){return this.rect.width},P.prototype.setWidth=function(D){this.rect.width=D},P.prototype.getHeight=function(){return this.rect.height},P.prototype.setHeight=function(D){this.rect.height=D},P.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},P.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},P.prototype.getCenter=function(){return new U(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},P.prototype.getLocation=function(){return new U(this.rect.x,this.rect.y)},P.prototype.getRect=function(){return this.rect},P.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},P.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},P.prototype.setRect=function(D,V){this.rect.x=D.x,this.rect.y=D.y,this.rect.width=V.width,this.rect.height=V.height},P.prototype.setCenter=function(D,V){this.rect.x=D-this.rect.width/2,this.rect.y=V-this.rect.height/2},P.prototype.setLocation=function(D,V){this.rect.x=D,this.rect.y=V},P.prototype.moveBy=function(D,V){this.rect.x+=D,this.rect.y+=V},P.prototype.getEdgeListToNode=function(D){var V=[],_=this;return _.edges.forEach(function(Q){if(Q.target==D){if(Q.source!=_)throw"Incorrect edge source!";V.push(Q)}}),V},P.prototype.getEdgesBetween=function(D){var V=[],_=this;return _.edges.forEach(function(Q){if(!(Q.source==_||Q.target==_))throw"Incorrect edge source and/or target";(Q.target==D||Q.source==D)&&V.push(Q)}),V},P.prototype.getNeighborsList=function(){var D=new Set,V=this;return V.edges.forEach(function(_){if(_.source==V)D.add(_.target);else{if(_.target!=V)throw"Incorrect incidency!";D.add(_.source)}}),D},P.prototype.withChildren=function(){var D=new Set,V,_;if(D.add(this),this.child!=null)for(var Q=this.child.getNodes(),ne=0;ne<Q.length;ne++)V=Q[ne],_=V.withChildren(),_.forEach(function(oe){D.add(oe)});return D},P.prototype.getNoOfChildren=function(){var D=0,V;if(this.child==null)D=1;else for(var _=this.child.getNodes(),Q=0;Q<_.length;Q++)V=_[Q],D+=V.getNoOfChildren();return D==0&&(D=1),D},P.prototype.getEstimatedSize=function(){if(this.estimatedSize==T.MIN_VALUE)throw"assert failed";return this.estimatedSize},P.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},P.prototype.scatter=function(){var D,V,_=-S.INITIAL_WORLD_BOUNDARY,Q=S.INITIAL_WORLD_BOUNDARY;D=S.WORLD_CENTER_X+G.nextDouble()*(Q-_)+_;var ne=-S.INITIAL_WORLD_BOUNDARY,oe=S.INITIAL_WORLD_BOUNDARY;V=S.WORLD_CENTER_Y+G.nextDouble()*(oe-ne)+ne,this.rect.x=D,this.rect.y=V},P.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var D=this.getChild();if(D.updateBounds(!0),this.rect.x=D.getLeft(),this.rect.y=D.getTop(),this.setWidth(D.getRight()-D.getLeft()),this.setHeight(D.getBottom()-D.getTop()),S.NODE_DIMENSIONS_INCLUDE_LABELS){var V=D.getRight()-D.getLeft(),_=D.getBottom()-D.getTop();this.labelWidth>V&&(this.rect.x-=(this.labelWidth-V)/2,this.setWidth(this.labelWidth)),this.labelHeight>_&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-_)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-_),this.setHeight(this.labelHeight))}}},P.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==T.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},P.prototype.transform=function(D){var V=this.rect.x;V>S.WORLD_BOUNDARY?V=S.WORLD_BOUNDARY:V<-S.WORLD_BOUNDARY&&(V=-S.WORLD_BOUNDARY);var _=this.rect.y;_>S.WORLD_BOUNDARY?_=S.WORLD_BOUNDARY:_<-S.WORLD_BOUNDARY&&(_=-S.WORLD_BOUNDARY);var Q=new U(V,_),ne=D.inverseTransformPoint(Q);this.setLocation(ne.x,ne.y)},P.prototype.getLeft=function(){return this.rect.x},P.prototype.getRight=function(){return this.rect.x+this.rect.width},P.prototype.getTop=function(){return this.rect.y},P.prototype.getBottom=function(){return this.rect.y+this.rect.height},P.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},ee.exports=P},function(ee,ce,H){function O(T,w){T==null&&w==null?(this.x=0,this.y=0):(this.x=T,this.y=w)}O.prototype.getX=function(){return this.x},O.prototype.getY=function(){return this.y},O.prototype.setX=function(T){this.x=T},O.prototype.setY=function(T){this.y=T},O.prototype.getDifference=function(T){return new DimensionD(this.x-T.x,this.y-T.y)},O.prototype.getCopy=function(){return new O(this.x,this.y)},O.prototype.translate=function(T){return this.x+=T.width,this.y+=T.height,this},ee.exports=O},function(ee,ce,H){var O=H(2),T=H(10),w=H(0),S=H(6),G=H(3),U=H(1),P=H(13),K=H(12),D=H(11);function V(Q,ne,oe){O.call(this,oe),this.estimatedSize=T.MIN_VALUE,this.margin=w.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=Q,ne!=null&&ne instanceof S?this.graphManager=ne:ne!=null&&ne instanceof Layout&&(this.graphManager=ne.graphManager)}V.prototype=Object.create(O.prototype);for(var _ in O)V[_]=O[_];V.prototype.getNodes=function(){return this.nodes},V.prototype.getEdges=function(){return this.edges},V.prototype.getGraphManager=function(){return this.graphManager},V.prototype.getParent=function(){return this.parent},V.prototype.getLeft=function(){return this.left},V.prototype.getRight=function(){return this.right},V.prototype.getTop=function(){return this.top},V.prototype.getBottom=function(){return this.bottom},V.prototype.isConnected=function(){return this.isConnected},V.prototype.add=function(Q,ne,oe){if(ne==null&&oe==null){var J=Q;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(J)>-1)throw"Node already in graph!";return J.owner=this,this.getNodes().push(J),J}else{var j=Q;if(!(this.getNodes().indexOf(ne)>-1&&this.getNodes().indexOf(oe)>-1))throw"Source or target not in graph!";if(!(ne.owner==oe.owner&&ne.owner==this))throw"Both owners must be this graph!";return ne.owner!=oe.owner?null:(j.source=ne,j.target=oe,j.isInterGraph=!1,this.getEdges().push(j),ne.edges.push(j),oe!=ne&&oe.edges.push(j),j)}},V.prototype.remove=function(Q){var ne=Q;if(Q instanceof G){if(ne==null)throw"Node is null!";if(!(ne.owner!=null&&ne.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var oe=ne.edges.slice(),J,j=oe.length,Y=0;Y<j;Y++)J=oe[Y],J.isInterGraph?this.graphManager.remove(J):J.source.owner.remove(J);var te=this.nodes.indexOf(ne);if(te==-1)throw"Node not in owner node list!";this.nodes.splice(te,1)}else if(Q instanceof U){var J=Q;if(J==null)throw"Edge is null!";if(!(J.source!=null&&J.target!=null))throw"Source and/or target is null!";if(!(J.source.owner!=null&&J.target.owner!=null&&J.source.owner==this&&J.target.owner==this))throw"Source and/or target owner is invalid!";var L=J.source.edges.indexOf(J),$=J.target.edges.indexOf(J);if(!(L>-1&&$>-1))throw"Source and/or target doesn't know this edge!";J.source.edges.splice(L,1),J.target!=J.source&&J.target.edges.splice($,1);var te=J.source.owner.getEdges().indexOf(J);if(te==-1)throw"Not in owner's edge list!";J.source.owner.getEdges().splice(te,1)}},V.prototype.updateLeftTop=function(){for(var Q=T.MAX_VALUE,ne=T.MAX_VALUE,oe,J,j,Y=this.getNodes(),te=Y.length,L=0;L<te;L++){var $=Y[L];oe=$.getTop(),J=$.getLeft(),Q>oe&&(Q=oe),ne>J&&(ne=J)}return Q==T.MAX_VALUE?null:(Y[0].getParent().paddingLeft!=null?j=Y[0].getParent().paddingLeft:j=this.margin,this.left=ne-j,this.top=Q-j,new K(this.left,this.top))},V.prototype.updateBounds=function(Q){for(var ne=T.MAX_VALUE,oe=-T.MAX_VALUE,J=T.MAX_VALUE,j=-T.MAX_VALUE,Y,te,L,$,R,W=this.nodes,fe=W.length,de=0;de<fe;de++){var Ae=W[de];Q&&Ae.child!=null&&Ae.updateBounds(),Y=Ae.getLeft(),te=Ae.getRight(),L=Ae.getTop(),$=Ae.getBottom(),ne>Y&&(ne=Y),oe<te&&(oe=te),J>L&&(J=L),j<$&&(j=$)}var Ne=new P(ne,J,oe-ne,j-J);ne==T.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),W[0].getParent().paddingLeft!=null?R=W[0].getParent().paddingLeft:R=this.margin,this.left=Ne.x-R,this.right=Ne.x+Ne.width+R,this.top=Ne.y-R,this.bottom=Ne.y+Ne.height+R},V.calculateBounds=function(Q){for(var ne=T.MAX_VALUE,oe=-T.MAX_VALUE,J=T.MAX_VALUE,j=-T.MAX_VALUE,Y,te,L,$,R=Q.length,W=0;W<R;W++){var fe=Q[W];Y=fe.getLeft(),te=fe.getRight(),L=fe.getTop(),$=fe.getBottom(),ne>Y&&(ne=Y),oe<te&&(oe=te),J>L&&(J=L),j<$&&(j=$)}var de=new P(ne,J,oe-ne,j-J);return de},V.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},V.prototype.getEstimatedSize=function(){if(this.estimatedSize==T.MIN_VALUE)throw"assert failed";return this.estimatedSize},V.prototype.calcEstimatedSize=function(){for(var Q=0,ne=this.nodes,oe=ne.length,J=0;J<oe;J++){var j=ne[J];Q+=j.calcEstimatedSize()}return Q==0?this.estimatedSize=w.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=Q/Math.sqrt(this.nodes.length),this.estimatedSize},V.prototype.updateConnected=function(){var Q=this;if(this.nodes.length==0){this.isConnected=!0;return}var ne=new D,oe=new Set,J=this.nodes[0],j,Y,te=J.withChildren();for(te.forEach(function(de){ne.push(de),oe.add(de)});ne.length!==0;){J=ne.shift(),j=J.getEdges();for(var L=j.length,$=0;$<L;$++){var R=j[$];if(Y=R.getOtherEndInGraph(J,this),Y!=null&&!oe.has(Y)){var W=Y.withChildren();W.forEach(function(de){ne.push(de),oe.add(de)})}}}if(this.isConnected=!1,oe.size>=this.nodes.length){var fe=0;oe.forEach(function(de){de.owner==Q&&fe++}),fe==this.nodes.length&&(this.isConnected=!0)}},ee.exports=V},function(ee,ce,H){var O,T=H(1);function w(S){O=H(5),this.layout=S,this.graphs=[],this.edges=[]}w.prototype.addRoot=function(){var S=this.layout.newGraph(),G=this.layout.newNode(null),U=this.add(S,G);return this.setRootGraph(U),this.rootGraph},w.prototype.add=function(S,G,U,P,K){if(U==null&&P==null&&K==null){if(S==null)throw"Graph is null!";if(G==null)throw"Parent node is null!";if(this.graphs.indexOf(S)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(S),S.parent!=null)throw"Already has a parent!";if(G.child!=null)throw"Already has a child!";return S.parent=G,G.child=S,S}else{K=U,P=G,U=S;var D=P.getOwner(),V=K.getOwner();if(!(D!=null&&D.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(V!=null&&V.getGraphManager()==this))throw"Target not in this graph mgr!";if(D==V)return U.isInterGraph=!1,D.add(U,P,K);if(U.isInterGraph=!0,U.source=P,U.target=K,this.edges.indexOf(U)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(U),!(U.source!=null&&U.target!=null))throw"Edge source and/or target is null!";if(!(U.source.edges.indexOf(U)==-1&&U.target.edges.indexOf(U)==-1))throw"Edge already in source and/or target incidency list!";return U.source.edges.push(U),U.target.edges.push(U),U}},w.prototype.remove=function(S){if(S instanceof O){var G=S;if(G.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(G==this.rootGraph||G.parent!=null&&G.parent.graphManager==this))throw"Invalid parent node!";var U=[];U=U.concat(G.getEdges());for(var P,K=U.length,D=0;D<K;D++)P=U[D],G.remove(P);var V=[];V=V.concat(G.getNodes());var _;K=V.length;for(var D=0;D<K;D++)_=V[D],G.remove(_);G==this.rootGraph&&this.setRootGraph(null);var Q=this.graphs.indexOf(G);this.graphs.splice(Q,1),G.parent=null}else if(S instanceof T){if(P=S,P==null)throw"Edge is null!";if(!P.isInterGraph)throw"Not an inter-graph edge!";if(!(P.source!=null&&P.target!=null))throw"Source and/or target is null!";if(!(P.source.edges.indexOf(P)!=-1&&P.target.edges.indexOf(P)!=-1))throw"Source and/or target doesn't know this edge!";var Q=P.source.edges.indexOf(P);if(P.source.edges.splice(Q,1),Q=P.target.edges.indexOf(P),P.target.edges.splice(Q,1),!(P.source.owner!=null&&P.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(P.source.owner.getGraphManager().edges.indexOf(P)==-1)throw"Not in owner graph manager's edge list!";var Q=P.source.owner.getGraphManager().edges.indexOf(P);P.source.owner.getGraphManager().edges.splice(Q,1)}},w.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},w.prototype.getGraphs=function(){return this.graphs},w.prototype.getAllNodes=function(){if(this.allNodes==null){for(var S=[],G=this.getGraphs(),U=G.length,P=0;P<U;P++)S=S.concat(G[P].getNodes());this.allNodes=S}return this.allNodes},w.prototype.resetAllNodes=function(){this.allNodes=null},w.prototype.resetAllEdges=function(){this.allEdges=null},w.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},w.prototype.getAllEdges=function(){if(this.allEdges==null){var S=[],G=this.getGraphs();G.length;for(var U=0;U<G.length;U++)S=S.concat(G[U].getEdges());S=S.concat(this.edges),this.allEdges=S}return this.allEdges},w.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},w.prototype.setAllNodesToApplyGravitation=function(S){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=S},w.prototype.getRoot=function(){return this.rootGraph},w.prototype.setRootGraph=function(S){if(S.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=S,S.parent==null&&(S.parent=this.layout.newNode("Root node"))},w.prototype.getLayout=function(){return this.layout},w.prototype.isOneAncestorOfOther=function(S,G){if(!(S!=null&&G!=null))throw"assert failed";if(S==G)return!0;var U=S.getOwner(),P;do{if(P=U.getParent(),P==null)break;if(P==G)return!0;if(U=P.getOwner(),U==null)break}while(!0);U=G.getOwner();do{if(P=U.getParent(),P==null)break;if(P==S)return!0;if(U=P.getOwner(),U==null)break}while(!0);return!1},w.prototype.calcLowestCommonAncestors=function(){for(var S,G,U,P,K,D=this.getAllEdges(),V=D.length,_=0;_<V;_++){if(S=D[_],G=S.source,U=S.target,S.lca=null,S.sourceInLca=G,S.targetInLca=U,G==U){S.lca=G.getOwner();continue}for(P=G.getOwner();S.lca==null;){for(S.targetInLca=U,K=U.getOwner();S.lca==null;){if(K==P){S.lca=K;break}if(K==this.rootGraph)break;if(S.lca!=null)throw"assert failed";S.targetInLca=K.getParent(),K=S.targetInLca.getOwner()}if(P==this.rootGraph)break;S.lca==null&&(S.sourceInLca=P.getParent(),P=S.sourceInLca.getOwner())}if(S.lca==null)throw"assert failed"}},w.prototype.calcLowestCommonAncestor=function(S,G){if(S==G)return S.getOwner();var U=S.getOwner();do{if(U==null)break;var P=G.getOwner();do{if(P==null)break;if(P==U)return P;P=P.getParent().getOwner()}while(!0);U=U.getParent().getOwner()}while(!0);return U},w.prototype.calcInclusionTreeDepths=function(S,G){S==null&&G==null&&(S=this.rootGraph,G=1);for(var U,P=S.getNodes(),K=P.length,D=0;D<K;D++)U=P[D],U.inclusionTreeDepth=G,U.child!=null&&this.calcInclusionTreeDepths(U.child,G+1)},w.prototype.includesInvalidEdge=function(){for(var S,G=this.edges.length,U=0;U<G;U++)if(S=this.edges[U],this.isOneAncestorOfOther(S.source,S.target))return!0;return!1},ee.exports=w},function(ee,ce,H){var O=H(0);function T(){}for(var w in O)T[w]=O[w];T.MAX_ITERATIONS=2500,T.DEFAULT_EDGE_LENGTH=50,T.DEFAULT_SPRING_STRENGTH=.45,T.DEFAULT_REPULSION_STRENGTH=4500,T.DEFAULT_GRAVITY_STRENGTH=.4,T.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,T.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,T.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,T.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,T.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,T.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,T.COOLING_ADAPTATION_FACTOR=.33,T.ADAPTATION_LOWER_NODE_LIMIT=1e3,T.ADAPTATION_UPPER_NODE_LIMIT=5e3,T.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,T.MAX_NODE_DISPLACEMENT=T.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,T.MIN_REPULSION_DIST=T.DEFAULT_EDGE_LENGTH/10,T.CONVERGENCE_CHECK_PERIOD=100,T.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,T.MIN_EDGE_LENGTH=1,T.GRID_CALCULATION_CHECK_PERIOD=10,ee.exports=T},function(ee,ce,H){var O=H(12);function T(){}T.calcSeparationAmount=function(w,S,G,U){if(!w.intersects(S))throw"assert failed";var P=new Array(2);this.decideDirectionsForOverlappingNodes(w,S,P),G[0]=Math.min(w.getRight(),S.getRight())-Math.max(w.x,S.x),G[1]=Math.min(w.getBottom(),S.getBottom())-Math.max(w.y,S.y),w.getX()<=S.getX()&&w.getRight()>=S.getRight()?G[0]+=Math.min(S.getX()-w.getX(),w.getRight()-S.getRight()):S.getX()<=w.getX()&&S.getRight()>=w.getRight()&&(G[0]+=Math.min(w.getX()-S.getX(),S.getRight()-w.getRight())),w.getY()<=S.getY()&&w.getBottom()>=S.getBottom()?G[1]+=Math.min(S.getY()-w.getY(),w.getBottom()-S.getBottom()):S.getY()<=w.getY()&&S.getBottom()>=w.getBottom()&&(G[1]+=Math.min(w.getY()-S.getY(),S.getBottom()-w.getBottom()));var K=Math.abs((S.getCenterY()-w.getCenterY())/(S.getCenterX()-w.getCenterX()));S.getCenterY()===w.getCenterY()&&S.getCenterX()===w.getCenterX()&&(K=1);var D=K*G[0],V=G[1]/K;G[0]<V?V=G[0]:D=G[1],G[0]=-1*P[0]*(V/2+U),G[1]=-1*P[1]*(D/2+U)},T.decideDirectionsForOverlappingNodes=function(w,S,G){w.getCenterX()<S.getCenterX()?G[0]=-1:G[0]=1,w.getCenterY()<S.getCenterY()?G[1]=-1:G[1]=1},T.getIntersection2=function(w,S,G){var U=w.getCenterX(),P=w.getCenterY(),K=S.getCenterX(),D=S.getCenterY();if(w.intersects(S))return G[0]=U,G[1]=P,G[2]=K,G[3]=D,!0;var V=w.getX(),_=w.getY(),Q=w.getRight(),ne=w.getX(),oe=w.getBottom(),J=w.getRight(),j=w.getWidthHalf(),Y=w.getHeightHalf(),te=S.getX(),L=S.getY(),$=S.getRight(),R=S.getX(),W=S.getBottom(),fe=S.getRight(),de=S.getWidthHalf(),Ae=S.getHeightHalf(),Ne=!1,_e=!1;if(U===K){if(P>D)return G[0]=U,G[1]=_,G[2]=K,G[3]=W,!1;if(P<D)return G[0]=U,G[1]=oe,G[2]=K,G[3]=L,!1}else if(P===D){if(U>K)return G[0]=V,G[1]=P,G[2]=$,G[3]=D,!1;if(U<K)return G[0]=Q,G[1]=P,G[2]=te,G[3]=D,!1}else{var tt=w.height/w.width,vt=S.height/S.width,Pe=(D-P)/(K-U),$e=void 0,Xe=void 0,rt=void 0,lt=void 0,at=void 0,et=void 0;if(-tt===Pe?U>K?(G[0]=ne,G[1]=oe,Ne=!0):(G[0]=Q,G[1]=_,Ne=!0):tt===Pe&&(U>K?(G[0]=V,G[1]=_,Ne=!0):(G[0]=J,G[1]=oe,Ne=!0)),-vt===Pe?K>U?(G[2]=R,G[3]=W,_e=!0):(G[2]=$,G[3]=L,_e=!0):vt===Pe&&(K>U?(G[2]=te,G[3]=L,_e=!0):(G[2]=fe,G[3]=W,_e=!0)),Ne&&_e)return!1;if(U>K?P>D?($e=this.getCardinalDirection(tt,Pe,4),Xe=this.getCardinalDirection(vt,Pe,2)):($e=this.getCardinalDirection(-tt,Pe,3),Xe=this.getCardinalDirection(-vt,Pe,1)):P>D?($e=this.getCardinalDirection(-tt,Pe,1),Xe=this.getCardinalDirection(-vt,Pe,3)):($e=this.getCardinalDirection(tt,Pe,2),Xe=this.getCardinalDirection(vt,Pe,4)),!Ne)switch($e){case 1:lt=_,rt=U+-Y/Pe,G[0]=rt,G[1]=lt;break;case 2:rt=J,lt=P+j*Pe,G[0]=rt,G[1]=lt;break;case 3:lt=oe,rt=U+Y/Pe,G[0]=rt,G[1]=lt;break;case 4:rt=ne,lt=P+-j*Pe,G[0]=rt,G[1]=lt;break}if(!_e)switch(Xe){case 1:et=L,at=K+-Ae/Pe,G[2]=at,G[3]=et;break;case 2:at=fe,et=D+de*Pe,G[2]=at,G[3]=et;break;case 3:et=W,at=K+Ae/Pe,G[2]=at,G[3]=et;break;case 4:at=R,et=D+-de*Pe,G[2]=at,G[3]=et;break}}return!1},T.getCardinalDirection=function(w,S,G){return w>S?G:1+G%4},T.getIntersection=function(w,S,G,U){if(U==null)return this.getIntersection2(w,S,G);var P=w.x,K=w.y,D=S.x,V=S.y,_=G.x,Q=G.y,ne=U.x,oe=U.y,J=void 0,j=void 0,Y=void 0,te=void 0,L=void 0,$=void 0,R=void 0,W=void 0,fe=void 0;return Y=V-K,L=P-D,R=D*K-P*V,te=oe-Q,$=_-ne,W=ne*Q-_*oe,fe=Y*$-te*L,fe===0?null:(J=(L*W-$*R)/fe,j=(te*R-Y*W)/fe,new O(J,j))},T.angleOfVector=function(w,S,G,U){var P=void 0;return w!==G?(P=Math.atan((U-S)/(G-w)),G<w?P+=Math.PI:U<S&&(P+=this.TWO_PI)):U<S?P=this.ONE_AND_HALF_PI:P=this.HALF_PI,P},T.doIntersect=function(w,S,G,U){var P=w.x,K=w.y,D=S.x,V=S.y,_=G.x,Q=G.y,ne=U.x,oe=U.y,J=(D-P)*(oe-Q)-(ne-_)*(V-K);if(J===0)return!1;var j=((oe-Q)*(ne-P)+(_-ne)*(oe-K))/J,Y=((K-V)*(ne-P)+(D-P)*(oe-K))/J;return 0<j&&j<1&&0<Y&&Y<1},T.HALF_PI=.5*Math.PI,T.ONE_AND_HALF_PI=1.5*Math.PI,T.TWO_PI=2*Math.PI,T.THREE_PI=3*Math.PI,ee.exports=T},function(ee,ce,H){function O(){}O.sign=function(T){return T>0?1:T<0?-1:0},O.floor=function(T){return T<0?Math.ceil(T):Math.floor(T)},O.ceil=function(T){return T<0?Math.floor(T):Math.ceil(T)},ee.exports=O},function(ee,ce,H){function O(){}O.MAX_VALUE=2147483647,O.MIN_VALUE=-2147483648,ee.exports=O},function(ee,ce,H){var O=function(){function P(K,D){for(var V=0;V<D.length;V++){var _=D[V];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(K,_.key,_)}}return function(K,D,V){return D&&P(K.prototype,D),V&&P(K,V),K}}();function T(P,K){if(!(P instanceof K))throw new TypeError("Cannot call a class as a function")}var w=function(K){return{value:K,next:null,prev:null}},S=function(K,D,V,_){return K!==null?K.next=D:_.head=D,V!==null?V.prev=D:_.tail=D,D.prev=K,D.next=V,_.length++,D},G=function(K,D){var V=K.prev,_=K.next;return V!==null?V.next=_:D.head=_,_!==null?_.prev=V:D.tail=V,K.prev=K.next=null,D.length--,K},U=function(){function P(K){var D=this;T(this,P),this.length=0,this.head=null,this.tail=null,K!=null&&K.forEach(function(V){return D.push(V)})}return O(P,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(D,V){return S(V.prev,w(D),V,this)}},{key:"insertAfter",value:function(D,V){return S(V,w(D),V.next,this)}},{key:"insertNodeBefore",value:function(D,V){return S(V.prev,D,V,this)}},{key:"insertNodeAfter",value:function(D,V){return S(V,D,V.next,this)}},{key:"push",value:function(D){return S(this.tail,w(D),null,this)}},{key:"unshift",value:function(D){return S(null,w(D),this.head,this)}},{key:"remove",value:function(D){return G(D,this)}},{key:"pop",value:function(){return G(this.tail,this).value}},{key:"popNode",value:function(){return G(this.tail,this)}},{key:"shift",value:function(){return G(this.head,this).value}},{key:"shiftNode",value:function(){return G(this.head,this)}},{key:"get_object_at",value:function(D){if(D<=this.length()){for(var V=1,_=this.head;V<D;)_=_.next,V++;return _.value}}},{key:"set_object_at",value:function(D,V){if(D<=this.length()){for(var _=1,Q=this.head;_<D;)Q=Q.next,_++;Q.value=V}}}]),P}();ee.exports=U},function(ee,ce,H){function O(T,w,S){this.x=null,this.y=null,T==null&&w==null&&S==null?(this.x=0,this.y=0):typeof T=="number"&&typeof w=="number"&&S==null?(this.x=T,this.y=w):T.constructor.name=="Point"&&w==null&&S==null&&(S=T,this.x=S.x,this.y=S.y)}O.prototype.getX=function(){return this.x},O.prototype.getY=function(){return this.y},O.prototype.getLocation=function(){return new O(this.x,this.y)},O.prototype.setLocation=function(T,w,S){T.constructor.name=="Point"&&w==null&&S==null?(S=T,this.setLocation(S.x,S.y)):typeof T=="number"&&typeof w=="number"&&S==null&&(parseInt(T)==T&&parseInt(w)==w?this.move(T,w):(this.x=Math.floor(T+.5),this.y=Math.floor(w+.5)))},O.prototype.move=function(T,w){this.x=T,this.y=w},O.prototype.translate=function(T,w){this.x+=T,this.y+=w},O.prototype.equals=function(T){if(T.constructor.name=="Point"){var w=T;return this.x==w.x&&this.y==w.y}return this==T},O.prototype.toString=function(){return new O().constructor.name+"[x="+this.x+",y="+this.y+"]"},ee.exports=O},function(ee,ce,H){function O(T,w,S,G){this.x=0,this.y=0,this.width=0,this.height=0,T!=null&&w!=null&&S!=null&&G!=null&&(this.x=T,this.y=w,this.width=S,this.height=G)}O.prototype.getX=function(){return this.x},O.prototype.setX=function(T){this.x=T},O.prototype.getY=function(){return this.y},O.prototype.setY=function(T){this.y=T},O.prototype.getWidth=function(){return this.width},O.prototype.setWidth=function(T){this.width=T},O.prototype.getHeight=function(){return this.height},O.prototype.setHeight=function(T){this.height=T},O.prototype.getRight=function(){return this.x+this.width},O.prototype.getBottom=function(){return this.y+this.height},O.prototype.intersects=function(T){return!(this.getRight()<T.x||this.getBottom()<T.y||T.getRight()<this.x||T.getBottom()<this.y)},O.prototype.getCenterX=function(){return this.x+this.width/2},O.prototype.getMinX=function(){return this.getX()},O.prototype.getMaxX=function(){return this.getX()+this.width},O.prototype.getCenterY=function(){return this.y+this.height/2},O.prototype.getMinY=function(){return this.getY()},O.prototype.getMaxY=function(){return this.getY()+this.height},O.prototype.getWidthHalf=function(){return this.width/2},O.prototype.getHeightHalf=function(){return this.height/2},ee.exports=O},function(ee,ce,H){var O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(w){return typeof w}:function(w){return w&&typeof Symbol=="function"&&w.constructor===Symbol&&w!==Symbol.prototype?"symbol":typeof w};function T(){}T.lastID=0,T.createID=function(w){return T.isPrimitive(w)?w:(w.uniqueID!=null||(w.uniqueID=T.getString(),T.lastID++),w.uniqueID)},T.getString=function(w){return w==null&&(w=T.lastID),"Object#"+w},T.isPrimitive=function(w){var S=typeof w>"u"?"undefined":O(w);return w==null||S!="object"&&S!="function"},ee.exports=T},function(ee,ce,H){function O(_){if(Array.isArray(_)){for(var Q=0,ne=Array(_.length);Q<_.length;Q++)ne[Q]=_[Q];return ne}else return Array.from(_)}var T=H(0),w=H(6),S=H(3),G=H(1),U=H(5),P=H(4),K=H(17),D=H(27);function V(_){D.call(this),this.layoutQuality=T.QUALITY,this.createBendsAsNeeded=T.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=T.DEFAULT_INCREMENTAL,this.animationOnLayout=T.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=T.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=T.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=T.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new w(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,_!=null&&(this.isRemoteUse=_)}V.RANDOM_SEED=1,V.prototype=Object.create(D.prototype),V.prototype.getGraphManager=function(){return this.graphManager},V.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},V.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},V.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},V.prototype.newGraphManager=function(){var _=new w(this);return this.graphManager=_,_},V.prototype.newGraph=function(_){return new U(null,this.graphManager,_)},V.prototype.newNode=function(_){return new S(this.graphManager,_)},V.prototype.newEdge=function(_){return new G(null,null,_)},V.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},V.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var _;return this.checkLayoutSuccess()?_=!1:_=this.layout(),T.ANIMATE==="during"?!1:(_&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,_)},V.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},V.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var _=this.graphManager.getAllEdges(),Q=0;Q<_.length;Q++)_[Q];for(var ne=this.graphManager.getRoot().getNodes(),Q=0;Q<ne.length;Q++)ne[Q];this.update(this.graphManager.getRoot())}},V.prototype.update=function(_){if(_==null)this.update2();else if(_ instanceof S){var Q=_;if(Q.getChild()!=null)for(var ne=Q.getChild().getNodes(),oe=0;oe<ne.length;oe++)update(ne[oe]);if(Q.vGraphObject!=null){var J=Q.vGraphObject;J.update(Q)}}else if(_ instanceof G){var j=_;if(j.vGraphObject!=null){var Y=j.vGraphObject;Y.update(j)}}else if(_ instanceof U){var te=_;if(te.vGraphObject!=null){var L=te.vGraphObject;L.update(te)}}},V.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=T.QUALITY,this.animationDuringLayout=T.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=T.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=T.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=T.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=T.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=T.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},V.prototype.transform=function(_){if(_==null)this.transform(new P(0,0));else{var Q=new K,ne=this.graphManager.getRoot().updateLeftTop();if(ne!=null){Q.setWorldOrgX(_.x),Q.setWorldOrgY(_.y),Q.setDeviceOrgX(ne.x),Q.setDeviceOrgY(ne.y);for(var oe=this.getAllNodes(),J,j=0;j<oe.length;j++)J=oe[j],J.transform(Q)}}},V.prototype.positionNodesRandomly=function(_){if(_==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var Q,ne,oe=_.getNodes(),J=0;J<oe.length;J++)Q=oe[J],ne=Q.getChild(),ne==null||ne.getNodes().length==0?Q.scatter():(this.positionNodesRandomly(ne),Q.updateBounds())},V.prototype.getFlatForest=function(){for(var _=[],Q=!0,ne=this.graphManager.getRoot().getNodes(),oe=!0,J=0;J<ne.length;J++)ne[J].getChild()!=null&&(oe=!1);if(!oe)return _;var j=new Set,Y=[],te=new Map,L=[];for(L=L.concat(ne);L.length>0&&Q;){for(Y.push(L[0]);Y.length>0&&Q;){var $=Y[0];Y.splice(0,1),j.add($);for(var R=$.getEdges(),J=0;J<R.length;J++){var W=R[J].getOtherEnd($);if(te.get($)!=W)if(!j.has(W))Y.push(W),te.set(W,$);else{Q=!1;break}}}if(!Q)_=[];else{var fe=[].concat(O(j));_.push(fe);for(var J=0;J<fe.length;J++){var de=fe[J],Ae=L.indexOf(de);Ae>-1&&L.splice(Ae,1)}j=new Set,te=new Map}}return _},V.prototype.createDummyNodesForBendpoints=function(_){for(var Q=[],ne=_.source,oe=this.graphManager.calcLowestCommonAncestor(_.source,_.target),J=0;J<_.bendpoints.length;J++){var j=this.newNode(null);j.setRect(new Point(0,0),new Dimension(1,1)),oe.add(j);var Y=this.newEdge(null);this.graphManager.add(Y,ne,j),Q.add(j),ne=j}var Y=this.newEdge(null);return this.graphManager.add(Y,ne,_.target),this.edgeToDummyNodes.set(_,Q),_.isInterGraph()?this.graphManager.remove(_):oe.remove(_),Q},V.prototype.createBendpointsFromDummyNodes=function(){var _=[];_=_.concat(this.graphManager.getAllEdges()),_=[].concat(O(this.edgeToDummyNodes.keys())).concat(_);for(var Q=0;Q<_.length;Q++){var ne=_[Q];if(ne.bendpoints.length>0){for(var oe=this.edgeToDummyNodes.get(ne),J=0;J<oe.length;J++){var j=oe[J],Y=new P(j.getCenterX(),j.getCenterY()),te=ne.bendpoints.get(J);te.x=Y.x,te.y=Y.y,j.getOwner().remove(j)}this.graphManager.add(ne,ne.source,ne.target)}}},V.transform=function(_,Q,ne,oe){if(ne!=null&&oe!=null){var J=Q;if(_<=50){var j=Q/ne;J-=(Q-j)/50*(50-_)}else{var Y=Q*oe;J+=(Y-Q)/50*(_-50)}return J}else{var te,L;return _<=50?(te=9*Q/500,L=Q/10):(te=9*Q/50,L=-8*Q),te*_+L}},V.findCenterOfTree=function(_){var Q=[];Q=Q.concat(_);var ne=[],oe=new Map,J=!1,j=null;(Q.length==1||Q.length==2)&&(J=!0,j=Q[0]);for(var Y=0;Y<Q.length;Y++){var te=Q[Y],L=te.getNeighborsList().size;oe.set(te,te.getNeighborsList().size),L==1&&ne.push(te)}var $=[];for($=$.concat(ne);!J;){var R=[];R=R.concat($),$=[];for(var Y=0;Y<Q.length;Y++){var te=Q[Y],W=Q.indexOf(te);W>=0&&Q.splice(W,1);var fe=te.getNeighborsList();fe.forEach(function(Ne){if(ne.indexOf(Ne)<0){var _e=oe.get(Ne),tt=_e-1;tt==1&&$.push(Ne),oe.set(Ne,tt)}})}ne=ne.concat($),(Q.length==1||Q.length==2)&&(J=!0,j=Q[0])}return j},V.prototype.setGraphManager=function(_){this.graphManager=_},ee.exports=V},function(ee,ce,H){function O(){}O.seed=1,O.x=0,O.nextDouble=function(){return O.x=Math.sin(O.seed++)*1e4,O.x-Math.floor(O.x)},ee.exports=O},function(ee,ce,H){var O=H(4);function T(w,S){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}T.prototype.getWorldOrgX=function(){return this.lworldOrgX},T.prototype.setWorldOrgX=function(w){this.lworldOrgX=w},T.prototype.getWorldOrgY=function(){return this.lworldOrgY},T.prototype.setWorldOrgY=function(w){this.lworldOrgY=w},T.prototype.getWorldExtX=function(){return this.lworldExtX},T.prototype.setWorldExtX=function(w){this.lworldExtX=w},T.prototype.getWorldExtY=function(){return this.lworldExtY},T.prototype.setWorldExtY=function(w){this.lworldExtY=w},T.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},T.prototype.setDeviceOrgX=function(w){this.ldeviceOrgX=w},T.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},T.prototype.setDeviceOrgY=function(w){this.ldeviceOrgY=w},T.prototype.getDeviceExtX=function(){return this.ldeviceExtX},T.prototype.setDeviceExtX=function(w){this.ldeviceExtX=w},T.prototype.getDeviceExtY=function(){return this.ldeviceExtY},T.prototype.setDeviceExtY=function(w){this.ldeviceExtY=w},T.prototype.transformX=function(w){var S=0,G=this.lworldExtX;return G!=0&&(S=this.ldeviceOrgX+(w-this.lworldOrgX)*this.ldeviceExtX/G),S},T.prototype.transformY=function(w){var S=0,G=this.lworldExtY;return G!=0&&(S=this.ldeviceOrgY+(w-this.lworldOrgY)*this.ldeviceExtY/G),S},T.prototype.inverseTransformX=function(w){var S=0,G=this.ldeviceExtX;return G!=0&&(S=this.lworldOrgX+(w-this.ldeviceOrgX)*this.lworldExtX/G),S},T.prototype.inverseTransformY=function(w){var S=0,G=this.ldeviceExtY;return G!=0&&(S=this.lworldOrgY+(w-this.ldeviceOrgY)*this.lworldExtY/G),S},T.prototype.inverseTransformPoint=function(w){var S=new O(this.inverseTransformX(w.x),this.inverseTransformY(w.y));return S},ee.exports=T},function(ee,ce,H){function O(D){if(Array.isArray(D)){for(var V=0,_=Array(D.length);V<D.length;V++)_[V]=D[V];return _}else return Array.from(D)}var T=H(15),w=H(7),S=H(0),G=H(8),U=H(9);function P(){T.call(this),this.useSmartIdealEdgeLengthCalculation=w.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=w.DEFAULT_EDGE_LENGTH,this.springConstant=w.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=w.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=w.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=w.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=w.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=w.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*w.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=w.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=w.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=w.MAX_ITERATIONS}P.prototype=Object.create(T.prototype);for(var K in T)P[K]=T[K];P.prototype.initParameters=function(){T.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=w.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},P.prototype.calcIdealEdgeLengths=function(){for(var D,V,_,Q,ne,oe,J=this.getGraphManager().getAllEdges(),j=0;j<J.length;j++)D=J[j],D.idealLength=this.idealEdgeLength,D.isInterGraph&&(_=D.getSource(),Q=D.getTarget(),ne=D.getSourceInLca().getEstimatedSize(),oe=D.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(D.idealLength+=ne+oe-2*S.SIMPLE_NODE_SIZE),V=D.getLca().getInclusionTreeDepth(),D.idealLength+=w.DEFAULT_EDGE_LENGTH*w.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(_.getInclusionTreeDepth()+Q.getInclusionTreeDepth()-2*V))},P.prototype.initSpringEmbedder=function(){var D=this.getAllNodes().length;this.incremental?(D>w.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*w.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(D-w.ADAPTATION_LOWER_NODE_LIMIT)/(w.ADAPTATION_UPPER_NODE_LIMIT-w.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-w.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=w.MAX_NODE_DISPLACEMENT_INCREMENTAL):(D>w.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(w.COOLING_ADAPTATION_FACTOR,1-(D-w.ADAPTATION_LOWER_NODE_LIMIT)/(w.ADAPTATION_UPPER_NODE_LIMIT-w.ADAPTATION_LOWER_NODE_LIMIT)*(1-w.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=w.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},P.prototype.calcSpringForces=function(){for(var D=this.getAllEdges(),V,_=0;_<D.length;_++)V=D[_],this.calcSpringForce(V,V.idealLength)},P.prototype.calcRepulsionForces=function(){var D=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,_,Q,ne,oe,J=this.getAllNodes(),j;if(this.useFRGridVariant)for(this.totalIterations%w.GRID_CALCULATION_CHECK_PERIOD==1&&D&&this.updateGrid(),j=new Set,_=0;_<J.length;_++)ne=J[_],this.calculateRepulsionForceOfANode(ne,j,D,V),j.add(ne);else for(_=0;_<J.length;_++)for(ne=J[_],Q=_+1;Q<J.length;Q++)oe=J[Q],ne.getOwner()==oe.getOwner()&&this.calcRepulsionForce(ne,oe)},P.prototype.calcGravitationalForces=function(){for(var D,V=this.getAllNodesToApplyGravitation(),_=0;_<V.length;_++)D=V[_],this.calcGravitationalForce(D)},P.prototype.moveNodes=function(){for(var D=this.getAllNodes(),V,_=0;_<D.length;_++)V=D[_],V.move()},P.prototype.calcSpringForce=function(D,V){var _=D.getSource(),Q=D.getTarget(),ne,oe,J,j;if(this.uniformLeafNodeSizes&&_.getChild()==null&&Q.getChild()==null)D.updateLengthSimple();else if(D.updateLength(),D.isOverlapingSourceAndTarget)return;ne=D.getLength(),ne!=0&&(oe=this.springConstant*(ne-V),J=oe*(D.lengthX/ne),j=oe*(D.lengthY/ne),_.springForceX+=J,_.springForceY+=j,Q.springForceX-=J,Q.springForceY-=j)},P.prototype.calcRepulsionForce=function(D,V){var _=D.getRect(),Q=V.getRect(),ne=new Array(2),oe=new Array(4),J,j,Y,te,L,$,R;if(_.intersects(Q)){G.calcSeparationAmount(_,Q,ne,w.DEFAULT_EDGE_LENGTH/2),$=2*ne[0],R=2*ne[1];var W=D.noOfChildren*V.noOfChildren/(D.noOfChildren+V.noOfChildren);D.repulsionForceX-=W*$,D.repulsionForceY-=W*R,V.repulsionForceX+=W*$,V.repulsionForceY+=W*R}else this.uniformLeafNodeSizes&&D.getChild()==null&&V.getChild()==null?(J=Q.getCenterX()-_.getCenterX(),j=Q.getCenterY()-_.getCenterY()):(G.getIntersection(_,Q,oe),J=oe[2]-oe[0],j=oe[3]-oe[1]),Math.abs(J)<w.MIN_REPULSION_DIST&&(J=U.sign(J)*w.MIN_REPULSION_DIST),Math.abs(j)<w.MIN_REPULSION_DIST&&(j=U.sign(j)*w.MIN_REPULSION_DIST),Y=J*J+j*j,te=Math.sqrt(Y),L=this.repulsionConstant*D.noOfChildren*V.noOfChildren/Y,$=L*J/te,R=L*j/te,D.repulsionForceX-=$,D.repulsionForceY-=R,V.repulsionForceX+=$,V.repulsionForceY+=R},P.prototype.calcGravitationalForce=function(D){var V,_,Q,ne,oe,J,j,Y;V=D.getOwner(),_=(V.getRight()+V.getLeft())/2,Q=(V.getTop()+V.getBottom())/2,ne=D.getCenterX()-_,oe=D.getCenterY()-Q,J=Math.abs(ne)+D.getWidth()/2,j=Math.abs(oe)+D.getHeight()/2,D.getOwner()==this.graphManager.getRoot()?(Y=V.getEstimatedSize()*this.gravityRangeFactor,(J>Y||j>Y)&&(D.gravitationForceX=-this.gravityConstant*ne,D.gravitationForceY=-this.gravityConstant*oe)):(Y=V.getEstimatedSize()*this.compoundGravityRangeFactor,(J>Y||j>Y)&&(D.gravitationForceX=-this.gravityConstant*ne*this.compoundGravityConstant,D.gravitationForceY=-this.gravityConstant*oe*this.compoundGravityConstant))},P.prototype.isConverged=function(){var D,V=!1;return this.totalIterations>this.maxIterations/3&&(V=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),D=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,D||V},P.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},P.prototype.calcNoOfChildrenForAllNodes=function(){for(var D,V=this.graphManager.getAllNodes(),_=0;_<V.length;_++)D=V[_],D.noOfChildren=D.getNoOfChildren()},P.prototype.calcGrid=function(D){var V=0,_=0;V=parseInt(Math.ceil((D.getRight()-D.getLeft())/this.repulsionRange)),_=parseInt(Math.ceil((D.getBottom()-D.getTop())/this.repulsionRange));for(var Q=new Array(V),ne=0;ne<V;ne++)Q[ne]=new Array(_);for(var ne=0;ne<V;ne++)for(var oe=0;oe<_;oe++)Q[ne][oe]=new Array;return Q},P.prototype.addNodeToGrid=function(D,V,_){var Q=0,ne=0,oe=0,J=0;Q=parseInt(Math.floor((D.getRect().x-V)/this.repulsionRange)),ne=parseInt(Math.floor((D.getRect().width+D.getRect().x-V)/this.repulsionRange)),oe=parseInt(Math.floor((D.getRect().y-_)/this.repulsionRange)),J=parseInt(Math.floor((D.getRect().height+D.getRect().y-_)/this.repulsionRange));for(var j=Q;j<=ne;j++)for(var Y=oe;Y<=J;Y++)this.grid[j][Y].push(D),D.setGridCoordinates(Q,ne,oe,J)},P.prototype.updateGrid=function(){var D,V,_=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),D=0;D<_.length;D++)V=_[D],this.addNodeToGrid(V,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},P.prototype.calculateRepulsionForceOfANode=function(D,V,_,Q){if(this.totalIterations%w.GRID_CALCULATION_CHECK_PERIOD==1&&_||Q){var ne=new Set;D.surrounding=new Array;for(var oe,J=this.grid,j=D.startX-1;j<D.finishX+2;j++)for(var Y=D.startY-1;Y<D.finishY+2;Y++)if(!(j<0||Y<0||j>=J.length||Y>=J[0].length)){for(var te=0;te<J[j][Y].length;te++)if(oe=J[j][Y][te],!(D.getOwner()!=oe.getOwner()||D==oe)&&!V.has(oe)&&!ne.has(oe)){var L=Math.abs(D.getCenterX()-oe.getCenterX())-(D.getWidth()/2+oe.getWidth()/2),$=Math.abs(D.getCenterY()-oe.getCenterY())-(D.getHeight()/2+oe.getHeight()/2);L<=this.repulsionRange&&$<=this.repulsionRange&&ne.add(oe)}}D.surrounding=[].concat(O(ne))}for(j=0;j<D.surrounding.length;j++)this.calcRepulsionForce(D,D.surrounding[j])},P.prototype.calcRepulsionRange=function(){return 0},ee.exports=P},function(ee,ce,H){var O=H(1),T=H(7);function w(G,U,P){O.call(this,G,U,P),this.idealLength=T.DEFAULT_EDGE_LENGTH}w.prototype=Object.create(O.prototype);for(var S in O)w[S]=O[S];ee.exports=w},function(ee,ce,H){var O=H(3);function T(S,G,U,P){O.call(this,S,G,U,P),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}T.prototype=Object.create(O.prototype);for(var w in O)T[w]=O[w];T.prototype.setGridCoordinates=function(S,G,U,P){this.startX=S,this.finishX=G,this.startY=U,this.finishY=P},ee.exports=T},function(ee,ce,H){function O(T,w){this.width=0,this.height=0,T!==null&&w!==null&&(this.height=w,this.width=T)}O.prototype.getWidth=function(){return this.width},O.prototype.setWidth=function(T){this.width=T},O.prototype.getHeight=function(){return this.height},O.prototype.setHeight=function(T){this.height=T},ee.exports=O},function(ee,ce,H){var O=H(14);function T(){this.map={},this.keys=[]}T.prototype.put=function(w,S){var G=O.createID(w);this.contains(G)||(this.map[G]=S,this.keys.push(w))},T.prototype.contains=function(w){return O.createID(w),this.map[w]!=null},T.prototype.get=function(w){var S=O.createID(w);return this.map[S]},T.prototype.keySet=function(){return this.keys},ee.exports=T},function(ee,ce,H){var O=H(14);function T(){this.set={}}T.prototype.add=function(w){var S=O.createID(w);this.contains(S)||(this.set[S]=w)},T.prototype.remove=function(w){delete this.set[O.createID(w)]},T.prototype.clear=function(){this.set={}},T.prototype.contains=function(w){return this.set[O.createID(w)]==w},T.prototype.isEmpty=function(){return this.size()===0},T.prototype.size=function(){return Object.keys(this.set).length},T.prototype.addAllTo=function(w){for(var S=Object.keys(this.set),G=S.length,U=0;U<G;U++)w.push(this.set[S[U]])},T.prototype.size=function(){return Object.keys(this.set).length},T.prototype.addAll=function(w){for(var S=w.length,G=0;G<S;G++){var U=w[G];this.add(U)}},ee.exports=T},function(ee,ce,H){var O=function(){function G(U,P){for(var K=0;K<P.length;K++){var D=P[K];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(U,D.key,D)}}return function(U,P,K){return P&&G(U.prototype,P),K&&G(U,K),U}}();function T(G,U){if(!(G instanceof U))throw new TypeError("Cannot call a class as a function")}var w=H(11),S=function(){function G(U,P){T(this,G),(P!==null||P!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var K=void 0;U instanceof w?K=U.size():K=U.length,this._quicksort(U,0,K-1)}return O(G,[{key:"_quicksort",value:function(P,K,D){if(K<D){var V=this._partition(P,K,D);this._quicksort(P,K,V),this._quicksort(P,V+1,D)}}},{key:"_partition",value:function(P,K,D){for(var V=this._get(P,K),_=K,Q=D;;){for(;this.compareFunction(V,this._get(P,Q));)Q--;for(;this.compareFunction(this._get(P,_),V);)_++;if(_<Q)this._swap(P,_,Q),_++,Q--;else return Q}}},{key:"_get",value:function(P,K){return P instanceof w?P.get_object_at(K):P[K]}},{key:"_set",value:function(P,K,D){P instanceof w?P.set_object_at(K,D):P[K]=D}},{key:"_swap",value:function(P,K,D){var V=this._get(P,K);this._set(P,K,this._get(P,D)),this._set(P,D,V)}},{key:"_defaultCompareFunction",value:function(P,K){return K>P}}]),G}();ee.exports=S},function(ee,ce,H){var O=function(){function S(G,U){for(var P=0;P<U.length;P++){var K=U[P];K.enumerable=K.enumerable||!1,K.configurable=!0,"value"in K&&(K.writable=!0),Object.defineProperty(G,K.key,K)}}return function(G,U,P){return U&&S(G.prototype,U),P&&S(G,P),G}}();function T(S,G){if(!(S instanceof G))throw new TypeError("Cannot call a class as a function")}var w=function(){function S(G,U){var P=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,K=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,D=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;T(this,S),this.sequence1=G,this.sequence2=U,this.match_score=P,this.mismatch_penalty=K,this.gap_penalty=D,this.iMax=G.length+1,this.jMax=U.length+1,this.grid=new Array(this.iMax);for(var V=0;V<this.iMax;V++){this.grid[V]=new Array(this.jMax);for(var _=0;_<this.jMax;_++)this.grid[V][_]=0}this.tracebackGrid=new Array(this.iMax);for(var Q=0;Q<this.iMax;Q++){this.tracebackGrid[Q]=new Array(this.jMax);for(var ne=0;ne<this.jMax;ne++)this.tracebackGrid[Q][ne]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return O(S,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var U=1;U<this.jMax;U++)this.grid[0][U]=this.grid[0][U-1]+this.gap_penalty,this.tracebackGrid[0][U]=[!1,!1,!0];for(var P=1;P<this.iMax;P++)this.grid[P][0]=this.grid[P-1][0]+this.gap_penalty,this.tracebackGrid[P][0]=[!1,!0,!1];for(var K=1;K<this.iMax;K++)for(var D=1;D<this.jMax;D++){var V=void 0;this.sequence1[K-1]===this.sequence2[D-1]?V=this.grid[K-1][D-1]+this.match_score:V=this.grid[K-1][D-1]+this.mismatch_penalty;var _=this.grid[K-1][D]+this.gap_penalty,Q=this.grid[K][D-1]+this.gap_penalty,ne=[V,_,Q],oe=this.arrayAllMaxIndexes(ne);this.grid[K][D]=ne[oe[0]],this.tracebackGrid[K][D]=[oe.includes(0),oe.includes(1),oe.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var U=[];for(U.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});U[0];){var P=U[0],K=this.tracebackGrid[P.pos[0]][P.pos[1]];K[0]&&U.push({pos:[P.pos[0]-1,P.pos[1]-1],seq1:this.sequence1[P.pos[0]-1]+P.seq1,seq2:this.sequence2[P.pos[1]-1]+P.seq2}),K[1]&&U.push({pos:[P.pos[0]-1,P.pos[1]],seq1:this.sequence1[P.pos[0]-1]+P.seq1,seq2:"-"+P.seq2}),K[2]&&U.push({pos:[P.pos[0],P.pos[1]-1],seq1:"-"+P.seq1,seq2:this.sequence2[P.pos[1]-1]+P.seq2}),P.pos[0]===0&&P.pos[1]===0&&this.alignments.push({sequence1:P.seq1,sequence2:P.seq2}),U.shift()}return this.alignments}},{key:"getAllIndexes",value:function(U,P){for(var K=[],D=-1;(D=U.indexOf(P,D+1))!==-1;)K.push(D);return K}},{key:"arrayAllMaxIndexes",value:function(U){return this.getAllIndexes(U,Math.max.apply(null,U))}}]),S}();ee.exports=w},function(ee,ce,H){var O=function(){};O.FDLayout=H(18),O.FDLayoutConstants=H(7),O.FDLayoutEdge=H(19),O.FDLayoutNode=H(20),O.DimensionD=H(21),O.HashMap=H(22),O.HashSet=H(23),O.IGeometry=H(8),O.IMath=H(9),O.Integer=H(10),O.Point=H(12),O.PointD=H(4),O.RandomSeed=H(16),O.RectangleD=H(13),O.Transform=H(17),O.UniqueIDGeneretor=H(14),O.Quicksort=H(24),O.LinkedList=H(11),O.LGraphObject=H(2),O.LGraph=H(5),O.LEdge=H(1),O.LGraphManager=H(6),O.LNode=H(3),O.Layout=H(15),O.LayoutConstants=H(0),O.NeedlemanWunsch=H(25),ee.exports=O},function(ee,ce,H){function O(){this.listeners=[]}var T=O.prototype;T.addListener=function(w,S){this.listeners.push({event:w,callback:S})},T.removeListener=function(w,S){for(var G=this.listeners.length;G>=0;G--){var U=this.listeners[G];U.event===w&&U.callback===S&&this.listeners.splice(G,1)}},T.emit=function(w,S){for(var G=0;G<this.listeners.length;G++){var U=this.listeners[G];w===U.event&&U.callback(S)}},ee.exports=O}])})}(Ki)),Ki.exports}var Vu;function Ap(){return Vu||(Vu=1,function(pe,le){(function(ce,H){pe.exports=H(Lp())})(Da,function(ee){return function(ce){var H={};function O(T){if(H[T])return H[T].exports;var w=H[T]={i:T,l:!1,exports:{}};return ce[T].call(w.exports,w,w.exports,O),w.l=!0,w.exports}return O.m=ce,O.c=H,O.i=function(T){return T},O.d=function(T,w,S){O.o(T,w)||Object.defineProperty(T,w,{configurable:!1,enumerable:!0,get:S})},O.n=function(T){var w=T&&T.__esModule?function(){return T.default}:function(){return T};return O.d(w,"a",w),w},O.o=function(T,w){return Object.prototype.hasOwnProperty.call(T,w)},O.p="",O(O.s=7)}([function(ce,H){ce.exports=ee},function(ce,H,O){var T=O(0).FDLayoutConstants;function w(){}for(var S in T)w[S]=T[S];w.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,w.DEFAULT_RADIAL_SEPARATION=T.DEFAULT_EDGE_LENGTH,w.DEFAULT_COMPONENT_SEPERATION=60,w.TILE=!0,w.TILING_PADDING_VERTICAL=10,w.TILING_PADDING_HORIZONTAL=10,w.TREE_REDUCTION_ON_INCREMENTAL=!1,ce.exports=w},function(ce,H,O){var T=O(0).FDLayoutEdge;function w(G,U,P){T.call(this,G,U,P)}w.prototype=Object.create(T.prototype);for(var S in T)w[S]=T[S];ce.exports=w},function(ce,H,O){var T=O(0).LGraph;function w(G,U,P){T.call(this,G,U,P)}w.prototype=Object.create(T.prototype);for(var S in T)w[S]=T[S];ce.exports=w},function(ce,H,O){var T=O(0).LGraphManager;function w(G){T.call(this,G)}w.prototype=Object.create(T.prototype);for(var S in T)w[S]=T[S];ce.exports=w},function(ce,H,O){var T=O(0).FDLayoutNode,w=O(0).IMath;function S(U,P,K,D){T.call(this,U,P,K,D)}S.prototype=Object.create(T.prototype);for(var G in T)S[G]=T[G];S.prototype.move=function(){var U=this.graphManager.getLayout();this.displacementX=U.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=U.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>U.coolingFactor*U.maxNodeDisplacement&&(this.displacementX=U.coolingFactor*U.maxNodeDisplacement*w.sign(this.displacementX)),Math.abs(this.displacementY)>U.coolingFactor*U.maxNodeDisplacement&&(this.displacementY=U.coolingFactor*U.maxNodeDisplacement*w.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),U.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},S.prototype.propogateDisplacementToChildren=function(U,P){for(var K=this.getChild().getNodes(),D,V=0;V<K.length;V++)D=K[V],D.getChild()==null?(D.moveBy(U,P),D.displacementX+=U,D.displacementY+=P):D.propogateDisplacementToChildren(U,P)},S.prototype.setPred1=function(U){this.pred1=U},S.prototype.getPred1=function(){return pred1},S.prototype.getPred2=function(){return pred2},S.prototype.setNext=function(U){this.next=U},S.prototype.getNext=function(){return next},S.prototype.setProcessed=function(U){this.processed=U},S.prototype.isProcessed=function(){return processed},ce.exports=S},function(ce,H,O){var T=O(0).FDLayout,w=O(4),S=O(3),G=O(5),U=O(2),P=O(1),K=O(0).FDLayoutConstants,D=O(0).LayoutConstants,V=O(0).Point,_=O(0).PointD,Q=O(0).Layout,ne=O(0).Integer,oe=O(0).IGeometry,J=O(0).LGraph,j=O(0).Transform;function Y(){T.call(this),this.toBeTiled={}}Y.prototype=Object.create(T.prototype);for(var te in T)Y[te]=T[te];Y.prototype.newGraphManager=function(){var L=new w(this);return this.graphManager=L,L},Y.prototype.newGraph=function(L){return new S(null,this.graphManager,L)},Y.prototype.newNode=function(L){return new G(this.graphManager,L)},Y.prototype.newEdge=function(L){return new U(null,null,L)},Y.prototype.initParameters=function(){T.prototype.initParameters.call(this,arguments),this.isSubLayout||(P.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=P.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=P.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=K.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=K.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=K.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=K.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=K.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=K.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/K.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=K.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},Y.prototype.layout=function(){var L=D.DEFAULT_CREATE_BENDS_AS_NEEDED;return L&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},Y.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(P.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var $=new Set(this.getAllNodes()),R=this.nodesWithGravity.filter(function(de){return $.has(de)});this.graphManager.setAllNodesToApplyGravitation(R)}}else{var L=this.getFlatForest();if(L.length>0)this.positionNodesRadially(L);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var $=new Set(this.getAllNodes()),R=this.nodesWithGravity.filter(function(W){return $.has(W)});this.graphManager.setAllNodesToApplyGravitation(R),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},Y.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%K.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var L=new Set(this.getAllNodes()),$=this.nodesWithGravity.filter(function(fe){return L.has(fe)});this.graphManager.setAllNodesToApplyGravitation($),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=K.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=K.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var R=!this.isTreeGrowing&&!this.isGrowthFinished,W=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(R,W),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},Y.prototype.getPositionsData=function(){for(var L=this.graphManager.getAllNodes(),$={},R=0;R<L.length;R++){var W=L[R].rect,fe=L[R].id;$[fe]={id:fe,x:W.getCenterX(),y:W.getCenterY(),w:W.width,h:W.height}}return $},Y.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var L=!1;if(K.ANIMATE==="during")this.emit("layoutstarted");else{for(;!L;)L=this.tick();this.graphManager.updateBounds()}},Y.prototype.calculateNodesToApplyGravitationTo=function(){var L=[],$,R=this.graphManager.getGraphs(),W=R.length,fe;for(fe=0;fe<W;fe++)$=R[fe],$.updateConnected(),$.isConnected||(L=L.concat($.getNodes()));return L},Y.prototype.createBendpoints=function(){var L=[];L=L.concat(this.graphManager.getAllEdges());var $=new Set,R;for(R=0;R<L.length;R++){var W=L[R];if(!$.has(W)){var fe=W.getSource(),de=W.getTarget();if(fe==de)W.getBendpoints().push(new _),W.getBendpoints().push(new _),this.createDummyNodesForBendpoints(W),$.add(W);else{var Ae=[];if(Ae=Ae.concat(fe.getEdgeListToNode(de)),Ae=Ae.concat(de.getEdgeListToNode(fe)),!$.has(Ae[0])){if(Ae.length>1){var Ne;for(Ne=0;Ne<Ae.length;Ne++){var _e=Ae[Ne];_e.getBendpoints().push(new _),this.createDummyNodesForBendpoints(_e)}}Ae.forEach(function(tt){$.add(tt)})}}}if($.size==L.length)break}},Y.prototype.positionNodesRadially=function(L){for(var $=new V(0,0),R=Math.ceil(Math.sqrt(L.length)),W=0,fe=0,de=0,Ae=new _(0,0),Ne=0;Ne<L.length;Ne++){Ne%R==0&&(de=0,fe=W,Ne!=0&&(fe+=P.DEFAULT_COMPONENT_SEPERATION),W=0);var _e=L[Ne],tt=Q.findCenterOfTree(_e);$.x=de,$.y=fe,Ae=Y.radialLayout(_e,tt,$),Ae.y>W&&(W=Math.floor(Ae.y)),de=Math.floor(Ae.x+P.DEFAULT_COMPONENT_SEPERATION)}this.transform(new _(D.WORLD_CENTER_X-Ae.x/2,D.WORLD_CENTER_Y-Ae.y/2))},Y.radialLayout=function(L,$,R){var W=Math.max(this.maxDiagonalInTree(L),P.DEFAULT_RADIAL_SEPARATION);Y.branchRadialLayout($,null,0,359,0,W);var fe=J.calculateBounds(L),de=new j;de.setDeviceOrgX(fe.getMinX()),de.setDeviceOrgY(fe.getMinY()),de.setWorldOrgX(R.x),de.setWorldOrgY(R.y);for(var Ae=0;Ae<L.length;Ae++){var Ne=L[Ae];Ne.transform(de)}var _e=new _(fe.getMaxX(),fe.getMaxY());return de.inverseTransformPoint(_e)},Y.branchRadialLayout=function(L,$,R,W,fe,de){var Ae=(W-R+1)/2;Ae<0&&(Ae+=180);var Ne=(Ae+R)%360,_e=Ne*oe.TWO_PI/360,tt=fe*Math.cos(_e),vt=fe*Math.sin(_e);L.setCenter(tt,vt);var Pe=[];Pe=Pe.concat(L.getEdges());var $e=Pe.length;$!=null&&$e--;for(var Xe=0,rt=Pe.length,lt,at=L.getEdgesBetween($);at.length>1;){var et=at[0];at.splice(0,1);var yt=Pe.indexOf(et);yt>=0&&Pe.splice(yt,1),rt--,$e--}$!=null?lt=(Pe.indexOf(at[0])+1)%rt:lt=0;for(var wt=Math.abs(W-R)/$e,Pt=lt;Xe!=$e;Pt=++Pt%rt){var dt=Pe[Pt].getOtherEnd(L);if(dt!=$){var dr=(R+Xe*wt)%360,qt=(dr+wt)%360;Y.branchRadialLayout(dt,L,dr,qt,fe+de,de),Xe++}}},Y.maxDiagonalInTree=function(L){for(var $=ne.MIN_VALUE,R=0;R<L.length;R++){var W=L[R],fe=W.getDiagonal();fe>$&&($=fe)}return $},Y.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},Y.prototype.groupZeroDegreeMembers=function(){var L=this,$={};this.memberGroups={},this.idToDummyNode={};for(var R=[],W=this.graphManager.getAllNodes(),fe=0;fe<W.length;fe++){var de=W[fe],Ae=de.getParent();this.getNodeDegreeWithChildren(de)===0&&(Ae.id==null||!this.getToBeTiled(Ae))&&R.push(de)}for(var fe=0;fe<R.length;fe++){var de=R[fe],Ne=de.getParent().id;typeof $[Ne]>"u"&&($[Ne]=[]),$[Ne]=$[Ne].concat(de)}Object.keys($).forEach(function(_e){if($[_e].length>1){var tt="DummyCompound_"+_e;L.memberGroups[tt]=$[_e];var vt=$[_e][0].getParent(),Pe=new G(L.graphManager);Pe.id=tt,Pe.paddingLeft=vt.paddingLeft||0,Pe.paddingRight=vt.paddingRight||0,Pe.paddingBottom=vt.paddingBottom||0,Pe.paddingTop=vt.paddingTop||0,L.idToDummyNode[tt]=Pe;var $e=L.getGraphManager().add(L.newGraph(),Pe),Xe=vt.getChild();Xe.add(Pe);for(var rt=0;rt<$[_e].length;rt++){var lt=$[_e][rt];Xe.remove(lt),$e.add(lt)}}})},Y.prototype.clearCompounds=function(){var L={},$={};this.performDFSOnCompounds();for(var R=0;R<this.compoundOrder.length;R++)$[this.compoundOrder[R].id]=this.compoundOrder[R],L[this.compoundOrder[R].id]=[].concat(this.compoundOrder[R].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[R].getChild()),this.compoundOrder[R].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(L,$)},Y.prototype.clearZeroDegreeMembers=function(){var L=this,$=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(R){var W=L.idToDummyNode[R];$[R]=L.tileNodes(L.memberGroups[R],W.paddingLeft+W.paddingRight),W.rect.width=$[R].width,W.rect.height=$[R].height})},Y.prototype.repopulateCompounds=function(){for(var L=this.compoundOrder.length-1;L>=0;L--){var $=this.compoundOrder[L],R=$.id,W=$.paddingLeft,fe=$.paddingTop;this.adjustLocations(this.tiledMemberPack[R],$.rect.x,$.rect.y,W,fe)}},Y.prototype.repopulateZeroDegreeMembers=function(){var L=this,$=this.tiledZeroDegreePack;Object.keys($).forEach(function(R){var W=L.idToDummyNode[R],fe=W.paddingLeft,de=W.paddingTop;L.adjustLocations($[R],W.rect.x,W.rect.y,fe,de)})},Y.prototype.getToBeTiled=function(L){var $=L.id;if(this.toBeTiled[$]!=null)return this.toBeTiled[$];var R=L.getChild();if(R==null)return this.toBeTiled[$]=!1,!1;for(var W=R.getNodes(),fe=0;fe<W.length;fe++){var de=W[fe];if(this.getNodeDegree(de)>0)return this.toBeTiled[$]=!1,!1;if(de.getChild()==null){this.toBeTiled[de.id]=!1;continue}if(!this.getToBeTiled(de))return this.toBeTiled[$]=!1,!1}return this.toBeTiled[$]=!0,!0},Y.prototype.getNodeDegree=function(L){L.id;for(var $=L.getEdges(),R=0,W=0;W<$.length;W++){var fe=$[W];fe.getSource().id!==fe.getTarget().id&&(R=R+1)}return R},Y.prototype.getNodeDegreeWithChildren=function(L){var $=this.getNodeDegree(L);if(L.getChild()==null)return $;for(var R=L.getChild().getNodes(),W=0;W<R.length;W++){var fe=R[W];$+=this.getNodeDegreeWithChildren(fe)}return $},Y.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},Y.prototype.fillCompexOrderByDFS=function(L){for(var $=0;$<L.length;$++){var R=L[$];R.getChild()!=null&&this.fillCompexOrderByDFS(R.getChild().getNodes()),this.getToBeTiled(R)&&this.compoundOrder.push(R)}},Y.prototype.adjustLocations=function(L,$,R,W,fe){$+=W,R+=fe;for(var de=$,Ae=0;Ae<L.rows.length;Ae++){var Ne=L.rows[Ae];$=de;for(var _e=0,tt=0;tt<Ne.length;tt++){var vt=Ne[tt];vt.rect.x=$,vt.rect.y=R,$+=vt.rect.width+L.horizontalPadding,vt.rect.height>_e&&(_e=vt.rect.height)}R+=_e+L.verticalPadding}},Y.prototype.tileCompoundMembers=function(L,$){var R=this;this.tiledMemberPack=[],Object.keys(L).forEach(function(W){var fe=$[W];R.tiledMemberPack[W]=R.tileNodes(L[W],fe.paddingLeft+fe.paddingRight),fe.rect.width=R.tiledMemberPack[W].width,fe.rect.height=R.tiledMemberPack[W].height})},Y.prototype.tileNodes=function(L,$){var R=P.TILING_PADDING_VERTICAL,W=P.TILING_PADDING_HORIZONTAL,fe={rows:[],rowWidth:[],rowHeight:[],width:0,height:$,verticalPadding:R,horizontalPadding:W};L.sort(function(Ne,_e){return Ne.rect.width*Ne.rect.height>_e.rect.width*_e.rect.height?-1:Ne.rect.width*Ne.rect.height<_e.rect.width*_e.rect.height?1:0});for(var de=0;de<L.length;de++){var Ae=L[de];fe.rows.length==0?this.insertNodeToRow(fe,Ae,0,$):this.canAddHorizontal(fe,Ae.rect.width,Ae.rect.height)?this.insertNodeToRow(fe,Ae,this.getShortestRowIndex(fe),$):this.insertNodeToRow(fe,Ae,fe.rows.length,$),this.shiftToLastRow(fe)}return fe},Y.prototype.insertNodeToRow=function(L,$,R,W){var fe=W;if(R==L.rows.length){var de=[];L.rows.push(de),L.rowWidth.push(fe),L.rowHeight.push(0)}var Ae=L.rowWidth[R]+$.rect.width;L.rows[R].length>0&&(Ae+=L.horizontalPadding),L.rowWidth[R]=Ae,L.width<Ae&&(L.width=Ae);var Ne=$.rect.height;R>0&&(Ne+=L.verticalPadding);var _e=0;Ne>L.rowHeight[R]&&(_e=L.rowHeight[R],L.rowHeight[R]=Ne,_e=L.rowHeight[R]-_e),L.height+=_e,L.rows[R].push($)},Y.prototype.getShortestRowIndex=function(L){for(var $=-1,R=Number.MAX_VALUE,W=0;W<L.rows.length;W++)L.rowWidth[W]<R&&($=W,R=L.rowWidth[W]);return $},Y.prototype.getLongestRowIndex=function(L){for(var $=-1,R=Number.MIN_VALUE,W=0;W<L.rows.length;W++)L.rowWidth[W]>R&&($=W,R=L.rowWidth[W]);return $},Y.prototype.canAddHorizontal=function(L,$,R){var W=this.getShortestRowIndex(L);if(W<0)return!0;var fe=L.rowWidth[W];if(fe+L.horizontalPadding+$<=L.width)return!0;var de=0;L.rowHeight[W]<R&&W>0&&(de=R+L.verticalPadding-L.rowHeight[W]);var Ae;L.width-fe>=$+L.horizontalPadding?Ae=(L.height+de)/(fe+$+L.horizontalPadding):Ae=(L.height+de)/L.width,de=R+L.verticalPadding;var Ne;return L.width<$?Ne=(L.height+de)/$:Ne=(L.height+de)/L.width,Ne<1&&(Ne=1/Ne),Ae<1&&(Ae=1/Ae),Ae<Ne},Y.prototype.shiftToLastRow=function(L){var $=this.getLongestRowIndex(L),R=L.rowWidth.length-1,W=L.rows[$],fe=W[W.length-1],de=fe.width+L.horizontalPadding;if(L.width-L.rowWidth[R]>de&&$!=R){W.splice(-1,1),L.rows[R].push(fe),L.rowWidth[$]=L.rowWidth[$]-de,L.rowWidth[R]=L.rowWidth[R]+de,L.width=L.rowWidth[instance.getLongestRowIndex(L)];for(var Ae=Number.MIN_VALUE,Ne=0;Ne<W.length;Ne++)W[Ne].height>Ae&&(Ae=W[Ne].height);$>0&&(Ae+=L.verticalPadding);var _e=L.rowHeight[$]+L.rowHeight[R];L.rowHeight[$]=Ae,L.rowHeight[R]<fe.height+L.verticalPadding&&(L.rowHeight[R]=fe.height+L.verticalPadding);var tt=L.rowHeight[$]+L.rowHeight[R];L.height+=tt-_e,this.shiftToLastRow(L)}},Y.prototype.tilingPreLayout=function(){P.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},Y.prototype.tilingPostLayout=function(){P.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},Y.prototype.reduceTrees=function(){for(var L=[],$=!0,R;$;){var W=this.graphManager.getAllNodes(),fe=[];$=!1;for(var de=0;de<W.length;de++)R=W[de],R.getEdges().length==1&&!R.getEdges()[0].isInterGraph&&R.getChild()==null&&(fe.push([R,R.getEdges()[0],R.getOwner()]),$=!0);if($==!0){for(var Ae=[],Ne=0;Ne<fe.length;Ne++)fe[Ne][0].getEdges().length==1&&(Ae.push(fe[Ne]),fe[Ne][0].getOwner().remove(fe[Ne][0]));L.push(Ae),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=L},Y.prototype.growTree=function(L){for(var $=L.length,R=L[$-1],W,fe=0;fe<R.length;fe++)W=R[fe],this.findPlaceforPrunedNode(W),W[2].add(W[0]),W[2].add(W[1],W[1].source,W[1].target);L.splice(L.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},Y.prototype.findPlaceforPrunedNode=function(L){var $,R,W=L[0];W==L[1].source?R=L[1].target:R=L[1].source;var fe=R.startX,de=R.finishX,Ae=R.startY,Ne=R.finishY,_e=0,tt=0,vt=0,Pe=0,$e=[_e,vt,tt,Pe];if(Ae>0)for(var Xe=fe;Xe<=de;Xe++)$e[0]+=this.grid[Xe][Ae-1].length+this.grid[Xe][Ae].length-1;if(de<this.grid.length-1)for(var Xe=Ae;Xe<=Ne;Xe++)$e[1]+=this.grid[de+1][Xe].length+this.grid[de][Xe].length-1;if(Ne<this.grid[0].length-1)for(var Xe=fe;Xe<=de;Xe++)$e[2]+=this.grid[Xe][Ne+1].length+this.grid[Xe][Ne].length-1;if(fe>0)for(var Xe=Ae;Xe<=Ne;Xe++)$e[3]+=this.grid[fe-1][Xe].length+this.grid[fe][Xe].length-1;for(var rt=ne.MAX_VALUE,lt,at,et=0;et<$e.length;et++)$e[et]<rt?(rt=$e[et],lt=1,at=et):$e[et]==rt&&lt++;if(lt==3&&rt==0)$e[0]==0&&$e[1]==0&&$e[2]==0?$=1:$e[0]==0&&$e[1]==0&&$e[3]==0?$=0:$e[0]==0&&$e[2]==0&&$e[3]==0?$=3:$e[1]==0&&$e[2]==0&&$e[3]==0&&($=2);else if(lt==2&&rt==0){var yt=Math.floor(Math.random()*2);$e[0]==0&&$e[1]==0?yt==0?$=0:$=1:$e[0]==0&&$e[2]==0?yt==0?$=0:$=2:$e[0]==0&&$e[3]==0?yt==0?$=0:$=3:$e[1]==0&&$e[2]==0?yt==0?$=1:$=2:$e[1]==0&&$e[3]==0?yt==0?$=1:$=3:yt==0?$=2:$=3}else if(lt==4&&rt==0){var yt=Math.floor(Math.random()*4);$=yt}else $=at;$==0?W.setCenter(R.getCenterX(),R.getCenterY()-R.getHeight()/2-K.DEFAULT_EDGE_LENGTH-W.getHeight()/2):$==1?W.setCenter(R.getCenterX()+R.getWidth()/2+K.DEFAULT_EDGE_LENGTH+W.getWidth()/2,R.getCenterY()):$==2?W.setCenter(R.getCenterX(),R.getCenterY()+R.getHeight()/2+K.DEFAULT_EDGE_LENGTH+W.getHeight()/2):W.setCenter(R.getCenterX()-R.getWidth()/2-K.DEFAULT_EDGE_LENGTH-W.getWidth()/2,R.getCenterY())},ce.exports=Y},function(ce,H,O){var T={};T.layoutBase=O(0),T.CoSEConstants=O(1),T.CoSEEdge=O(2),T.CoSEGraph=O(3),T.CoSEGraphManager=O(4),T.CoSELayout=O(6),T.CoSENode=O(5),ce.exports=T}])})}(qi)),qi.exports}(function(pe,le){(function(ce,H){pe.exports=H(Ap())})(Da,function(ee){return function(ce){var H={};function O(T){if(H[T])return H[T].exports;var w=H[T]={i:T,l:!1,exports:{}};return ce[T].call(w.exports,w,w.exports,O),w.l=!0,w.exports}return O.m=ce,O.c=H,O.i=function(T){return T},O.d=function(T,w,S){O.o(T,w)||Object.defineProperty(T,w,{configurable:!1,enumerable:!0,get:S})},O.n=function(T){var w=T&&T.__esModule?function(){return T.default}:function(){return T};return O.d(w,"a",w),w},O.o=function(T,w){return Object.prototype.hasOwnProperty.call(T,w)},O.p="",O(O.s=1)}([function(ce,H){ce.exports=ee},function(ce,H,O){var T=O(0).layoutBase.LayoutConstants,w=O(0).layoutBase.FDLayoutConstants,S=O(0).CoSEConstants,G=O(0).CoSELayout,U=O(0).CoSENode,P=O(0).layoutBase.PointD,K=O(0).layoutBase.DimensionD,D={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function V(oe,J){var j={};for(var Y in oe)j[Y]=oe[Y];for(var Y in J)j[Y]=J[Y];return j}function _(oe){this.options=V(D,oe),Q(this.options)}var Q=function(J){J.nodeRepulsion!=null&&(S.DEFAULT_REPULSION_STRENGTH=w.DEFAULT_REPULSION_STRENGTH=J.nodeRepulsion),J.idealEdgeLength!=null&&(S.DEFAULT_EDGE_LENGTH=w.DEFAULT_EDGE_LENGTH=J.idealEdgeLength),J.edgeElasticity!=null&&(S.DEFAULT_SPRING_STRENGTH=w.DEFAULT_SPRING_STRENGTH=J.edgeElasticity),J.nestingFactor!=null&&(S.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=w.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=J.nestingFactor),J.gravity!=null&&(S.DEFAULT_GRAVITY_STRENGTH=w.DEFAULT_GRAVITY_STRENGTH=J.gravity),J.numIter!=null&&(S.MAX_ITERATIONS=w.MAX_ITERATIONS=J.numIter),J.gravityRange!=null&&(S.DEFAULT_GRAVITY_RANGE_FACTOR=w.DEFAULT_GRAVITY_RANGE_FACTOR=J.gravityRange),J.gravityCompound!=null&&(S.DEFAULT_COMPOUND_GRAVITY_STRENGTH=w.DEFAULT_COMPOUND_GRAVITY_STRENGTH=J.gravityCompound),J.gravityRangeCompound!=null&&(S.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=w.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=J.gravityRangeCompound),J.initialEnergyOnIncremental!=null&&(S.DEFAULT_COOLING_FACTOR_INCREMENTAL=w.DEFAULT_COOLING_FACTOR_INCREMENTAL=J.initialEnergyOnIncremental),J.quality=="draft"?T.QUALITY=0:J.quality=="proof"?T.QUALITY=2:T.QUALITY=1,S.NODE_DIMENSIONS_INCLUDE_LABELS=w.NODE_DIMENSIONS_INCLUDE_LABELS=T.NODE_DIMENSIONS_INCLUDE_LABELS=J.nodeDimensionsIncludeLabels,S.DEFAULT_INCREMENTAL=w.DEFAULT_INCREMENTAL=T.DEFAULT_INCREMENTAL=!J.randomize,S.ANIMATE=w.ANIMATE=T.ANIMATE=J.animate,S.TILE=J.tile,S.TILING_PADDING_VERTICAL=typeof J.tilingPaddingVertical=="function"?J.tilingPaddingVertical.call():J.tilingPaddingVertical,S.TILING_PADDING_HORIZONTAL=typeof J.tilingPaddingHorizontal=="function"?J.tilingPaddingHorizontal.call():J.tilingPaddingHorizontal};_.prototype.run=function(){var oe,J,j=this.options;this.idToLNode={};var Y=this.layout=new G,te=this;te.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var L=Y.newGraphManager();this.gm=L;var $=this.options.eles.nodes(),R=this.options.eles.edges();this.root=L.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes($),Y);for(var W=0;W<R.length;W++){var fe=R[W],de=this.idToLNode[fe.data("source")],Ae=this.idToLNode[fe.data("target")];if(de!==Ae&&de.getEdgesBetween(Ae).length==0){var Ne=L.add(Y.newEdge(),de,Ae);Ne.id=fe.id()}}var _e=function(Pe,$e){typeof Pe=="number"&&(Pe=$e);var Xe=Pe.data("id"),rt=te.idToLNode[Xe];return{x:rt.getRect().getCenterX(),y:rt.getRect().getCenterY()}},tt=function vt(){for(var Pe=function(){j.fit&&j.cy.fit(j.eles,j.padding),oe||(oe=!0,te.cy.one("layoutready",j.ready),te.cy.trigger({type:"layoutready",layout:te}))},$e=te.options.refresh,Xe,rt=0;rt<$e&&!Xe;rt++)Xe=te.stopped||te.layout.tick();if(Xe){Y.checkLayoutSuccess()&&!Y.isSubLayout&&Y.doPostLayout(),Y.tilingPostLayout&&Y.tilingPostLayout(),Y.isLayoutFinished=!0,te.options.eles.nodes().positions(_e),Pe(),te.cy.one("layoutstop",te.options.stop),te.cy.trigger({type:"layoutstop",layout:te}),J&&cancelAnimationFrame(J),oe=!1;return}var lt=te.layout.getPositionsData();j.eles.nodes().positions(function(at,et){if(typeof at=="number"&&(at=et),!at.isParent()){for(var yt=at.id(),wt=lt[yt],Pt=at;wt==null&&(wt=lt[Pt.data("parent")]||lt["DummyCompound_"+Pt.data("parent")],lt[yt]=wt,Pt=Pt.parent()[0],Pt!=null););return wt!=null?{x:wt.x,y:wt.y}:{x:at.position("x"),y:at.position("y")}}}),Pe(),J=requestAnimationFrame(vt)};return Y.addListener("layoutstarted",function(){te.options.animate==="during"&&(J=requestAnimationFrame(tt))}),Y.runLayout(),this.options.animate!=="during"&&(te.options.eles.nodes().not(":parent").layoutPositions(te,te.options,_e),oe=!1),this},_.prototype.getTopMostNodes=function(oe){for(var J={},j=0;j<oe.length;j++)J[oe[j].id()]=!0;var Y=oe.filter(function(te,L){typeof te=="number"&&(te=L);for(var $=te.parent()[0];$!=null;){if(J[$.id()])return!1;$=$.parent()[0]}return!0});return Y},_.prototype.processChildrenList=function(oe,J,j){for(var Y=J.length,te=0;te<Y;te++){var L=J[te],$=L.children(),R,W=L.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(L.outerWidth()!=null&&L.outerHeight()!=null?R=oe.add(new U(j.graphManager,new P(L.position("x")-W.w/2,L.position("y")-W.h/2),new K(parseFloat(W.w),parseFloat(W.h)))):R=oe.add(new U(this.graphManager)),R.id=L.data("id"),R.paddingLeft=parseInt(L.css("padding")),R.paddingTop=parseInt(L.css("padding")),R.paddingRight=parseInt(L.css("padding")),R.paddingBottom=parseInt(L.css("padding")),this.options.nodeDimensionsIncludeLabels&&L.isParent()){var fe=L.boundingBox({includeLabels:!0,includeNodes:!1}).w,de=L.boundingBox({includeLabels:!0,includeNodes:!1}).h,Ae=L.css("text-halign");R.labelWidth=fe,R.labelHeight=de,R.labelPos=Ae}if(this.idToLNode[L.data("id")]=R,isNaN(R.rect.x)&&(R.rect.x=0),isNaN(R.rect.y)&&(R.rect.y=0),$!=null&&$.length>0){var Ne;Ne=j.getGraphManager().add(j.newGraph(),R),this.processChildrenList(Ne,$,j)}}},_.prototype.stop=function(){return this.stopped=!0,this};var ne=function(J){J("layout","cose-bilkent",_)};typeof cytoscape<"u"&&ne(cytoscape),ce.exports=ne}])})})(Hu);var Op=Hu.exports;const Np=_u(Op);var Zi=function(){var pe=function(j,Y,te,L){for(te=te||{},L=j.length;L--;te[j[L]]=Y);return te},le=[1,4],ee=[1,13],ce=[1,12],H=[1,15],O=[1,16],T=[1,20],w=[1,19],S=[6,7,8],G=[1,26],U=[1,24],P=[1,25],K=[6,7,11],D=[1,6,13,15,16,19,22],V=[1,33],_=[1,34],Q=[1,6,7,11,13,15,16,19,22],ne={trace:function(){},yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:function(Y,te,L,$,R,W,fe){var de=W.length-1;switch(R){case 6:case 7:return $;case 8:$.getLogger().trace("Stop NL ");break;case 9:$.getLogger().trace("Stop EOF ");break;case 11:$.getLogger().trace("Stop NL2 ");break;case 12:$.getLogger().trace("Stop EOF2 ");break;case 15:$.getLogger().info("Node: ",W[de].id),$.addNode(W[de-1].length,W[de].id,W[de].descr,W[de].type);break;case 16:$.getLogger().trace("Icon: ",W[de]),$.decorateNode({icon:W[de]});break;case 17:case 21:$.decorateNode({class:W[de]});break;case 18:$.getLogger().trace("SPACELIST");break;case 19:$.getLogger().trace("Node: ",W[de].id),$.addNode(0,W[de].id,W[de].descr,W[de].type);break;case 20:$.decorateNode({icon:W[de]});break;case 25:$.getLogger().trace("node found ..",W[de-2]),this.$={id:W[de-1],descr:W[de-1],type:$.getType(W[de-2],W[de])};break;case 26:this.$={id:W[de],descr:W[de],type:$.nodeType.DEFAULT};break;case 27:$.getLogger().trace("node found ..",W[de-3]),this.$={id:W[de-3],descr:W[de-1],type:$.getType(W[de-2],W[de])};break}},table:[{3:1,4:2,5:3,6:[1,5],8:le},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:le},{6:ee,7:[1,10],9:9,12:11,13:ce,14:14,15:H,16:O,17:17,18:18,19:T,22:w},pe(S,[2,3]),{1:[2,2]},pe(S,[2,4]),pe(S,[2,5]),{1:[2,6],6:ee,12:21,13:ce,14:14,15:H,16:O,17:17,18:18,19:T,22:w},{6:ee,9:22,12:11,13:ce,14:14,15:H,16:O,17:17,18:18,19:T,22:w},{6:G,7:U,10:23,11:P},pe(K,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:T,22:w}),pe(K,[2,18]),pe(K,[2,19]),pe(K,[2,20]),pe(K,[2,21]),pe(K,[2,23]),pe(K,[2,24]),pe(K,[2,26],{19:[1,30]}),{20:[1,31]},{6:G,7:U,10:32,11:P},{1:[2,7],6:ee,12:21,13:ce,14:14,15:H,16:O,17:17,18:18,19:T,22:w},pe(D,[2,14],{7:V,11:_}),pe(Q,[2,8]),pe(Q,[2,9]),pe(Q,[2,10]),pe(K,[2,15]),pe(K,[2,16]),pe(K,[2,17]),{20:[1,35]},{21:[1,36]},pe(D,[2,13],{7:V,11:_}),pe(Q,[2,11]),pe(Q,[2,12]),{21:[1,37]},pe(K,[2,25]),pe(K,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:function(Y,te){if(te.recoverable)this.trace(Y);else{var L=new Error(Y);throw L.hash=te,L}},parse:function(Y){var te=this,L=[0],$=[],R=[null],W=[],fe=this.table,de="",Ae=0,Ne=0,_e=2,tt=1,vt=W.slice.call(arguments,1),Pe=Object.create(this.lexer),$e={yy:{}};for(var Xe in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Xe)&&($e.yy[Xe]=this.yy[Xe]);Pe.setInput(Y,$e.yy),$e.yy.lexer=Pe,$e.yy.parser=this,typeof Pe.yylloc>"u"&&(Pe.yylloc={});var rt=Pe.yylloc;W.push(rt);var lt=Pe.options&&Pe.options.ranges;typeof $e.yy.parseError=="function"?this.parseError=$e.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function at(){var er;return er=$.pop()||Pe.lex()||tt,typeof er!="number"&&(er instanceof Array&&($=er,er=$.pop()),er=te.symbols_[er]||er),er}for(var et,yt,wt,Pt,dt={},dr,qt,un,Zr;;){if(yt=L[L.length-1],this.defaultActions[yt]?wt=this.defaultActions[yt]:((et===null||typeof et>"u")&&(et=at()),wt=fe[yt]&&fe[yt][et]),typeof wt>"u"||!wt.length||!wt[0]){var Sa="";Zr=[];for(dr in fe[yt])this.terminals_[dr]&&dr>_e&&Zr.push("'"+this.terminals_[dr]+"'");Pe.showPosition?Sa="Parse error on line "+(Ae+1)+`:
`+Pe.showPosition()+`
Expecting `+Zr.join(", ")+", got '"+(this.terminals_[et]||et)+"'":Sa="Parse error on line "+(Ae+1)+": Unexpected "+(et==tt?"end of input":"'"+(this.terminals_[et]||et)+"'"),this.parseError(Sa,{text:Pe.match,token:this.terminals_[et]||et,line:Pe.yylineno,loc:rt,expected:Zr})}if(wt[0]instanceof Array&&wt.length>1)throw new Error("Parse Error: multiple actions possible at state: "+yt+", token: "+et);switch(wt[0]){case 1:L.push(et),R.push(Pe.yytext),W.push(Pe.yylloc),L.push(wt[1]),et=null,Ne=Pe.yyleng,de=Pe.yytext,Ae=Pe.yylineno,rt=Pe.yylloc;break;case 2:if(qt=this.productions_[wt[1]][1],dt.$=R[R.length-qt],dt._$={first_line:W[W.length-(qt||1)].first_line,last_line:W[W.length-1].last_line,first_column:W[W.length-(qt||1)].first_column,last_column:W[W.length-1].last_column},lt&&(dt._$.range=[W[W.length-(qt||1)].range[0],W[W.length-1].range[1]]),Pt=this.performAction.apply(dt,[de,Ne,Ae,$e.yy,wt[1],R,W].concat(vt)),typeof Pt<"u")return Pt;qt&&(L=L.slice(0,-1*qt*2),R=R.slice(0,-1*qt),W=W.slice(0,-1*qt)),L.push(this.productions_[wt[1]][0]),R.push(dt.$),W.push(dt._$),un=fe[L[L.length-2]][L[L.length-1]],L.push(un);break;case 3:return!0}}return!0}},oe=function(){var j={EOF:1,parseError:function(te,L){if(this.yy.parser)this.yy.parser.parseError(te,L);else throw new Error(te)},setInput:function(Y,te){return this.yy=te||this.yy||{},this._input=Y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var Y=this._input[0];this.yytext+=Y,this.yyleng++,this.offset++,this.match+=Y,this.matched+=Y;var te=Y.match(/(?:\r\n?|\n).*/g);return te?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),Y},unput:function(Y){var te=Y.length,L=Y.split(/(?:\r\n?|\n)/g);this._input=Y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-te),this.offset-=te;var $=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),L.length-1&&(this.yylineno-=L.length-1);var R=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:L?(L.length===$.length?this.yylloc.first_column:0)+$[$.length-L.length].length-L[0].length:this.yylloc.first_column-te},this.options.ranges&&(this.yylloc.range=[R[0],R[0]+this.yyleng-te]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(Y){this.unput(this.match.slice(Y))},pastInput:function(){var Y=this.matched.substr(0,this.matched.length-this.match.length);return(Y.length>20?"...":"")+Y.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var Y=this.match;return Y.length<20&&(Y+=this._input.substr(0,20-Y.length)),(Y.substr(0,20)+(Y.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var Y=this.pastInput(),te=new Array(Y.length+1).join("-");return Y+this.upcomingInput()+`
`+te+"^"},test_match:function(Y,te){var L,$,R;if(this.options.backtrack_lexer&&(R={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(R.yylloc.range=this.yylloc.range.slice(0))),$=Y[0].match(/(?:\r\n?|\n).*/g),$&&(this.yylineno+=$.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:$?$[$.length-1].length-$[$.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+Y[0].length},this.yytext+=Y[0],this.match+=Y[0],this.matches=Y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(Y[0].length),this.matched+=Y[0],L=this.performAction.call(this,this.yy,this,te,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),L)return L;if(this._backtrack){for(var W in R)this[W]=R[W];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var Y,te,L,$;this._more||(this.yytext="",this.match="");for(var R=this._currentRules(),W=0;W<R.length;W++)if(L=this._input.match(this.rules[R[W]]),L&&(!te||L[0].length>te[0].length)){if(te=L,$=W,this.options.backtrack_lexer){if(Y=this.test_match(L,R[W]),Y!==!1)return Y;if(this._backtrack){te=!1;continue}else return!1}else if(!this.options.flex)break}return te?(Y=this.test_match(te,R[$]),Y!==!1?Y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var te=this.next();return te||this.lex()},begin:function(te){this.conditionStack.push(te)},popState:function(){var te=this.conditionStack.length-1;return te>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(te){return te=this.conditionStack.length-1-Math.abs(te||0),te>=0?this.conditionStack[te]:"INITIAL"},pushState:function(te){this.begin(te)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(te,L,$,R){switch($){case 0:return te.getLogger().trace("Found comment",L.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:this.popState();break;case 5:te.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return te.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:te.getLogger().trace("end icon"),this.popState();break;case 10:return te.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return te.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return te.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return te.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:return this.begin("NODE"),19;case 15:return this.begin("NODE"),19;case 16:return this.begin("NODE"),19;case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:te.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return te.getLogger().trace("description:",L.yytext),"NODE_DESCR";case 26:this.popState();break;case 27:return this.popState(),te.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),te.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),te.getLogger().trace("node end ...",L.yytext),"NODE_DEND";case 30:return this.popState(),te.getLogger().trace("node end (("),"NODE_DEND";case 31:return this.popState(),te.getLogger().trace("node end (-"),"NODE_DEND";case 32:return this.popState(),te.getLogger().trace("node end (-"),"NODE_DEND";case 33:return this.popState(),te.getLogger().trace("node end (("),"NODE_DEND";case 34:return this.popState(),te.getLogger().trace("node end (("),"NODE_DEND";case 35:return te.getLogger().trace("Long description:",L.yytext),20;case 36:return te.getLogger().trace("Long description:",L.yytext),20}},rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return j}();ne.lexer=oe;function J(){this.yy={}}return J.prototype=ne,ne.Parser=J,new J}();Zi.parser=Zi;const Mp=Zi,on=pe=>Ep(pe,sn());let jt=[],Xu=0,Qi={};const Ip=()=>{jt=[],Xu=0,Qi={}},Rp=function(pe){for(let le=jt.length-1;le>=0;le--)if(jt[le].level<pe)return jt[le];return null},kp=()=>jt.length>0?jt[0]:null,Pp=(pe,le,ee,ce)=>{qr.info("addNode",pe,le,ee,ce);const H=sn(),O={id:Xu++,nodeId:on(le),level:pe,descr:on(ee),type:ce,children:[],width:sn().mindmap.maxNodeWidth};switch(O.type){case mt.ROUNDED_RECT:O.padding=2*H.mindmap.padding;break;case mt.RECT:O.padding=2*H.mindmap.padding;break;case mt.HEXAGON:O.padding=2*H.mindmap.padding;break;default:O.padding=H.mindmap.padding}const T=Rp(pe);if(T)T.children.push(O),jt.push(O);else if(jt.length===0)jt.push(O);else{let w=new Error('There can be only one root. No parent could be found for ("'+O.descr+'")');throw w.hash={text:"branch "+name,token:"branch "+name,line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:['"checkout '+name+'"']},w}},mt={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Bp=(pe,le)=>{switch(qr.debug("In get type",pe,le),pe){case"[":return mt.RECT;case"(":return le===")"?mt.ROUNDED_RECT:mt.CLOUD;case"((":return mt.CIRCLE;case")":return mt.CLOUD;case"))":return mt.BANG;case"{{":return mt.HEXAGON;default:return mt.DEFAULT}},Wu=(pe,le)=>{Qi[pe]=le},Fp=pe=>{const le=jt[jt.length-1];pe&&pe.icon&&(le.icon=on(pe.icon)),pe&&pe.class&&(le.class=on(pe.class))},Kr=pe=>{switch(pe){case mt.DEFAULT:return"no-border";case mt.RECT:return"rect";case mt.ROUNDED_RECT:return"rounded-rect";case mt.CIRCLE:return"circle";case mt.CLOUD:return"cloud";case mt.BANG:return"bang";case mt.HEXAGON:return"hexgon";default:return"no-border"}};let qu;const zp=pe=>{qu=pe},Gp=()=>qr,$p=pe=>jt[pe],Ji=pe=>Qi[pe],Vp=Object.freeze(Object.defineProperty({__proto__:null,addNode:Pp,clear:Ip,decorateNode:Fp,getElementById:Ji,getLogger:Gp,getMindmap:kp,getNodeById:$p,getType:Bp,nodeType:mt,get parseError(){return qu},sanitizeText:on,setElementForId:Wu,setErrorHandler:zp,type2Str:Kr},Symbol.toStringTag,{value:"Module"})),Ku=12,_p=function(pe,le,ee){pe.append("path").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("d",`M0 ${le.height-5} v${-le.height+2*5} q0,-5 5,-5 h${le.width-2*5} q5,0 5,5 v${le.height-5} H0 Z`),pe.append("line").attr("class","node-line-"+ee).attr("x1",0).attr("y1",le.height).attr("x2",le.width).attr("y2",le.height)},Up=function(pe,le){pe.append("rect").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("height",le.height).attr("width",le.width)},Yp=function(pe,le){const ee=le.width,ce=le.height,H=.15*ee,O=.25*ee,T=.35*ee,w=.2*ee;pe.append("path").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("d",`M0 0 a${H},${H} 0 0,1 ${ee*.25},${-1*ee*.1}
      a${T},${T} 1 0,1 ${ee*.4},${-1*ee*.1}
      a${O},${O} 1 0,1 ${ee*.35},${1*ee*.2}

      a${H},${H} 1 0,1 ${ee*.15},${1*ce*.35}
      a${w},${w} 1 0,1 ${-1*ee*.15},${1*ce*.65}

      a${O},${H} 1 0,1 ${-1*ee*.25},${ee*.15}
      a${T},${T} 1 0,1 ${-1*ee*.5},0
      a${H},${H} 1 0,1 ${-1*ee*.25},${-1*ee*.15}

      a${H},${H} 1 0,1 ${-1*ee*.1},${-1*ce*.35}
      a${w},${w} 1 0,1 ${ee*.1},${-1*ce*.65}

    H0 V0 Z`)},Hp=function(pe,le){const ee=le.width,ce=le.height,H=.15*ee;pe.append("path").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("d",`M0 0 a${H},${H} 1 0,0 ${ee*.25},${-1*ce*.1}
      a${H},${H} 1 0,0 ${ee*.25},0
      a${H},${H} 1 0,0 ${ee*.25},0
      a${H},${H} 1 0,0 ${ee*.25},${1*ce*.1}

      a${H},${H} 1 0,0 ${ee*.15},${1*ce*.33}
      a${H*.8},${H*.8} 1 0,0 0,${1*ce*.34}
      a${H},${H} 1 0,0 ${-1*ee*.15},${1*ce*.33}

      a${H},${H} 1 0,0 ${-1*ee*.25},${ce*.15}
      a${H},${H} 1 0,0 ${-1*ee*.25},0
      a${H},${H} 1 0,0 ${-1*ee*.25},0
      a${H},${H} 1 0,0 ${-1*ee*.25},${-1*ce*.15}

      a${H},${H} 1 0,0 ${-1*ee*.1},${-1*ce*.33}
      a${H*.8},${H*.8} 1 0,0 0,${-1*ce*.34}
      a${H},${H} 1 0,0 ${ee*.1},${-1*ce*.33}

    H0 V0 Z`)},Xp=function(pe,le){pe.append("circle").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("r",le.width/2)};function Wp(pe,le,ee,ce,H){return pe.insert("polygon",":first-child").attr("points",ce.map(function(O){return O.x+","+O.y}).join(" ")).attr("transform","translate("+(H.width-le)/2+", "+ee+")")}const qp=function(pe,le){const ee=le.height,H=ee/4,O=le.width-le.padding+2*H,T=[{x:H,y:0},{x:O-H,y:0},{x:O,y:-ee/2},{x:O-H,y:-ee},{x:H,y:-ee},{x:0,y:-ee/2}];Wp(pe,O,ee,T,le)},Kp=function(pe,le){pe.append("rect").attr("id","node-"+le.id).attr("class","node-bkg node-"+Kr(le.type)).attr("height",le.height).attr("rx",le.padding).attr("ry",le.padding).attr("width",le.width)},Zp=function(pe,le,ee,ce){const H=ce.htmlLabels,O=ee%(Ku-1),T=pe.append("g");le.section=O;let w="section-"+O;O<0&&(w+=" section-root"),T.attr("class",(le.class?le.class+" ":"")+"mindmap-node "+w);const S=T.append("g"),G=T.append("g"),U=le.descr.replace(/(<br\/*>)/g,`
`);Dp(G,U,{useHtmlLabels:H,width:le.width,classes:"mindmap-node-label"}),H||G.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const P=G.node().getBBox(),K=ce.fontSize.replace?ce.fontSize.replace("px",""):ce.fontSize;if(le.height=P.height+K*1.1*.5+le.padding,le.width=P.width+2*le.padding,le.icon)if(le.type===mt.CIRCLE)le.height+=50,le.width+=50,T.append("foreignObject").attr("height","50px").attr("width",le.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+O+" "+le.icon),G.attr("transform","translate("+le.width/2+", "+(le.height/2-1.5*le.padding)+")");else{le.width+=50;const D=le.height;le.height=Math.max(D,60);const V=Math.abs(le.height-D);T.append("foreignObject").attr("width","60px").attr("height",le.height).attr("style","text-align: center;margin-top:"+V/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+O+" "+le.icon),G.attr("transform","translate("+(25+le.width/2)+", "+(V/2+le.padding/2)+")")}else if(H){const D=(le.width-P.width)/2,V=(le.height-P.height)/2;G.attr("transform","translate("+D+", "+V+")")}else{const D=le.width/2,V=le.padding/2;G.attr("transform","translate("+D+", "+V+")")}switch(le.type){case mt.DEFAULT:_p(S,le,O);break;case mt.ROUNDED_RECT:Kp(S,le);break;case mt.RECT:Up(S,le);break;case mt.CIRCLE:S.attr("transform","translate("+le.width/2+", "+ +le.height/2+")"),Xp(S,le);break;case mt.CLOUD:Yp(S,le);break;case mt.BANG:Hp(S,le);break;case mt.HEXAGON:qp(S,le);break}return Wu(le.id,T),le.height},Qp=function(le,ee,ce,H,O){const T=O%(Ku-1),w=ce.x+ce.width/2,S=ce.y+ce.height/2,G=ee.x+ee.width/2,U=ee.y+ee.height/2,P=G>w?w+Math.abs(w-G)/2:w-Math.abs(w-G)/2,K=U>S?S+Math.abs(S-U)/2:S-Math.abs(S-U)/2,D=G>w?Math.abs(w-P)/2+w:-Math.abs(w-P)/2+w,V=U>S?Math.abs(S-K)/2+S:-Math.abs(S-K)/2+S;le.append("path").attr("d",ce.direction==="TB"||ce.direction==="BT"?`M${w},${S} Q${w},${V} ${P},${K} T${G},${U}`:`M${w},${S} Q${D},${S} ${P},${K} T${G},${U}`).attr("class","edge section-edge-"+T+" edge-depth-"+H)},Jp=function(pe){const le=Ji(pe.id),ee=pe.x||0,ce=pe.y||0;le.attr("transform","translate("+ee+","+ce+")")},Zu={drawNode:Zp,positionNode:Jp,drawEdge:Qp};Yu.use(Np);function Qu(pe,le,ee,ce){Zu.drawNode(pe,le,ee,ce),le.children&&le.children.forEach((H,O)=>{Qu(pe,H,ee<0?O:ee,ce)})}function jp(pe,le){le.edges().map((ee,ce)=>{const H=ee.data();if(ee[0]._private.bodyBounds){const O=ee[0]._private.rscratch;qr.trace("Edge: ",ce,H),pe.insert("path").attr("d",`M ${O.startX},${O.startY} L ${O.midX},${O.midY} L${O.endX},${O.endY} `).attr("class","edge section-edge-"+H.section+" edge-depth-"+H.depth)}})}function Ju(pe,le,ee,ce){le.add({group:"nodes",data:{id:pe.id,labelText:pe.descr,height:pe.height,width:pe.width,level:ce,nodeId:pe.id,padding:pe.padding,type:pe.type},position:{x:pe.x,y:pe.y}}),pe.children&&pe.children.forEach(H=>{Ju(H,le,ee,ce+1),le.add({group:"edges",data:{id:`${pe.id}_${H.id}`,source:pe.id,target:H.id,depth:ce,section:H.section}})})}function ey(pe,le){return new Promise(ee=>{const ce=ti("body").append("div").attr("id","cy").attr("style","display:none"),H=Yu({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});ce.remove(),Ju(pe,H,le,0),H.nodes().forEach(function(O){O.layoutDimensions=()=>{const T=O.data();return{w:T.width,h:T.height}}}),H.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),H.ready(O=>{qr.info("Ready",O),ee(H)})})}function ty(pe){pe.nodes().map((le,ee)=>{const ce=le.data();ce.x=le.position().x,ce.y=le.position().y,Zu.positionNode(ce);const H=Ji(ce.nodeId);qr.info("Id:",ee,"Position: (",le.position().x,", ",le.position().y,")",ce),H.attr("transform",`translate(${le.position().x-ce.width/2}, ${le.position().y-ce.height/2})`),H.attr("attr",`apa-${ee})`)})}const ry=async(pe,le,ee,ce)=>{const H=sn();H.htmlLabels=!1,qr.debug(`Rendering mindmap diagram
`+pe,ce.parser);const O=sn().securityLevel;let T;O==="sandbox"&&(T=ti("#i"+le));const S=(O==="sandbox"?ti(T.nodes()[0].contentDocument.body):ti("body")).select("#"+le);S.append("g");const G=ce.db.getMindmap(),U=S.append("g");U.attr("class","mindmap-edges");const P=S.append("g");P.attr("class","mindmap-nodes"),Qu(P,G,-1,H);const K=await ey(G,H);jp(U,K),ty(K),wp(void 0,S,H.mindmap.padding,H.mindmap.useMaxWidth)},ay={draw:ry},ny=pe=>{let le="";for(let ee=0;ee<pe.THEME_COLOR_LIMIT;ee++)pe["lineColor"+ee]=pe["lineColor"+ee]||pe["cScaleInv"+ee],xp(pe["lineColor"+ee])?pe["lineColor"+ee]=Tp(pe["lineColor"+ee],20):pe["lineColor"+ee]=Cp(pe["lineColor"+ee],20);for(let ee=0;ee<pe.THEME_COLOR_LIMIT;ee++){const ce=""+(17-3*ee);le+=`
    .section-${ee-1} rect, .section-${ee-1} path, .section-${ee-1} circle, .section-${ee-1} polygon, .section-${ee-1} path  {
      fill: ${pe["cScale"+ee]};
    }
    .section-${ee-1} text {
     fill: ${pe["cScaleLabel"+ee]};
    }
    .node-icon-${ee-1} {
      font-size: 40px;
      color: ${pe["cScaleLabel"+ee]};
    }
    .section-edge-${ee-1}{
      stroke: ${pe["cScale"+ee]};
    }
    .edge-depth-${ee-1}{
      stroke-width: ${ce};
    }
    .section-${ee-1} line {
      stroke: ${pe["cScaleInv"+ee]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return le},iy=pe=>`
  .edge {
    stroke-width: 3;
  }
  ${ny(pe)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${pe.git0};
  }
  .section-root text {
    fill: ${pe.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,sy=iy,ly={db:Vp,renderer:ay,parser:Mp,styles:sy};export{ly as diagram};
