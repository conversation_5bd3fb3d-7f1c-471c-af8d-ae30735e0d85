// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/system/dao/internal"
)

// sysLoginLogDao is the data access object for table sys_login_log.
// You can define custom methods on it to extend its functionality as you wish.
type sysLoginLogDao struct {
	*internal.SysLoginLogDao
}

var (
	// SysLoginLog is globally public accessible object for table sys_login_log operations.
	SysLoginLog = sysLoginLogDao{
		internal.NewSysLoginLogDao(),
	}
)

// Fill with you ideas below.
