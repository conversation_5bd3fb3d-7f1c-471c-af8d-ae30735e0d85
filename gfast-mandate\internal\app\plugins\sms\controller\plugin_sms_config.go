// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2023-12-01 14:49:39
// 生成路径: internal/app/plugins/sms/controller/plugin_sms_config.go
// 生成人：gfast
// desc:短信配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/plugins/sms"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type pluginSmsConfigController struct {
	systemController.BaseController
}

var PluginSmsConfig = new(pluginSmsConfigController)

// List 列表
func (c *pluginSmsConfigController) List(ctx context.Context, req *sms.PluginSmsConfigSearchReq) (res *sms.PluginSmsConfigSearchRes, err error) {
	res = new(sms.PluginSmsConfigSearchRes)
	res.PluginSmsConfigSearchRes, err = service.PluginSmsConfig().List(ctx, &req.PluginSmsConfigSearchReq)
	return
}

// Get 获取短信配置
func (c *pluginSmsConfigController) Get(ctx context.Context, req *sms.PluginSmsConfigGetReq) (res *sms.PluginSmsConfigGetRes, err error) {
	res = new(sms.PluginSmsConfigGetRes)
	res.PluginSmsConfigInfoRes, err = service.PluginSmsConfig().GetById(ctx, req.Id)
	return
}

// Add 添加短信配置
func (c *pluginSmsConfigController) Add(ctx context.Context, req *sms.PluginSmsConfigAddReq) (res *sms.PluginSmsConfigAddRes, err error) {
	err = service.PluginSmsConfig().Add(ctx, req.PluginSmsConfigAddReq)
	return
}

// Edit 修改短信配置
func (c *pluginSmsConfigController) Edit(ctx context.Context, req *sms.PluginSmsConfigEditReq) (res *sms.PluginSmsConfigEditRes, err error) {
	err = service.PluginSmsConfig().Edit(ctx, req.PluginSmsConfigEditReq)
	return
}

// Delete 删除短信配置
func (c *pluginSmsConfigController) Delete(ctx context.Context, req *sms.PluginSmsConfigDeleteReq) (res *sms.PluginSmsConfigDeleteRes, err error) {
	err = service.PluginSmsConfig().Delete(ctx, req.Ids)
	return
}

func (c *pluginSmsConfigController) ChangeStatus(ctx context.Context, req *sms.PluginsSmsConfigChangeStatusReq) (res *sms.PluginsSmsConfigChangeStatusRes, err error) {
	err = service.PluginSmsConfig().ChangeStatus(ctx, req.Id, req.Status)
	return
}
