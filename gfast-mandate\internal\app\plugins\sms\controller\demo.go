/*
* @desc:短信发送测试
* @company:云南奇讯科技有限公司
* @Author: y<PERSON><PERSON><PERSON><PERSON><<EMAIL>>
* @Date:   2023/12/5 17:32
 */

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/plugins/sms"
	"github.com/tiger1103/gfast/v3/internal/app/plugins/sms/driver"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

var SmsDemo = new(smsDemoController)

type smsDemoController struct {
	systemController.BaseController
}

func (c *smsDemoController) SendVerify(ctx context.Context, req *sms.SendVerifyReq) (res *sms.SendVerifyRes, err error) {
	err = driver.CommonSms.SendVerifyCode(&driver.SendSmsArgs{
		Mobiles: []string{req.Mobile},
		Ctx:     ctx,
	})
	return
}

func (c *smsDemoController) SendContent(ctx context.Context, req *sms.SendContentReq) (res *sms.SendContentRes, err error) {
	err = driver.CommonSms.SendTemplateSms(&driver.SendSmsArgs{
		Mobiles: req.Mobile,
		Params:  req.Params,
		Ctx:     ctx,
	})
	return
}

func (c *smsDemoController) CheckVerifyCode(ctx context.Context, req *sms.CheckVerifyReq) (res *sms.CheckVerifyRes, err error) {
	err = driver.CommonSms.CheckVerifyCode(ctx, req.Mobile, req.Code)
	return
}
