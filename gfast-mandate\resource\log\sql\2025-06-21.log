2025-06-21T10:04:29.991+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [220 ms] [default] [gfast] [rows:29 ] SHOW TABLES
2025-06-21T10:04:30.079+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 88 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-21T10:04:30.101+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 22 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-21T10:04:30.124+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 22 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-21T10:06:01.255+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 60 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-21T10:06:01.277+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 15 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:06:01.295+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 15 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 10:06:01' WHERE `id`=31
2025-06-21T10:06:01.348+08:00 [DEBU] {78396c509cec4a1810c6d86b85dfb2e7} [ 43 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-21T10:06:01.360+08:00 [DEBU] {78396c509cec4a1810c6d86b85dfb2e7} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 10:06:01','系统后台') 
2025-06-21T10:07:37.058+08:00 [DEBU] {9c0bc26bb2ec4a1817c6d86b6a766b5f} [692 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:07:37.108+08:00 [DEBU] {9c0bc26bb2ec4a1817c6d86b6a766b5f} [ 44 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 10:07:37' WHERE `id`=31
2025-06-21T10:07:37.192+08:00 [DEBU] {a4f36b9fb2ec4a1818c6d86b59838160} [ 49 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 10:07:37','系统后台') 
2025-06-21T10:10:05.331+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [ 27 ms] [default] [gfast] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-06-21T10:10:05.332+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:10:05.343+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:10:05.353+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  8 ms] [default] [gfast] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-06-21T10:10:05.359+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:10:05','在线用户定时更新，执行成功') 
2025-06-21T10:18:18.515+08:00 [DEBU] {c4799ef447ed4a18a2a5c57f5b0e94ea} [ 25 ms] [default] [gfast] [rows:29 ] SHOW TABLES
2025-06-21T10:18:18.544+08:00 [DEBU] {c4799ef447ed4a18a2a5c57f5b0e94ea} [ 28 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-21T10:18:18.547+08:00 [DEBU] {c4799ef447ed4a18a2a5c57f5b0e94ea} [  2 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-21T10:18:18.555+08:00 [DEBU] {c4799ef447ed4a18a2a5c57f5b0e94ea} [  7 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-21T10:18:30.565+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 20 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-21T10:18:30.567+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:18:30.577+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 10 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 10:18:30' WHERE `id`=31
2025-06-21T10:18:30.599+08:00 [DEBU] {94b21cc54aed4a18a8a5c57f0efda74b} [ 22 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-21T10:18:30.614+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 15 ms] [default] [gfast] [rows:11 ] SHOW FULL COLUMNS FROM `sys_role`
2025-06-21T10:18:30.625+08:00 [DEBU] {94b21cc54aed4a18a8a5c57f0efda74b} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 10:18:30','系统后台') 
2025-06-21T10:18:30.643+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 28 ms] [default] [gfast] [rows:7  ] SELECT `id`,`pid`,`status`,`list_order`,`name`,`remark`,`data_scope`,`created_at`,`updated_at`,`created_by`,`effectiveTime` FROM `sys_role` ORDER BY `list_order` asc,`id` asc
2025-06-21T10:18:30.658+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 14 ms] [default] [gfast] [rows:7  ] SHOW FULL COLUMNS FROM `casbin_rule`
2025-06-21T10:18:30.661+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [  3 ms] [default] [gfast] [rows:98 ] SELECT `ptype`,`v0`,`v1`,`v2`,`v3`,`v4`,`v5` FROM `casbin_rule`
2025-06-21T10:18:30.676+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [ 14 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_auth_rule`
2025-06-21T10:18:30.681+08:00 [DEBU] {d0712dc34aed4a18a7a5c57f86f5ded0} [  5 ms] [default] [gfast] [rows:57 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-06-21T10:18:30.691+08:00 [DEBU] {481875cb4aed4a18a9a5c57f20042cd0} [  8 ms] [default] [gfast] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-06-21T10:18:30.696+08:00 [DEBU] {481875cb4aed4a18a9a5c57f20042cd0} [  4 ms] [default] [gfast] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow==' LIMIT 1
2025-06-21T10:18:30.702+08:00 [DEBU] {481875cb4aed4a18a9a5c57f20042cd0} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('4e26f102d3b8906db51aa563df49f871','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow==','2025-06-21 10:18:30','demo','::1','Chrome','Windows 10') 
2025-06-21T10:20:05.540+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 15 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:20:05.541+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:20:05.541+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:20:05.543+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T10:20:05.555+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 12 ms] [default] [gfast] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-06-21T10:20:05.558+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:20:05','在线用户定时更新，执行成功') 
2025-06-21T10:23:22.971+08:00 [DEBU] {408fc9d78eed4a18aca5c57f44590f02} [ 23 ms] [default] [gfast] [rows:24 ] SHOW FULL COLUMNS FROM `tools_gen_table`
2025-06-21T10:23:22.975+08:00 [DEBU] {408fc9d78eed4a18aca5c57f44590f02} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `tools_gen_table`
2025-06-21T10:23:22.981+08:00 [DEBU] {408fc9d78eed4a18aca5c57f44590f02} [  5 ms] [default] [gfast] [rows:9  ] SELECT `table_id`,`table_name`,`table_comment`,`class_name`,`tpl_category`,`package_name`,`module_name`,`business_name`,`function_name`,`function_author`,`options`,`create_time`,`update_time`,`remark`,`overwrite`,`sort_column`,`sort_type`,`show_detail`,`excel_port`,`excel_imp`,`use_snow_id`,`use_virtual`,`overwrite_info`,`menu_pid` FROM `tools_gen_table` ORDER BY `table_id` asc LIMIT 0,10
2025-06-21T10:23:22.995+08:00 [DEBU] {1c92e3d98eed4a18ada5c57f03e8304d} [ 11 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dept`
2025-06-21T10:23:23.000+08:00 [DEBU] {1c92e3d98eed4a18ada5c57f03e8304d} [  4 ms] [default] [gfast] [rows:14 ] SELECT `dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_dept` WHERE `deleted_at` IS NULL
2025-06-21T10:23:23.012+08:00 [DEBU] {1c92e3d98eed4a18ada5c57f03e8304d} [ 11 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_oper_log`
2025-06-21T10:23:23.019+08:00 [DEBU] {1c92e3d98eed4a18ada5c57f03e8304d} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('代码生成','/api/v1/system/tools/gen/tableList','GET',1,'demo','财务部门','/api/v1/system/tools/gen/tableList?tableName=&tableComment=&pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10","tableComment":"","tableName":""}','2025-06-21 10:23:23') 
2025-06-21T10:23:39.324+08:00 [DEBU] {fca4c4a792ed4a18afa5c57fd2bcf53b} [  1 ms] [default] [gfast] [rows:1  ] SELECT `table_id`,`table_name`,`table_comment`,`class_name`,`tpl_category`,`package_name`,`module_name`,`business_name`,`function_name`,`function_author`,`options`,`create_time`,`update_time`,`remark`,`overwrite`,`sort_column`,`sort_type`,`show_detail`,`excel_port`,`excel_imp`,`use_snow_id`,`use_virtual`,`overwrite_info`,`menu_pid` FROM `tools_gen_table` WHERE `table_id`=92 LIMIT 1
2025-06-21T10:23:39.333+08:00 [DEBU] {fca4c4a792ed4a18afa5c57fd2bcf53b} [  8 ms] [default] [gfast] [rows:39 ] SHOW FULL COLUMNS FROM `tools_gen_table_column`
2025-06-21T10:23:39.336+08:00 [DEBU] {fca4c4a792ed4a18afa5c57fd2bcf53b} [  2 ms] [default] [gfast] [rows:2  ] SELECT `column_id`,`table_id`,`column_name`,`column_comment`,`column_type`,`go_type`,`ts_type`,`go_field`,`html_field`,`is_pk`,`is_increment`,`is_required`,`is_edit`,`is_list`,`is_detail`,`is_query`,`sort_order_edit`,`sort_order_list`,`sort_order_detail`,`sort_order_query`,`query_type`,`html_type`,`dict_type`,`link_table_name`,`link_table_class`,`link_table_module_name`,`link_table_business_name`,`link_table_package`,`link_label_id`,`link_label_name`,`col_span`,`row_span`,`is_row_start`,`min_width`,`is_fixed`,`is_overflow_tooltip`,`is_cascade`,`parent_column_name`,`cascade_column_name` FROM `tools_gen_table_column` WHERE `table_id`=92 ORDER BY `sort_order_edit` asc,`column_id` asc
2025-06-21T10:23:39.724+08:00 [DEBU] {a0aa7bbf92ed4a18b0a5c57f36b9395c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/tools/gen/preview','GET',1,'demo','财务部门','/api/v1/system/tools/gen/preview?tableId=92','::1','内网IP','{"tableId":"92"}','2025-06-21 10:23:39') 
2025-06-21T10:29:36.224+08:00 [DEBU] {787c23c0e5ed4a18b2a5c57f529df042} [ 10 ms] [default] [gfast] [rows:1  ] SELECT `table_id`,`table_name`,`table_comment`,`class_name`,`tpl_category`,`package_name`,`module_name`,`business_name`,`function_name`,`function_author`,`options`,`create_time`,`update_time`,`remark`,`overwrite`,`sort_column`,`sort_type`,`show_detail`,`excel_port`,`excel_imp`,`use_snow_id`,`use_virtual`,`overwrite_info`,`menu_pid` FROM `tools_gen_table` WHERE `table_id`=96 LIMIT 1
2025-06-21T10:29:36.234+08:00 [DEBU] {787c23c0e5ed4a18b2a5c57f529df042} [  7 ms] [default] [gfast] [rows:9  ] SELECT `column_id`,`table_id`,`column_name`,`column_comment`,`column_type`,`go_type`,`ts_type`,`go_field`,`html_field`,`is_pk`,`is_increment`,`is_required`,`is_edit`,`is_list`,`is_detail`,`is_query`,`sort_order_edit`,`sort_order_list`,`sort_order_detail`,`sort_order_query`,`query_type`,`html_type`,`dict_type`,`link_table_name`,`link_table_class`,`link_table_module_name`,`link_table_business_name`,`link_table_package`,`link_label_id`,`link_label_name`,`col_span`,`row_span`,`is_row_start`,`min_width`,`is_fixed`,`is_overflow_tooltip`,`is_cascade`,`parent_column_name`,`cascade_column_name` FROM `tools_gen_table_column` WHERE `table_id`=96 ORDER BY `sort_order_edit` asc,`column_id` asc
2025-06-21T10:29:36.675+08:00 [DEBU] {e462dedae5ed4a18b3a5c57f2bbd5d6f} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/tools/gen/preview','GET',1,'demo','财务部门','/api/v1/system/tools/gen/preview?tableId=96','::1','内网IP','{"tableId":"96"}','2025-06-21 10:29:36') 
2025-06-21T10:29:54.113+08:00 [DEBU] {88c069eae9ed4a18b7a5c57fc1bafd7e} [ 10 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_dict_type`
2025-06-21T10:29:54.115+08:00 [DEBU] {88c069eae9ed4a18b7a5c57fc1bafd7e} [  2 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_job_group') AND (`status`=1) LIMIT 1
2025-06-21T10:29:54.124+08:00 [DEBU] {88c069eae9ed4a18b7a5c57fc1bafd7e} [  8 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dict_data`
2025-06-21T10:29:54.142+08:00 [DEBU] {c0278debe9ed4a18bba5c57fe50fb30a} [ 18 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_job`
2025-06-21T10:29:54.143+08:00 [DEBU] {c0278debe9ed4a18bba5c57fe50fb30a} [  1 ms] [default] [gfast] [rows:3  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` ORDER BY `job_id` asc LIMIT 0,10
2025-06-21T10:29:54.149+08:00 [DEBU] {88c069eae9ed4a18b7a5c57fc1bafd7e} [ 25 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_job_group') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:29:54.155+08:00 [DEBU] {248095eae9ed4a18b8a5c57fb21a2487} [  5 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_job_policy') AND (`status`=1) LIMIT 1
2025-06-21T10:29:54.155+08:00 [DEBU] {c0278debe9ed4a18bba5c57fe50fb30a} [ 11 ms] [default] [gfast] [rows:2  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id` IN(1,2)) AND `deleted_at` IS NULL
2025-06-21T10:29:54.159+08:00 [DEBU] {248095eae9ed4a18b8a5c57fb21a2487} [  4 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_job_policy') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:29:54.159+08:00 [DEBU] {c0278debe9ed4a18bba5c57fe50fb30a} [  3 ms] [default] [gfast] [rows:2  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id` IN(31,1)) AND `deleted_at` IS NULL
2025-06-21T10:29:54.160+08:00 [DEBU] {248095eae9ed4a18b9a5c57fb373f7a8} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_job_status') AND (`status`=1) LIMIT 1
2025-06-21T10:29:54.163+08:00 [DEBU] {248095eae9ed4a18b9a5c57fb373f7a8} [  2 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_job_status') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:29:54.169+08:00 [DEBU] {********************************} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_job_status&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_job_status"}','2025-06-21 10:29:54') 
2025-06-21T10:29:54.173+08:00 [DEBU] {********************************} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_job_policy&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_job_policy"}','2025-06-21 10:29:54') 
2025-06-21T10:29:54.187+08:00 [DEBU] {ecfd19eee9ed4a18bca5c57ffc15f845} [ 19 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_job_group&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_job_group"}','2025-06-21 10:29:54') 
2025-06-21T10:29:54.212+08:00 [DEBU] {********************************} [ 44 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('定时任务','/api/v1/system/sysJob/list','GET',1,'demo','财务部门','/api/v1/system/sysJob/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:29:54') 
2025-06-21T10:30:05.230+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  5 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:30:05.233+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:30:05.234+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:30:05.234+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T10:30:05.244+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:30:05','在线用户定时更新，执行成功') 
2025-06-21T10:30:55.742+08:00 [DEBU] {fc93ce43f8ed4a18c5a5c57f5aac5a3b} [ 10 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_upload_drive') AND (`status`=1) LIMIT 1
2025-06-21T10:30:55.750+08:00 [DEBU] {fc93ce43f8ed4a18c5a5c57f5aac5a3b} [  7 ms] [default] [gfast] [rows:4  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_upload_drive') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:30:55.752+08:00 [DEBU] {fc93ce43f8ed4a18c6a5c57ff257f324} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_upload_file_type') AND (`status`=1) LIMIT 1
2025-06-21T10:30:55.754+08:00 [DEBU] {fc93ce43f8ed4a18c6a5c57ff257f324} [  1 ms] [default] [gfast] [rows:6  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_upload_file_type') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:30:55.773+08:00 [DEBU] {7c032945f8ed4a18c8a5c57fc3894553} [ 18 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_attachment`
2025-06-21T10:30:55.774+08:00 [DEBU] {7c032945f8ed4a18c8a5c57fc3894553} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:30:55.775+08:00 [DEBU] {7c032945f8ed4a18c8a5c57fc3894553} [  1 ms] [default] [gfast] [rows:3  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:30:55.788+08:00 [DEBU] {ac518146f8ed4a18cba5c57f9596cd2a} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:30:55') 
2025-06-21T10:30:55.792+08:00 [DEBU] {e0fb3a45f8ed4a18caa5c57f4f07e2ab} [ 18 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_upload_drive&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_upload_drive"}','2025-06-21 10:30:55') 
2025-06-21T10:30:55.794+08:00 [DEBU] {e0fb3a45f8ed4a18c9a5c57f8b403c1c} [ 20 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_upload_file_type&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_upload_file_type"}','2025-06-21 10:30:55') 
2025-06-21T10:31:04.113+08:00 [DEBU] {c0be1935faed4a18cca5c57f94b50820} [ 38 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:04.115+08:00 [DEBU] {c0be1935faed4a18cca5c57f94b50820} [  1 ms] [default] [gfast] [rows:3  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:04.129+08:00 [DEBU] {a0419637faed4a18cda5c57f56502b22} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:04') 
2025-06-21T10:31:15.211+08:00 [DEBU] {c837ccccfced4a18cfa5c57fc15fae3f} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:15.213+08:00 [DEBU] {c837ccccfced4a18cfa5c57fc15fae3f} [  1 ms] [default] [gfast] [rows:3  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:15.221+08:00 [DEBU] {8c2d2bcdfced4a18d0a5c57ff0695e03} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:15') 
2025-06-21T10:31:15.252+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  8 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_config`
2025-06-21T10:31:15.254+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  2 ms] [default] [gfast] [rows:1  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` WHERE `config_key`='sys.uploadFile.fileType' LIMIT 1
2025-06-21T10:31:15.255+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  1 ms] [default] [gfast] [rows:1  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` WHERE `config_key`='sys.uploadFile.fileSize' LIMIT 1
2025-06-21T10:31:15.265+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at` FROM `sys_attachment` WHERE `md5`='0900a8591ad4596c2118778a1e517d44' LIMIT 1
2025-06-21T10:31:15.266+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id`=0) AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:31:15.284+08:00 [DEBU] {b02041cdfced4a18d1a5c57f8a7afa03} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_attachment`(`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at`) VALUES('system',0,'1.png','image','image/png','upload_file/2025-06-21/darux19vj0e8irkfhe.png',2699711,'png','0900a8591ad4596c2118778a1e517d44',31,true,'2025-06-21 10:31:15','2025-06-21 10:31:15') 
2025-06-21T10:31:15.288+08:00 [DEBU] {786747d1fced4a18d2a5c57fa148d6de} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/upload/singleFile','POST',1,'demo','财务部门','/api/v1/system/upload/singleFile','::1','内网IP','{"file":{"Filename":"1.png","Header":{"Content-Disposition":["form-data; name=\"file\"; filename=\"1.png\""],"Content-Type":["image/png"]},"Size":2699711},"token":"7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow=="}','2025-06-21 10:31:15') 
2025-06-21T10:31:15.350+08:00 [DEBU] {945b1ad5fced4a18d3a5c57fecbdaa9c} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:15.352+08:00 [DEBU] {945b1ad5fced4a18d3a5c57fecbdaa9c} [  1 ms] [default] [gfast] [rows:4  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:15.360+08:00 [DEBU] {50b86fd5fced4a18d4a5c57f19b543f9} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:15') 
2025-06-21T10:31:34.051+08:00 [DEBU] {1c43482e01ee4a18d5a5c57fb7382aac} [ 26 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:34.053+08:00 [DEBU] {1c43482e01ee4a18d5a5c57fb7382aac} [  1 ms] [default] [gfast] [rows:4  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:34.074+08:00 [DEBU] {c83a163001ee4a18d6a5c57f9d261b60} [ 17 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:34') 
2025-06-21T10:31:47.826+08:00 [DEBU] {5ce46e6404ee4a18d7a5c57f6916d9a2} [  5 ms] [default] [gfast] [rows:0  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at` FROM `sys_attachment` WHERE `md5`='50e9dcc3465b52c03e7adf04b7b02a62' LIMIT 1
2025-06-21T10:31:47.834+08:00 [DEBU] {5ce46e6404ee4a18d7a5c57f6916d9a2} [  8 ms] [default] [gfast] [rows:0  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id`=0) AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:31:47.835+08:00 [DEBU] {7810a46404ee4a18d9a5c57f71fec119} [ 12 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:47.841+08:00 [DEBU] {7810a46404ee4a18d9a5c57f71fec119} [  5 ms] [default] [gfast] [rows:4  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:47.844+08:00 [DEBU] {e84ab36404ee4a18daa5c57f6fb4c77f} [ 18 ms] [default] [gfast] [rows:0  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at` FROM `sys_attachment` WHERE `md5`='1e8d77e76b614bfd1cfefa5a8fa644c7' LIMIT 1
2025-06-21T10:31:47.847+08:00 [DEBU] {5ce46e6404ee4a18d7a5c57f6916d9a2} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_attachment`(`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at`) VALUES('system',0,'56.jpg','image','image/jpeg','upload_file/2025-06-21/daruxg8gua8otplwso.jpg',70135,'jpg','50e9dcc3465b52c03e7adf04b7b02a62',31,true,'2025-06-21 10:31:47','2025-06-21 10:31:47') 
2025-06-21T10:31:47.848+08:00 [DEBU] {e84ab36404ee4a18daa5c57f6fb4c77f} [  4 ms] [default] [gfast] [rows:0  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id`=0) AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:31:47.856+08:00 [DEBU] {e84ab36404ee4a18daa5c57f6fb4c77f} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_attachment`(`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at`) VALUES('system',0,'640 (1).jpg','image','image/jpeg','upload_file/2025-06-21/daruxg8pexi8soy0yr.jpg',162953,'jpg','1e8d77e76b614bfd1cfefa5a8fa644c7',31,true,'2025-06-21 10:31:47','2025-06-21 10:31:47') 
2025-06-21T10:31:47.864+08:00 [DEBU] {f40a836404ee4a18d8a5c57fbad28196} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at` FROM `sys_attachment` WHERE `md5`='5dfbd024e4c49eed79add3772477e0be' LIMIT 1
2025-06-21T10:31:47.865+08:00 [DEBU] {f40a836404ee4a18d8a5c57fbad28196} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`user_nickname` FROM `sys_user` WHERE (`id`=0) AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:31:47.865+08:00 [DEBU] {e02b156604ee4a18dba5c57f8828ea9f} [ 18 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:47') 
2025-06-21T10:31:47.872+08:00 [DEBU] {bc55d76604ee4a18dca5c57fc4d5f7c0} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/upload/singleFile','POST',1,'demo','财务部门','/api/v1/system/upload/singleFile','::1','内网IP','{"file":{"Filename":"640 (1).jpg","Header":{"Content-Disposition":["form-data; name=\"file\"; filename=\"640 (1).jpg\""],"Content-Type":["image/jpeg"]},"Size":162953},"token":"7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow=="}','2025-06-21 10:31:47') 
2025-06-21T10:31:47.873+08:00 [DEBU] {cc49506704ee4a18dda5c57f1236fe7f} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/upload/singleFile','POST',1,'demo','财务部门','/api/v1/system/upload/singleFile','::1','内网IP','{"file":{"Filename":"56.jpg","Header":{"Content-Disposition":["form-data; name=\"file\"; filename=\"56.jpg\""],"Content-Type":["image/jpeg"]},"Size":70135},"token":"7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow=="}','2025-06-21 10:31:47') 
2025-06-21T10:31:47.877+08:00 [DEBU] {f40a836404ee4a18d8a5c57fbad28196} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_attachment`(`app_id`,`drive`,`name`,`kind`,`mime_type`,`path`,`size`,`ext`,`md5`,`created_by`,`status`,`created_at`,`updated_at`) VALUES('system',0,'70e5dda4-0bcd-4cca-84b1-dbc140ae7e64_1747298805063225037_origin~tplv-a9rns2rl98-image-dark-watermark.png','image','image/png','upload_file/2025-06-21/daruxg8z8c44dnvo2w.png',1858215,'png','5dfbd024e4c49eed79add3772477e0be',31,true,'2025-06-21 10:31:47','2025-06-21 10:31:47') 
2025-06-21T10:31:47.890+08:00 [DEBU] {f4a4fe6704ee4a18dea5c57f34dff774} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/upload/singleFile','POST',1,'demo','财务部门','/api/v1/system/upload/singleFile','::1','内网IP','{"file":{"Filename":"70e5dda4-0bcd-4cca-84b1-dbc140ae7e64_1747298805063225037_origin~tplv-a9rns2rl98-image-dark-watermark.png","Header":{"Content-Disposition":["form-data; name=\"file\"; filename=\"70e5dda4-0bcd-4cca-84b1-dbc140ae7e64_1747298805063225037_origin~tplv-a9rns2rl98-image-dark-watermark.png\""],"Content-Type":["image/png"]},"Size":1858215},"token":"7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow=="}','2025-06-21 10:31:47') 
2025-06-21T10:31:47.987+08:00 [DEBU] {348f646e04ee4a18dfa5c57f2fd65c09} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:47.989+08:00 [DEBU] {348f646e04ee4a18dfa5c57f2fd65c09} [  1 ms] [default] [gfast] [rows:7  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:47.996+08:00 [DEBU] {3421b96e04ee4a18e0a5c57f7aab9124} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:47') 
2025-06-21T10:31:48.017+08:00 [DEBU] {ec09217004ee4a18e1a5c57f74b3fd39} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:48.019+08:00 [DEBU] {ec09217004ee4a18e1a5c57f74b3fd39} [  1 ms] [default] [gfast] [rows:7  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:48.026+08:00 [DEBU] {3c867c7004ee4a18e2a5c57fa14b69e3} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:48') 
2025-06-21T10:31:48.080+08:00 [DEBU] {f422ed7304ee4a18e3a5c57fed007668} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_attachment`
2025-06-21T10:31:48.081+08:00 [DEBU] {f422ed7304ee4a18e3a5c57fed007668} [  1 ms] [default] [gfast] [rows:7  ] SELECT `id`,`app_id`,`drive`,`name`,`kind`,`path`,`size`,`ext`,`status`,`created_at`,`updated_at` FROM `sys_attachment` ORDER BY `updated_at` desc,`id` desc LIMIT 0,10
2025-06-21T10:31:48.087+08:00 [DEBU] {a86f377404ee4a18e4a5c57f6bb2dd2a} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('附件管理','/api/v1/system/sysAttachment/list','GET',1,'demo','财务部门','/api/v1/system/sysAttachment/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:31:48') 
2025-06-21T10:32:06.881+08:00 [DEBU] {60ca65d408ee4a18e7a5c57f807c1831} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:06') 
2025-06-21T10:32:11.828+08:00 [DEBU] {501747fb09ee4a18e9a5c57f231639e5} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:11') 
2025-06-21T10:32:16.827+08:00 [DEBU] {24a63b250bee4a18eba5c57f317e786a} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:16') 
2025-06-21T10:32:21.859+08:00 [DEBU] {b862514f0cee4a18eda5c57fa44c18ed} [ 34 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:21') 
2025-06-21T10:32:26.829+08:00 [DEBU] {680051790dee4a18efa5c57f478b02d8} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:26') 
2025-06-21T10:32:31.830+08:00 [DEBU] {30cc50a30eee4a18f1a5c57ff2e5f5ff} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:31') 
2025-06-21T10:32:36.839+08:00 [DEBU] {1c1fc3cd0fee4a18f4a5c57f1959c091} [  8 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='admin_login_status') AND (`status`=1) LIMIT 1
2025-06-21T10:32:36.848+08:00 [DEBU] {1c1fc3cd0fee4a18f4a5c57f1959c091} [  9 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='admin_login_status') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:32:36.853+08:00 [DEBU] {4cd7d5cd0fee4a18f5a5c57f9b169225} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:32:36') 
2025-06-21T10:32:36.856+08:00 [DEBU] {b81f25cf0fee4a18f8a5c57f6ecc90cc} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_login_log`
2025-06-21T10:32:36.858+08:00 [DEBU] {b81f25cf0fee4a18f8a5c57f6ecc90cc} [  2 ms] [default] [gfast] [rows:10 ] SELECT `info_id`,`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module` FROM `sys_login_log` ORDER BY `info_id` DESC LIMIT 0,10
2025-06-21T10:32:36.865+08:00 [DEBU] {146880cf0fee4a18f9a5c57f6e4cb53f} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('登录日志','/api/v1/system/loginLog/list','GET',1,'demo','财务部门','/api/v1/system/loginLog/list?pageNum=1&pageSize=10&status=&ipaddr=&loginLocation=&userName=','::1','内网IP','{"ipaddr":"","loginLocation":"","pageNum":"1","pageSize":"10","status":"","userName":""}','2025-06-21 10:32:36') 
2025-06-21T10:32:36.866+08:00 [DEBU] {80c8f0ce0fee4a18f7a5c57fd9023e9b} [ 15 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=admin_login_status&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"admin_login_status"}','2025-06-21 10:32:36') 
2025-06-21T10:32:49.089+08:00 [DEBU] {58461ea812ee4a18fba5c57f063496d1} [  4 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_oper_log_type') AND (`status`=1) LIMIT 1
2025-06-21T10:32:49.094+08:00 [DEBU] {58461ea812ee4a18fba5c57f063496d1} [  5 ms] [default] [gfast] [rows:4  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_oper_log_type') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T10:32:49.100+08:00 [DEBU] {7081cfa812ee4a18fca5c57ff7d3e71c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_oper_log_type&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_oper_log_type"}','2025-06-21 10:32:49') 
2025-06-21T10:32:49.121+08:00 [DEBU] {58a086a912ee4a18fea5c57f84b6cd74} [ 13 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_oper_log`
2025-06-21T10:32:49.123+08:00 [DEBU] {58a086a912ee4a18fea5c57f84b6cd74} [  0 ms] [default] [gfast] [rows:10 ] SELECT `oper_id`,`title`,`business_type`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`error_msg`,`oper_time` FROM `sys_oper_log` ORDER BY `oper_id` DESC LIMIT 0,10
2025-06-21T10:32:49.129+08:00 [DEBU] {6c2b8caa12ee4a18ffa5c57f4e3330f1} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('操作日志','/api/v1/system/operLog/list','GET',1,'demo','财务部门','/api/v1/system/operLog/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:32:49') 
2025-06-21T10:32:53.718+08:00 [DEBU] {6ca847bc13ee4a1801a6c57fb50f251f} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:32:53.719+08:00 [DEBU] {6ca847bc13ee4a1801a6c57fb50f251f} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,10
2025-06-21T10:32:53.725+08:00 [DEBU] {1c158abc13ee4a1802a6c57f1bedbfe3} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('在线用户','/api/v1/system/online/list','GET',1,'demo','财务部门','/api/v1/system/online/list?ipaddr=&userName=&pageNum=1&pageSize=10','::1','内网IP','{"ipaddr":"","pageNum":"1","pageSize":"10","userName":""}','2025-06-21 10:32:53') 
2025-06-21T10:33:00.495+08:00 [DEBU] {8438f64f15ee4a1804a6c57fd6264cbd} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_oper_log_type&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_oper_log_type"}','2025-06-21 10:33:00') 
2025-06-21T10:33:00.511+08:00 [DEBU] {283b2b5115ee4a1805a6c57fe2a2a8aa} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_oper_log`
2025-06-21T10:33:00.512+08:00 [DEBU] {283b2b5115ee4a1805a6c57fe2a2a8aa} [  0 ms] [default] [gfast] [rows:10 ] SELECT `oper_id`,`title`,`business_type`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`error_msg`,`oper_time` FROM `sys_oper_log` ORDER BY `oper_id` DESC LIMIT 0,10
2025-06-21T10:33:00.518+08:00 [DEBU] {f0ee6d5115ee4a1806a6c57f2345cd95} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('操作日志','/api/v1/system/operLog/list','GET',1,'demo','财务部门','/api/v1/system/operLog/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 10:33:00') 
2025-06-21T10:33:03.680+08:00 [DEBU] {8c6ff80d16ee4a1808a6c57f78e3365c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('服务监控','/api/v1/system/monitor/server','GET',1,'demo','财务部门','/api/v1/system/monitor/server','::1','内网IP','{}','2025-06-21 10:33:03') 
2025-06-21T10:40:05.342+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 17 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:40:05.344+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:40:05.345+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:40:05.346+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T10:40:05.360+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:40:05','在线用户定时更新，执行成功') 
2025-06-21T10:50:05.985+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 47 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:50:05.990+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:50:05.993+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:50:05.995+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T10:50:06.013+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 17 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:50:05','在线用户定时更新，执行成功') 
2025-06-21T11:00:05.432+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:00:05.438+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:00:05.439+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:00:05.440+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:00:05.443+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:00:05','在线用户定时更新，执行成功') 
2025-06-21T11:04:08.591+08:00 [DEBU] {8c9d583ac8ef4a180ba6c57f2c7dabd6} [ 19 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:04:08.597+08:00 [DEBU] {6cd6673ac8ef4a180ca6c57ffcb636fb} [ 13 ms] [default] [gfast] [rows:28 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:04:08.598+08:00 [DEBU] {8c9d583ac8ef4a180ba6c57f2c7dabd6} [  3 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:04:08.622+08:00 [DEBU] {e0686144c8ef4a180da6c57f4995a9e3} [ 11 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:04:08') 
2025-06-21T11:04:08.624+08:00 [DEBU] {e0686144c8ef4a180ea6c57fc97f8f49} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 11:04:08') 
2025-06-21T11:04:20.697+08:00 [DEBU] {ac298414cbef4a1810a6c57f1b32baf1} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('flow_type')
2025-06-21T11:04:20.700+08:00 [DEBU] {ac298414cbef4a1810a6c57f1b32baf1} [  2 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('flow_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:04:20.706+08:00 [DEBU] {5c105115cbef4a1811a6c57f1e3d9309} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=31','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"31"}','2025-06-21 11:04:20') 
2025-06-21T11:04:22.959+08:00 [DEBU] {9cedd49bcbef4a1813a6c57f7431fa15} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('flow_status')
2025-06-21T11:04:22.961+08:00 [DEBU] {9cedd49bcbef4a1813a6c57f7431fa15} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('flow_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:04:22.967+08:00 [DEBU] {4009299ccbef4a1814a6c57f5a2ffd8a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=32','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"32"}','2025-06-21 11:04:22') 
2025-06-21T11:04:23.784+08:00 [DEBU] {3c18fccccbef4a1816a6c57f09783e4c} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('flow_level')
2025-06-21T11:04:23.785+08:00 [DEBU] {3c18fccccbef4a1816a6c57f09783e4c} [  0 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('flow_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:04:23.790+08:00 [DEBU] {7ca53dcdcbef4a1817a6c57f20acf5f5} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=35','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"35"}','2025-06-21 11:04:23') 
2025-06-21T11:04:40.128+08:00 [DEBU] {b4d1cb9acfef4a1819a6c57f97b27993} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys','sys_user_sex','sys_job_status','sys_job_group','admin_login_status','sys_oper_log_status','sys_job_policy','sys_show_hide','sys_normal_disable','sys_yes_no','menu_module_type','sys_blog_type','sys_log_sign','sys_oper_log_type','notice_tag','sys_upload_drive','sys_upload_file_type')
2025-06-21T11:04:40.131+08:00 [DEBU] {b4d1cb9acfef4a1819a6c57f97b27993} [  2 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys','sys_user_sex','sys_job_status','sys_job_group','admin_login_status','sys_oper_log_status','sys_job_policy','sys_show_hide','sys_normal_disable','sys_yes_no','menu_module_type','sys_blog_type','sys_log_sign','sys_oper_log_type','notice_tag','sys_upload_drive','sys_upload_file_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:04:40.138+08:00 [DEBU] {1077889bcfef4a181aa6c57f4d0bfe8e} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=54','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"54"}','2025-06-21 11:04:40') 
2025-06-21T11:04:45.340+08:00 [DEBU] {08409fd1d0ef4a181ca6c57f512d00ba} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:04:45') 
2025-06-21T11:06:38.839+08:00 [DEBU] {f097053eebef4a181ea6c57ff0b55036} [ 10 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='moment_category' LIMIT 1
2025-06-21T11:06:38.843+08:00 [DEBU] {f097053eebef4a181ea6c57ff0b55036} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'动态分类','moment_category',1,31,'','2025-06-21 11:06:38','2025-06-21 11:06:38') 
2025-06-21T11:06:38.849+08:00 [DEBU] {18115e3febef4a181fa6c57f6eefa103} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"动态分类","dictType":"moment_category","pid":54,"remark":"","status":1}','2025-06-21 11:06:38') 
2025-06-21T11:06:38.867+08:00 [DEBU] {18467d40ebef4a1820a6c57f7262f320} [  2 ms] [default] [gfast] [rows:29 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:06:38.874+08:00 [DEBU] {043bc940ebef4a1821a6c57fe76c180c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:06:38') 
2025-06-21T11:06:48.142+08:00 [DEBU] {00de1869edef4a1823a6c57ff8970c0b} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys','sys_user_sex','sys_job_status','sys_job_group','admin_login_status','sys_oper_log_status','sys_job_policy','sys_show_hide','sys_normal_disable','sys_yes_no','menu_module_type','sys_blog_type','sys_log_sign','sys_oper_log_type','notice_tag','sys_upload_drive','sys_upload_file_type','moment_category')
2025-06-21T11:06:48.143+08:00 [DEBU] {00de1869edef4a1823a6c57ff8970c0b} [  1 ms] [default] [gfast] [rows:9  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys','sys_user_sex','sys_job_status','sys_job_group','admin_login_status','sys_oper_log_status','sys_job_policy','sys_show_hide','sys_normal_disable','sys_yes_no','menu_module_type','sys_blog_type','sys_log_sign','sys_oper_log_type','notice_tag','sys_upload_drive','sys_upload_file_type','moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 40,10
2025-06-21T11:06:48.148+08:00 [DEBU] {bc1ba169edef4a1824a6c57f48306eef} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=5&pageSize=10&dictLabel=&status=&typeId=54','::1','内网IP','{"dictLabel":"","pageNum":"5","pageSize":"10","status":"","typeId":"54"}','2025-06-21 11:06:48') 
2025-06-21T11:06:57.344+08:00 [DEBU] {58fbc28defef4a1826a6c57ff111aaa8} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:06:57') 
2025-06-21T11:07:03.734+08:00 [DEBU] {0086ad0af1ef4a1827a6c57f3a825408} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:03.735+08:00 [DEBU] {0086ad0af1ef4a1827a6c57f3a825408} [  0 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:07:03.741+08:00 [DEBU] {04dbf90af1ef4a182aa6c57f9c95e45c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 11:07:03') 
2025-06-21T11:07:03.743+08:00 [DEBU] {04dbf90af1ef4a1829a6c57f9276158f} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:07:03') 
2025-06-21T11:07:06.921+08:00 [DEBU] {84a45bc8f1ef4a182ca6c57ffa1e6121} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:06.930+08:00 [DEBU] {84a45bc8f1ef4a182ca6c57ffa1e6121} [  9 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 80,10
2025-06-21T11:07:06.936+08:00 [DEBU] {287f6dc9f1ef4a182da6c57fad299f1f} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=9&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"9","pageSize":"10","status":""}','2025-06-21 11:07:06') 
2025-06-21T11:07:09.703+08:00 [DEBU] {bcb17e6ef2ef4a182fa6c57fd88ecb84} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:09.704+08:00 [DEBU] {bcb17e6ef2ef4a182fa6c57fd88ecb84} [  1 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 70,10
2025-06-21T11:07:09.709+08:00 [DEBU] {bc95bd6ef2ef4a1830a6c57f2e9d5ab2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=8&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"8","pageSize":"10","status":""}','2025-06-21 11:07:09') 
2025-06-21T11:07:13.175+08:00 [DEBU] {34bb7d3df3ef4a1832a6c57f174ea0d6} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:13.177+08:00 [DEBU] {34bb7d3df3ef4a1832a6c57f174ea0d6} [  1 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 60,10
2025-06-21T11:07:13.183+08:00 [DEBU] {8cc4cb3df3ef4a1833a6c57f8b4fbb5f} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=7&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"7","pageSize":"10","status":""}','2025-06-21 11:07:13') 
2025-06-21T11:07:15.926+08:00 [DEBU] {9cd97ae1f3ef4a1835a6c57fb3e1f17d} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:15.927+08:00 [DEBU] {9cd97ae1f3ef4a1835a6c57fb3e1f17d} [  1 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 50,10
2025-06-21T11:07:15.932+08:00 [DEBU] {********************************} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=6&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"6","pageSize":"10","status":""}','2025-06-21 11:07:15') 
2025-06-21T11:07:17.160+08:00 [DEBU] {ec71072bf4ef4a1838a6c57f5a96cb6c} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:17.162+08:00 [DEBU] {ec71072bf4ef4a1838a6c57f5a96cb6c} [  1 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 40,10
2025-06-21T11:07:17.167+08:00 [DEBU] {e4624b2bf4ef4a1839a6c57f468487c6} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=5&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"5","pageSize":"10","status":""}','2025-06-21 11:07:17') 
2025-06-21T11:07:18.024+08:00 [DEBU] {d499785ef4ef4a183ba6c57f23f70c2b} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:18.026+08:00 [DEBU] {d499785ef4ef4a183ba6c57f23f70c2b} [  2 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 20,10
2025-06-21T11:07:18.032+08:00 [DEBU] {f440ca5ef4ef4a183ca6c57f44e6096c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=3&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"3","pageSize":"10","status":""}','2025-06-21 11:07:18') 
2025-06-21T11:07:19.710+08:00 [DEBU] {4c6ffdc2f4ef4a183ea6c57fa3940f5a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:19.711+08:00 [DEBU] {4c6ffdc2f4ef4a183ea6c57fa3940f5a} [  0 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 10,10
2025-06-21T11:07:19.715+08:00 [DEBU] {c0293dc3f4ef4a183fa6c57f67778d7b} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=2&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"2","pageSize":"10","status":""}','2025-06-21 11:07:19') 
2025-06-21T11:07:20.700+08:00 [DEBU] {18d7f7fdf4ef4a1840a6c57f5b98c49f} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T11:07:20.701+08:00 [DEBU] {18d7f7fdf4ef4a1840a6c57f5b98c49f} [  1 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:07:20.705+08:00 [DEBU] {9c6139fef4ef4a1841a6c57f9bb3c063} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 11:07:20') 
2025-06-21T11:07:58.394+08:00 [DEBU] {40cc8ebefdef4a1843a6c57f07505d21} [105 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T11:07:58.399+08:00 [DEBU] {40cc8ebefdef4a1843a6c57f07505d21} [  5 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:07:58.460+08:00 [DEBU] {6c8f66c5fdef4a1844a6c57f83403386} [ 56 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 11:07:58') 
2025-06-21T11:08:02.039+08:00 [DEBU] {5800b898feef4a1846a6c57f26652851} [ 84 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:08:01') 
2025-06-21T11:08:19.394+08:00 [DEBU] {680553a802f04a1848a6c57fad999b52} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_file_type')
2025-06-21T11:08:19.396+08:00 [DEBU] {680553a802f04a1848a6c57fad999b52} [  1 ms] [default] [gfast] [rows:6  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_file_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:08:19.404+08:00 [DEBU] {4cbfafa802f04a1849a6c57fbeebaa3c} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=53','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"53"}','2025-06-21 11:08:19') 
2025-06-21T11:08:20.687+08:00 [DEBU] {e81967f502f04a184ba6c57f2ba2aecb} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_user_sex')
2025-06-21T11:08:20.687+08:00 [DEBU] {e81967f502f04a184ba6c57f2ba2aecb} [  0 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_user_sex') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:08:20.692+08:00 [DEBU] {143ab1f502f04a184ca6c57f41f0a362} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=1','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"1"}','2025-06-21 11:08:20') 
2025-06-21T11:08:32.419+08:00 [DEBU] {ac08e8af05f04a184da6c57f50162e0b} [ 15 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T11:08:32.420+08:00 [DEBU] {ac08e8af05f04a184da6c57f50162e0b} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:08:32.426+08:00 [DEBU] {74ca11b105f04a184ea6c57fbf5b3042} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 11:08:32') 
2025-06-21T11:08:34.533+08:00 [DEBU] {5094982e06f04a1850a6c57f697119f2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:08:34') 
2025-06-21T11:08:57.657+08:00 [DEBU] {2c8aaa900bf04a1852a6c57f25d84f63} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'工作','1','moment_category','','',1,1,31,'','2025-06-21 11:08:57','2025-06-21 11:08:57') 
2025-06-21T11:08:57.662+08:00 [DEBU] {184a4e910bf04a1853a6c57f4ccf0c03} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"工作","dictSort":0,"dictType":"moment_category","dictValue":"1","isDefault":1,"remark":"","status":1}','2025-06-21 11:08:57') 
2025-06-21T11:08:57.690+08:00 [DEBU] {4c106c920bf04a1854a6c57fc12f91a4} [ 11 ms] [default] [gfast] [rows:29 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:08:57.691+08:00 [DEBU] {4c106c920bf04a1854a6c57fc12f91a4} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T11:08:57.693+08:00 [DEBU] {4c106c920bf04a1854a6c57fc12f91a4} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:08:57.700+08:00 [DEBU] {042b70930bf04a1855a6c57fd851064d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 11:08:57') 
2025-06-21T11:09:00.325+08:00 [DEBU] {1cac02300cf04a1857a6c57fa66ccda9} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:09:00') 
2025-06-21T11:09:19.216+08:00 [DEBU] {c8a9f49410f04a1858a6c57fbf479c24} [ 20 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'生活','2','moment_category','','',0,1,31,'','2025-06-21 11:09:19','2025-06-21 11:09:19') 
2025-06-21T11:09:19.220+08:00 [DEBU] {ccb43e9610f04a1859a6c57fdf2c8696} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"生活","dictSort":0,"dictType":"moment_category","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 11:09:19') 
2025-06-21T11:09:19.247+08:00 [DEBU] {68b8ac9710f04a185aa6c57fc1ed99b5} [  5 ms] [default] [gfast] [rows:29 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:09:19.249+08:00 [DEBU] {68b8ac9710f04a185aa6c57fc1ed99b5} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T11:09:19.251+08:00 [DEBU] {68b8ac9710f04a185aa6c57fc1ed99b5} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:09:19.255+08:00 [DEBU] {d40d4f9810f04a185ba6c57f17f0fc0c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 11:09:19') 
2025-06-21T11:09:21.598+08:00 [DEBU] {5c8b0d2411f04a185da6c57f02e63c5e} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:09:21') 
2025-06-21T11:09:44.966+08:00 [DEBU] {9c2bcb9416f04a185ea6c57fe112d57d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'学习','3','moment_category','','',0,1,31,'','2025-06-21 11:09:44','2025-06-21 11:09:44') 
2025-06-21T11:09:44.970+08:00 [DEBU] {b811149516f04a185fa6c57f64995625} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"学习","dictSort":0,"dictType":"moment_category","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 11:09:44') 
2025-06-21T11:09:44.987+08:00 [DEBU] {04261c9616f04a1860a6c57ff81b87de} [  2 ms] [default] [gfast] [rows:29 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:09:44.988+08:00 [DEBU] {04261c9616f04a1860a6c57ff81b87de} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T11:09:44.989+08:00 [DEBU] {04261c9616f04a1860a6c57ff81b87de} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:09:44.994+08:00 [DEBU] {3cd7739616f04a1861a6c57ff51ecf43} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 11:09:44') 
2025-06-21T11:10:05.029+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:10:05.031+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:10:05.031+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:10:05.032+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:10:05.036+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:10:05','在线用户定时更新，执行成功') 
2025-06-21T11:10:27.932+08:00 [DEBU] {f47ffc9320f04a1863a6c57fb885ef12} [ 32 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:10:27') 
2025-06-21T11:11:49.757+08:00 [DEBU] {c0fab1a233f04a1865a6c57fe2117f38} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:11:49') 
2025-06-21T11:12:14.435+08:00 [DEBU] {f0d1456139f04a1866a6c57f19e43439} [ 12 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='task_status' LIMIT 1
2025-06-21T11:12:14.450+08:00 [DEBU] {f0d1456139f04a1866a6c57f19e43439} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'任务状态','task_status',1,31,'','2025-06-21 11:12:14','2025-06-21 11:12:14') 
2025-06-21T11:12:14.458+08:00 [DEBU] {5cb50d6339f04a1867a6c57f70532e02} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"任务状态","dictType":"task_status","pid":54,"remark":"","status":1}','2025-06-21 11:12:14') 
2025-06-21T11:12:14.474+08:00 [DEBU] {e4133a6439f04a1868a6c57f9b9acec4} [  2 ms] [default] [gfast] [rows:30 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:12:14.480+08:00 [DEBU] {d04c836439f04a1869a6c57faa7f6ce0} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:12:14') 
2025-06-21T11:12:16.387+08:00 [DEBU] {247de2d539f04a186ba6c57fe8d1d0e1} [  8 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:12:16.387+08:00 [DEBU] {247de2d539f04a186ba6c57fe8d1d0e1} [  0 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:12:16.392+08:00 [DEBU] {c06c83d639f04a186ca6c57f405053e3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:12:16') 
2025-06-21T11:12:18.707+08:00 [DEBU] {ac2e7d603af04a186ea6c57f480b5b1a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:12:18') 
2025-06-21T11:12:55.750+08:00 [DEBU] {8c446cff42f04a186fa6c57f29be5d2b} [ 19 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'未开始','1','task_status','','',1,1,31,'','2025-06-21 11:12:55','2025-06-21 11:12:55') 
2025-06-21T11:12:55.754+08:00 [DEBU] {e0d2b00043f04a1870a6c57f6e347c3c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"未开始","dictSort":0,"dictType":"task_status","dictValue":"1","isDefault":1,"remark":"","status":1}','2025-06-21 11:12:55') 
2025-06-21T11:12:55.777+08:00 [DEBU] {88cc9a0143f04a1871a6c57f4b03363d} [ 10 ms] [default] [gfast] [rows:30 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:12:55.779+08:00 [DEBU] {88cc9a0143f04a1871a6c57f4b03363d} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:12:55.781+08:00 [DEBU] {88cc9a0143f04a1871a6c57f4b03363d} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:12:55.786+08:00 [DEBU] {2423930243f04a1872a6c57f59c7cf57} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:12:55') 
2025-06-21T11:12:57.797+08:00 [DEBU] {08fa6e7a43f04a1874a6c57f13dbd536} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:12:57') 
2025-06-21T11:13:11.040+08:00 [DEBU] {30cea08f46f04a1875a6c57fe2b9b90c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'进行中','2','task_status','','',0,1,31,'','2025-06-21 11:13:11','2025-06-21 11:13:11') 
2025-06-21T11:13:11.045+08:00 [DEBU] {00db189046f04a1876a6c57fb08c8a71} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"进行中","dictSort":0,"dictType":"task_status","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 11:13:11') 
2025-06-21T11:13:11.062+08:00 [DEBU] {c8d52c9146f04a1877a6c57f91de4f5d} [  0 ms] [default] [gfast] [rows:30 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:13:11.064+08:00 [DEBU] {c8d52c9146f04a1877a6c57f91de4f5d} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:13:11.065+08:00 [DEBU] {c8d52c9146f04a1877a6c57f91de4f5d} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:13:11.071+08:00 [DEBU] {481d9c9146f04a1878a6c57f48840640} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:13:11') 
2025-06-21T11:13:12.724+08:00 [DEBU] {bc7521f446f04a187aa6c57f7b31e9c4} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:13:12') 
2025-06-21T11:13:33.814+08:00 [DEBU] {d83e17dd4bf04a187ba6c57ffcaf4f30} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'已完成','3','task_status','','',0,1,31,'','2025-06-21 11:13:33','2025-06-21 11:13:33') 
2025-06-21T11:13:33.818+08:00 [DEBU] {c8b47edd4bf04a187ca6c57f05303bd0} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"已完成","dictSort":0,"dictType":"task_status","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 11:13:33') 
2025-06-21T11:13:33.834+08:00 [DEBU] {c8e36cde4bf04a187da6c57f6252d44f} [  2 ms] [default] [gfast] [rows:30 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:13:33.835+08:00 [DEBU] {c8e36cde4bf04a187da6c57f6252d44f} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:13:33.837+08:00 [DEBU] {c8e36cde4bf04a187da6c57f6252d44f} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:13:33.848+08:00 [DEBU] {60e5e6de4bf04a187ea6c57ff0c105fa} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:13:33') 
2025-06-21T11:13:42.964+08:00 [DEBU] {d4f2a2fe4df04a1880a6c57f66da4bfe} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:13:42') 
2025-06-21T11:14:04.659+08:00 [DEBU] {94a7b20b53f04a1881a6c57f098a424e} [  3 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='task_priority' LIMIT 1
2025-06-21T11:14:04.663+08:00 [DEBU] {94a7b20b53f04a1881a6c57f098a424e} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'任务优先级','task_priority',1,31,'','2025-06-21 11:14:04','2025-06-21 11:14:04') 
2025-06-21T11:14:04.668+08:00 [DEBU] {4838450c53f04a1882a6c57fe88e3de2} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"任务优先级","dictType":"task_priority","pid":0,"remark":"","status":1}','2025-06-21 11:14:04') 
2025-06-21T11:14:04.688+08:00 [DEBU] {004f6b0d53f04a1883a6c57fe22bbdb3} [  3 ms] [default] [gfast] [rows:31 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:14:04.697+08:00 [DEBU] {d877d30d53f04a1884a6c57fa2658a7c} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:14:04') 
2025-06-21T11:14:15.417+08:00 [DEBU] {846af48c55f04a1886a6c57fe5dc2a44} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:14:15') 
2025-06-21T11:14:15.430+08:00 [DEBU] {301b8d8d55f04a1888a6c57f5f636d2d} [  4 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=58 LIMIT 1
2025-06-21T11:14:15.435+08:00 [DEBU] {58c1098e55f04a1889a6c57fea41dba2} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=58','::1','内网IP','{"dictId":"58"}','2025-06-21 11:14:15') 
2025-06-21T11:14:34.038+08:00 [DEBU] {786deee259f04a188ba6c57f9e6437cb} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T11:14:34.039+08:00 [DEBU] {786deee259f04a188ba6c57f9e6437cb} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:14:34.046+08:00 [DEBU] {74ae38e359f04a188ca6c57fcb7e3449} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 11:14:34') 
2025-06-21T11:14:37.162+08:00 [DEBU] {84bb5c9c5af04a188fa6c57fde8af73f} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:14:37') 
2025-06-21T11:14:37.171+08:00 [DEBU] {00ca3a9d5af04a1890a6c57f03d9a047} [  8 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=59 LIMIT 1
2025-06-21T11:14:37.179+08:00 [DEBU] {6c4bde9d5af04a1891a6c57f9c88a521} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=59','::1','内网IP','{"dictId":"59"}','2025-06-21 11:14:37') 
2025-06-21T11:14:41.291+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [  3 ms] [default] [gfast] [rows:0  ] [txid:1] BEGIN (IosolationLevel: Default, ReadOnly: false)
2025-06-21T11:14:41.295+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [  3 ms] [default] [gfast] [rows:0  ] [txid:1] SELECT `dict_id` FROM `sys_dict_type` WHERE (`dict_type`='task_priority') AND (dict_id !=59) LIMIT 1
2025-06-21T11:14:41.296+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [  1 ms] [default] [gfast] [rows:1  ] [txid:1] SELECT `dict_type` FROM `sys_dict_type` WHERE `dict_id`=59 LIMIT 1
2025-06-21T11:14:41.310+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [ 13 ms] [default] [gfast] [rows:1  ] [txid:1] UPDATE `sys_dict_type` SET `pid`=54,`dict_name`='任务优先级',`dict_type`='task_priority',`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 11:14:41' WHERE `dict_id`=59
2025-06-21T11:14:41.320+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [ 10 ms] [default] [gfast] [rows:0  ] [txid:1] UPDATE `sys_dict_data` SET `dict_type`='task_priority',`updated_at`='2025-06-21 11:14:41' WHERE `dict_type`='task_priority'
2025-06-21T11:14:41.322+08:00 [DEBU] {e87b04935bf04a1893a6c57f963d3aa9} [  2 ms] [default] [gfast] [rows:0  ] [txid:1] COMMIT
2025-06-21T11:14:41.327+08:00 [DEBU] {702049955bf04a1894a6c57f2e2ae6eb} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/type/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:14:05","dictId":59,"dictName":"任务优先级","dictType":"task_priority","pid":54,"remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:14:05"}','2025-06-21 11:14:41') 
2025-06-21T11:14:41.345+08:00 [DEBU] {ac3678965bf04a1895a6c57fa07e642e} [  1 ms] [default] [gfast] [rows:31 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:14:41.351+08:00 [DEBU] {1475b3965bf04a1896a6c57f06555a0c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:14:41') 
2025-06-21T11:14:43.512+08:00 [DEBU] {a4c3a3175cf04a1897a6c57fd14931da} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T11:14:43.513+08:00 [DEBU] {a4c3a3175cf04a1897a6c57fd14931da} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:14:43.517+08:00 [DEBU] {48fcd5175cf04a1898a6c57fca20669a} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 11:14:43') 
2025-06-21T11:14:45.337+08:00 [DEBU] {d0b153845cf04a189aa6c57f6a4abf23} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:14:45') 
2025-06-21T11:14:58.556+08:00 [DEBU] {4ced39985ff04a189ba6c57f0381ba27} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'低','1','task_priority','','',0,1,31,'','2025-06-21 11:14:58','2025-06-21 11:14:58') 
2025-06-21T11:14:58.559+08:00 [DEBU] {b85475985ff04a189ca6c57fb5e062fc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"低","dictSort":0,"dictType":"task_priority","dictValue":"1","isDefault":0,"remark":"","status":1}','2025-06-21 11:14:58') 
2025-06-21T11:14:58.579+08:00 [DEBU] {001b98995ff04a189da6c57fd843ada1} [  2 ms] [default] [gfast] [rows:31 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:14:58.580+08:00 [DEBU] {001b98995ff04a189da6c57fd843ada1} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T11:14:58.582+08:00 [DEBU] {001b98995ff04a189da6c57fd843ada1} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:14:58.587+08:00 [DEBU] {68f70c9a5ff04a189ea6c57ff50700a7} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 11:14:58') 
2025-06-21T11:15:00.356+08:00 [DEBU] {9ca2870360f04a18a0a6c57faf0705c4} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:15:00') 
2025-06-21T11:15:10.252+08:00 [DEBU] {50bde15062f04a18a1a6c57f97f9cb40} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'中','2','task_priority','','',1,1,31,'','2025-06-21 11:15:10','2025-06-21 11:15:10') 
2025-06-21T11:15:10.263+08:00 [DEBU] {c0de9f5162f04a18a2a6c57f6f79b5d2} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"中","dictSort":0,"dictType":"task_priority","dictValue":"2","isDefault":1,"remark":"","status":1}','2025-06-21 11:15:10') 
2025-06-21T11:15:10.282+08:00 [DEBU] {d493d05262f04a18a3a6c57f3283ba22} [  8 ms] [default] [gfast] [rows:31 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:15:10.283+08:00 [DEBU] {d493d05262f04a18a3a6c57f3283ba22} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T11:15:10.285+08:00 [DEBU] {d493d05262f04a18a3a6c57f3283ba22} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:15:10.289+08:00 [DEBU] {c4a4975362f04a18a4a6c57fc3a058f1} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 11:15:10') 
2025-06-21T11:15:12.163+08:00 [DEBU] {60aa4ec362f04a18a6a6c57fdc30f872} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:15:12') 
2025-06-21T11:15:22.899+08:00 [DEBU] {c87a394365f04a18a7a6c57f7bd2f3b7} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'高','3','task_priority','','',0,1,31,'','2025-06-21 11:15:22','2025-06-21 11:15:22') 
2025-06-21T11:15:22.903+08:00 [DEBU] {08ef7a4365f04a18a8a6c57f32faf799} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"高","dictSort":0,"dictType":"task_priority","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 11:15:22') 
2025-06-21T11:15:22.922+08:00 [DEBU] {64faa34465f04a18a9a6c57f339b8877} [  1 ms] [default] [gfast] [rows:31 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:15:22.924+08:00 [DEBU] {64faa34465f04a18a9a6c57f339b8877} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T11:15:22.926+08:00 [DEBU] {64faa34465f04a18a9a6c57f339b8877} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:15:22.931+08:00 [DEBU] {00561a4565f04a18aaa6c57f9baeaf23} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 11:15:22') 
2025-06-21T11:17:16.440+08:00 [DEBU] {f0ba2eb27ff04a18aca6c57f9d4abf06} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:17:16') 
2025-06-21T11:18:07.813+08:00 [DEBU] {bcef1ea68bf04a18ada6c57f5152a4c8} [ 48 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='user_relation_type' LIMIT 1
2025-06-21T11:18:07.820+08:00 [DEBU] {bcef1ea68bf04a18ada6c57f5152a4c8} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'人脉关系类型','user_relation_type',1,31,'','2025-06-21 11:18:07','2025-06-21 11:18:07') 
2025-06-21T11:18:07.824+08:00 [DEBU] {0cd680a98bf04a18aea6c57f3ae39c77} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"人脉关系类型","dictType":"user_relation_type","pid":54,"remark":"","status":1}','2025-06-21 11:18:07') 
2025-06-21T11:18:07.850+08:00 [DEBU] {60de1dab8bf04a18afa6c57f5d2e7733} [  1 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:18:07.856+08:00 [DEBU] {1c2b65ab8bf04a18b0a6c57fb1d53239} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:18:07') 
2025-06-21T11:18:09.498+08:00 [DEBU] {b0a15c0d8cf04a18b2a6c57fc69ef4f7} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:18:09.499+08:00 [DEBU] {b0a15c0d8cf04a18b2a6c57fc69ef4f7} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:18:09.503+08:00 [DEBU] {943d8c0d8cf04a18b3a6c57fc68c2aa3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:18:09') 
2025-06-21T11:18:11.147+08:00 [DEBU] {f8e98c6f8cf04a18b5a6c57f374ecff8} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:18:11') 
2025-06-21T11:18:31.426+08:00 [DEBU] {c80a3e2891f04a18b6a6c57f097d6709} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'教练','1','user_relation_type','','',0,1,31,'','2025-06-21 11:18:31','2025-06-21 11:18:31') 
2025-06-21T11:18:31.432+08:00 [DEBU] {c0929f2891f04a18b7a6c57f59fc50c3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"教练","dictSort":0,"dictType":"user_relation_type","dictValue":"1","isDefault":0,"remark":"","status":1}','2025-06-21 11:18:31') 
2025-06-21T11:18:31.462+08:00 [DEBU] {78ce722a91f04a18b8a6c57f130d0eb6} [  2 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:18:31.464+08:00 [DEBU] {78ce722a91f04a18b8a6c57f130d0eb6} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:18:31.469+08:00 [DEBU] {78ce722a91f04a18b8a6c57f130d0eb6} [  4 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:18:31.474+08:00 [DEBU] {6c752c2b91f04a18b9a6c57ff0f4f2bb} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:18:31') 
2025-06-21T11:18:33.628+08:00 [DEBU] {101090ab91f04a18bba6c57f11825163} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:18:33') 
2025-06-21T11:18:54.159+08:00 [DEBU] {2c86ff7296f04a18bca6c57fd6d9d4fd} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'介绍人','2','user_relation_type','','',0,1,31,'','2025-06-21 11:18:54','2025-06-21 11:18:54') 
2025-06-21T11:18:54.164+08:00 [DEBU] {8cca877396f04a18bda6c57f040276a7} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"介绍人","dictSort":0,"dictType":"user_relation_type","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 11:18:54') 
2025-06-21T11:18:54.248+08:00 [DEBU] {c40e837496f04a18bea6c57fb3adfdce} [ 65 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:18:54.250+08:00 [DEBU] {c40e837496f04a18bea6c57fb3adfdce} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:18:54.252+08:00 [DEBU] {c40e837496f04a18bea6c57fb3adfdce} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:18:54.257+08:00 [DEBU] {5cc71e7996f04a18bfa6c57ffbfe28bc} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:18:54') 
2025-06-21T11:19:03.082+08:00 [DEBU] {2cff298798f04a18c1a6c57f7802b510} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:19:03') 
2025-06-21T11:19:24.930+08:00 [DEBU] {98e0349d9df04a18c2a6c57f04d98c60} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'我介绍的人','3','user_relation_type','','',0,1,31,'','2025-06-21 11:19:24','2025-06-21 11:19:24') 
2025-06-21T11:19:24.935+08:00 [DEBU] {9c97ab9d9df04a18c3a6c57f1d18b831} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"我介绍的人","dictSort":0,"dictType":"user_relation_type","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 11:19:24') 
2025-06-21T11:19:24.952+08:00 [DEBU] {389ab19e9df04a18c4a6c57ff863df60} [  2 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:19:24.954+08:00 [DEBU] {389ab19e9df04a18c4a6c57ff863df60} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:19:24.955+08:00 [DEBU] {389ab19e9df04a18c4a6c57ff863df60} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:19:24.961+08:00 [DEBU] {e8212e9f9df04a18c5a6c57fe6d5b69c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:19:24') 
2025-06-21T11:19:26.619+08:00 [DEBU] {dc3004029ef04a18c7a6c57f19f18acb} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:19:26') 
2025-06-21T11:19:26.644+08:00 [DEBU] {843a92039ef04a18c9a6c57f105f2fe0} [  2 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_code`=132 LIMIT 1
2025-06-21T11:19:26.648+08:00 [DEBU] {0c10c6039ef04a18caa6c57f101f2f9b} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/get','GET',1,'demo','财务部门','/api/v1/system/dict/data/get?dictCode=132','::1','内网IP','{"dictCode":"132"}','2025-06-21 11:19:26') 
2025-06-21T11:19:40.759+08:00 [DEBU] {0880d64ca1f04a18cca6c57f77b0dbb5} [  2 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dict_data` SET `dict_sort`=0,`dict_label`='我的介绍人',`dict_value`='2',`dict_type`='user_relation_type',`css_class`='',`list_class`='',`is_default`=0,`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 11:19:40' WHERE `dict_code`=132
2025-06-21T11:19:40.764+08:00 [DEBU] {9481164da1f04a18cda6c57f7d7e3458} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/data/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:18:54","cssClass":"","dictCode":132,"dictLabel":"我的介绍人","dictSort":0,"dictType":"user_relation_type","dictValue":"2","isDefault":0,"listClass":"","remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:18:54"}','2025-06-21 11:19:40') 
2025-06-21T11:19:40.779+08:00 [DEBU] {78531e4ea1f04a18cea6c57fca4aed44} [  1 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:19:40.781+08:00 [DEBU] {78531e4ea1f04a18cea6c57fca4aed44} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:19:40.783+08:00 [DEBU] {78531e4ea1f04a18cea6c57fca4aed44} [  2 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:19:40.789+08:00 [DEBU] {30089d4ea1f04a18cfa6c57f51e04953} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:19:40') 
2025-06-21T11:19:47.994+08:00 [DEBU] {e4fc17fca2f04a18d1a6c57f0c715886} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:19:47') 
2025-06-21T11:19:57.731+08:00 [DEBU] {247f5a40a5f04a18d2a6c57f9df05838} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'死党','4','user_relation_type','','',0,1,31,'','2025-06-21 11:19:57','2025-06-21 11:19:57') 
2025-06-21T11:19:57.736+08:00 [DEBU] {18e6c240a5f04a18d3a6c57f7cb65b27} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"死党","dictSort":0,"dictType":"user_relation_type","dictValue":"4","isDefault":0,"remark":"","status":1}','2025-06-21 11:19:57') 
2025-06-21T11:19:57.751+08:00 [DEBU] {b0d5bc41a5f04a18d4a6c57f2ff35715} [  1 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:19:57.753+08:00 [DEBU] {b0d5bc41a5f04a18d4a6c57f2ff35715} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:19:57.755+08:00 [DEBU] {b0d5bc41a5f04a18d4a6c57f2ff35715} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:19:57.760+08:00 [DEBU] {54633042a5f04a18d5a6c57fdf97976b} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:19:57') 
2025-06-21T11:20:01.263+08:00 [DEBU] {e8f2fe12a6f04a18d7a6c57fa0fd02cb} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:20:01') 
2025-06-21T11:20:05.027+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:20:05.029+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:20:05.029+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:20:05.030+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:20:05.033+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:20:05','在线用户定时更新，执行成功') 
2025-06-21T11:20:17.419+08:00 [DEBU] {4890dbd5a9f04a18d8a6c57f14a18d89} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'学员','5','user_relation_type','','',0,1,31,'','2025-06-21 11:20:17','2025-06-21 11:20:17') 
2025-06-21T11:20:17.423+08:00 [DEBU] {20e733d6a9f04a18d9a6c57f9e633482} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"学员","dictSort":0,"dictType":"user_relation_type","dictValue":"5","isDefault":0,"remark":"","status":1}','2025-06-21 11:20:17') 
2025-06-21T11:20:17.440+08:00 [DEBU] {f02542d7a9f04a18daa6c57f83f1e816} [  2 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:20:17.441+08:00 [DEBU] {f02542d7a9f04a18daa6c57f83f1e816} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:20:17.442+08:00 [DEBU] {f02542d7a9f04a18daa6c57f83f1e816} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:20:17.446+08:00 [DEBU] {741e93d7a9f04a18dba6c57f7ab36ebc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:20:17') 
2025-06-21T11:20:25.462+08:00 [DEBU] {547644b5abf04a18dda6c57f01225a53} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:20:25') 
2025-06-21T11:20:25.550+08:00 [DEBU] {d8979ebaabf04a18dfa6c57fedc8c71f} [  2 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_code`=131 LIMIT 1
2025-06-21T11:20:25.555+08:00 [DEBU] {54eae3baabf04a18e0a6c57f55a78956} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/get','GET',1,'demo','财务部门','/api/v1/system/dict/data/get?dictCode=131','::1','内网IP','{"dictCode":"131"}','2025-06-21 11:20:25') 
2025-06-21T11:20:32.779+08:00 [DEBU] {6cfff768adf04a18e1a6c57f256f17bf} [ 11 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dict_data` SET `dict_sort`=0,`dict_label`='我的教练',`dict_value`='1',`dict_type`='user_relation_type',`css_class`='',`list_class`='',`is_default`=0,`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 11:20:32' WHERE `dict_code`=131
2025-06-21T11:20:32.788+08:00 [DEBU] {f0c3bd69adf04a18e2a6c57fc2b673e9} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/data/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:18:31","cssClass":"","dictCode":131,"dictLabel":"我的教练","dictSort":0,"dictType":"user_relation_type","dictValue":"1","isDefault":0,"listClass":"","remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:18:31"}','2025-06-21 11:20:32') 
2025-06-21T11:20:32.805+08:00 [DEBU] {5867ac6aadf04a18e3a6c57f265b4007} [  8 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:20:32.807+08:00 [DEBU] {5867ac6aadf04a18e3a6c57f265b4007} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:20:32.808+08:00 [DEBU] {5867ac6aadf04a18e3a6c57f265b4007} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:20:32.813+08:00 [DEBU] {9413786badf04a18e4a6c57f261b6979} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:20:32') 
2025-06-21T11:20:35.269+08:00 [DEBU] {683edcfdadf04a18e6a6c57f584c23e6} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:20:35') 
2025-06-21T11:20:35.281+08:00 [DEBU] {8c40abfeadf04a18e8a6c57f140a5184} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_code`=134 LIMIT 1
2025-06-21T11:20:35.287+08:00 [DEBU] {28d2e6feadf04a18e9a6c57f5ebf3ef2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/get','GET',1,'demo','财务部门','/api/v1/system/dict/data/get?dictCode=134','::1','内网IP','{"dictCode":"134"}','2025-06-21 11:20:35') 
2025-06-21T11:20:39.428+08:00 [DEBU] {5835b1f5aef04a18eaa6c57fcbefabb5} [  4 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dict_data` SET `dict_sort`=0,`dict_label`='我的死党',`dict_value`='4',`dict_type`='user_relation_type',`css_class`='',`list_class`='',`is_default`=0,`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 11:20:39' WHERE `dict_code`=134
2025-06-21T11:20:39.433+08:00 [DEBU] {b4960cf6aef04a18eba6c57f74b05f16} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/data/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:19:58","cssClass":"","dictCode":134,"dictLabel":"我的死党","dictSort":0,"dictType":"user_relation_type","dictValue":"4","isDefault":0,"listClass":"","remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:19:58"}','2025-06-21 11:20:39') 
2025-06-21T11:20:39.455+08:00 [DEBU] {c4726af7aef04a18eca6c57f402536fa} [  2 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:20:39.456+08:00 [DEBU] {c4726af7aef04a18eca6c57f402536fa} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:20:39.458+08:00 [DEBU] {c4726af7aef04a18eca6c57f402536fa} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:20:39.463+08:00 [DEBU] {e0fdd6f7aef04a18eda6c57fdb9283ab} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:20:39') 
2025-06-21T11:20:41.509+08:00 [DEBU] {f88ecc71aff04a18efa6c57ff67f20b2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:20:41') 
2025-06-21T11:20:41.523+08:00 [DEBU] {741cb372aff04a18f1a6c57f6c95e7e7} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_code`=135 LIMIT 1
2025-06-21T11:20:41.528+08:00 [DEBU] {c8c7f472aff04a18f2a6c57f40efa451} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/get','GET',1,'demo','财务部门','/api/v1/system/dict/data/get?dictCode=135','::1','内网IP','{"dictCode":"135"}','2025-06-21 11:20:41') 
2025-06-21T11:20:48.714+08:00 [DEBU] {30a43e1fb1f04a18f3a6c57f75a6c152} [  3 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dict_data` SET `dict_sort`=0,`dict_label`='我的学员',`dict_value`='5',`dict_type`='user_relation_type',`css_class`='',`list_class`='',`is_default`=0,`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 11:20:48' WHERE `dict_code`=135
2025-06-21T11:20:48.718+08:00 [DEBU] {6857851fb1f04a18f4a6c57f9893e271} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/data/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:20:17","cssClass":"","dictCode":135,"dictLabel":"我的学员","dictSort":0,"dictType":"user_relation_type","dictValue":"5","isDefault":0,"listClass":"","remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:20:17"}','2025-06-21 11:20:48') 
2025-06-21T11:20:48.735+08:00 [DEBU] {b0bc9a20b1f04a18f5a6c57f10c55417} [  1 ms] [default] [gfast] [rows:32 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:20:48.737+08:00 [DEBU] {b0bc9a20b1f04a18f5a6c57f10c55417} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type')
2025-06-21T11:20:48.738+08:00 [DEBU] {b0bc9a20b1f04a18f5a6c57f10c55417} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('user_relation_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:20:48.743+08:00 [DEBU] {4ca1f620b1f04a18f6a6c57f66a4c00a} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=60','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"60"}','2025-06-21 11:20:48') 
2025-06-21T11:30:05.936+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:30:05.937+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:30:05.938+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:30:05.939+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:30:05.948+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:30:05','在线用户定时更新，执行成功') 
2025-06-21T11:38:31.481+08:00 [DEBU] {b06f8490a8f14a18f8a6c57fc8c35222} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:38:31') 
2025-06-21T11:38:52.206+08:00 [DEBU] {dc94a663adf14a18f9a6c57f9a0da116} [ 16 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='group_type' LIMIT 1
2025-06-21T11:38:52.210+08:00 [DEBU] {dc94a663adf14a18f9a6c57f9a0da116} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'群组类型','group_type',1,31,'','2025-06-21 11:38:52','2025-06-21 11:38:52') 
2025-06-21T11:38:52.216+08:00 [DEBU] {584dee64adf14a18faa6c57f6a66ae15} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"群组类型","dictType":"group_type","pid":54,"remark":"","status":1}','2025-06-21 11:38:52') 
2025-06-21T11:38:52.236+08:00 [DEBU] {04662d66adf14a18fba6c57f2f591e9b} [  1 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:38:52.243+08:00 [DEBU] {9cae9766adf14a18fca6c57f8ae9f6a9} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:38:52') 
2025-06-21T11:38:54.416+08:00 [DEBU] {b8f8e1e7adf14a18fea6c57f4961ceca} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:38:54.417+08:00 [DEBU] {b8f8e1e7adf14a18fea6c57f4961ceca} [  0 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:38:54.422+08:00 [DEBU] {88258fe8adf14a18ffa6c57f8a59670c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:38:54') 
2025-06-21T11:38:56.333+08:00 [DEBU] {b4865d5aaef14a1801a7c57f8d905d3c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:38:56') 
2025-06-21T11:39:22.823+08:00 [DEBU] {a4f30b85b4f14a1802a7c57fe73205de} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'平台群','1','group_type','','',0,1,31,'','2025-06-21 11:39:22','2025-06-21 11:39:22') 
2025-06-21T11:39:22.828+08:00 [DEBU] {d053a785b4f14a1803a7c57fe1e2a6f7} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"平台群","dictSort":0,"dictType":"group_type","dictValue":"1","isDefault":0,"remark":"","status":1}','2025-06-21 11:39:22') 
2025-06-21T11:39:22.845+08:00 [DEBU] {cc56a286b4f14a1804a7c57f4c929a5d} [  2 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:39:22.847+08:00 [DEBU] {cc56a286b4f14a1804a7c57f4c929a5d} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:39:22.848+08:00 [DEBU] {cc56a286b4f14a1804a7c57f4c929a5d} [  0 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:39:22.854+08:00 [DEBU] {48112a87b4f14a1805a7c57fb7614eaa} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:39:22') 
2025-06-21T11:39:25.549+08:00 [DEBU] {f80cdd27b5f14a1807a7c57f06fce7d1} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:39:25') 
2025-06-21T11:39:34.911+08:00 [DEBU] {bc1f0955b7f14a1808a7c57f3d97c1b5} [ 16 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'团队群','2','group_type','','',1,1,31,'','2025-06-21 11:39:34','2025-06-21 11:39:34') 
2025-06-21T11:39:34.941+08:00 [DEBU] {60f51f56b7f14a1809a7c57f872ed5c1} [ 15 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"团队群","dictSort":0,"dictType":"group_type","dictValue":"2","isDefault":1,"remark":"","status":1}','2025-06-21 11:39:34') 
2025-06-21T11:39:34.945+08:00 [DEBU] {58d61d57b7f14a180aa7c57ff60f0325} [ 15 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:39:34.948+08:00 [DEBU] {58d61d57b7f14a180aa7c57ff60f0325} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:39:34.949+08:00 [DEBU] {58d61d57b7f14a180aa7c57ff60f0325} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:39:34.957+08:00 [DEBU] {1c3b7158b7f14a180ba7c57fab1889b6} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:39:34') 
2025-06-21T11:39:39.879+08:00 [DEBU] {d8bef67db8f14a180da7c57f268c1cef} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:39:39') 
2025-06-21T11:39:52.745+08:00 [DEBU] {f0e5d87cbbf14a180ea7c57f87220445} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'项目群','3','group_type','','',0,1,31,'','2025-06-21 11:39:52','2025-06-21 11:39:52') 
2025-06-21T11:39:52.750+08:00 [DEBU] {30bb277dbbf14a180fa7c57fd51ad9bf} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"项目群","dictSort":0,"dictType":"group_type","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 11:39:52') 
2025-06-21T11:39:52.766+08:00 [DEBU] {fcb2217ebbf14a1810a7c57f7ad02551} [  2 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:39:52.767+08:00 [DEBU] {fcb2217ebbf14a1810a7c57f7ad02551} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:39:52.768+08:00 [DEBU] {fcb2217ebbf14a1810a7c57f7ad02551} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:39:52.774+08:00 [DEBU] {6c4f8d7ebbf14a1811a7c57fab454663} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:39:52') 
2025-06-21T11:40:03.397+08:00 [DEBU] {382e50f5bdf14a1813a7c57f9c5a5b7a} [ 43 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:40:03') 
2025-06-21T11:40:05.331+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:40:05.332+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:40:05.333+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:40:05.333+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:40:05.340+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:40:05','在线用户定时更新，执行成功') 
2025-06-21T11:40:17.003+08:00 [DEBU] {a8e99e22c1f14a1814a7c57f5ef3e061} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'兴趣群','4','group_type','','',0,1,31,'','2025-06-21 11:40:16','2025-06-21 11:40:16') 
2025-06-21T11:40:17.007+08:00 [DEBU] {403af722c1f14a1815a7c57f1d75e07b} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"兴趣群","dictSort":0,"dictType":"group_type","dictValue":"4","isDefault":0,"remark":"","status":1}','2025-06-21 11:40:17') 
2025-06-21T11:40:17.022+08:00 [DEBU] {784bee23c1f14a1816a7c57f90b89235} [  2 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:40:17.024+08:00 [DEBU] {784bee23c1f14a1816a7c57f90b89235} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:40:17.025+08:00 [DEBU] {784bee23c1f14a1816a7c57f90b89235} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:40:17.030+08:00 [DEBU] {44c65124c1f14a1817a7c57f8082c674} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:40:17') 
2025-06-21T11:40:19.820+08:00 [DEBU] {88e5abcac1f14a1819a7c57fbc50a4dc} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:40:19') 
2025-06-21T11:40:29.546+08:00 [DEBU] {b016490ec4f14a181aa7c57f30b5c88f} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'其他群','5','group_type','','',0,1,31,'','2025-06-21 11:40:29','2025-06-21 11:40:29') 
2025-06-21T11:40:29.551+08:00 [DEBU] {50a8a80ec4f14a181ba7c57f124a79df} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"其他群","dictSort":0,"dictType":"group_type","dictValue":"5","isDefault":0,"remark":"","status":1}','2025-06-21 11:40:29') 
2025-06-21T11:40:29.567+08:00 [DEBU] {ccaca90fc4f14a181ca7c57fdb0b6ea4} [  1 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:40:29.569+08:00 [DEBU] {ccaca90fc4f14a181ca7c57fdb0b6ea4} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T11:40:29.570+08:00 [DEBU] {ccaca90fc4f14a181ca7c57fdb0b6ea4} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:40:29.574+08:00 [DEBU] {30f10a10c4f14a181da7c57fa43f1f33} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 11:40:29') 
2025-06-21T11:41:20.607+08:00 [DEBU] {cc5f86f1cff14a181fa7c57f3f82a41e} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T11:41:20.608+08:00 [DEBU] {cc5f86f1cff14a181fa7c57f3f82a41e} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:41:20.619+08:00 [DEBU] {74612cf2cff14a1820a7c57f9b24dbaf} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 11:41:20') 
2025-06-21T11:41:28.311+08:00 [DEBU] {8cac1fbdd1f14a1821a7c57fc711fb39} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:41:28.312+08:00 [DEBU] {8cac1fbdd1f14a1821a7c57fc711fb39} [  0 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:41:28.316+08:00 [DEBU] {f0445abdd1f14a1822a7c57f4718c0bc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:41:28') 
2025-06-21T11:41:34.943+08:00 [DEBU] {84d56748d3f14a1823a7c57f417fb8a9} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T11:41:34.944+08:00 [DEBU] {84d56748d3f14a1823a7c57f417fb8a9} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:41:34.949+08:00 [DEBU] {b0edaa48d3f14a1824a7c57ff7761b53} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 11:41:34') 
2025-06-21T11:41:38.381+08:00 [DEBU] {8c5b5215d4f14a1825a7c57fb8c98376} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T11:41:38.382+08:00 [DEBU] {8c5b5215d4f14a1825a7c57fb8c98376} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:41:38.386+08:00 [DEBU] {d81e9315d4f14a1826a7c57f708c7406} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 11:41:38') 
2025-06-21T11:41:40.518+08:00 [DEBU] {282ea594d4f14a1828a7c57fe466e6b1} [  2 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T11:41:40.524+08:00 [DEBU] {********************************} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:41:40') 
2025-06-21T11:41:40.529+08:00 [DEBU] {9cf0eb94d4f14a182ba7c57f129b9ff0} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 11:41:40') 
2025-06-21T11:41:51.143+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  1 ms] [default] [gfast] [rows:0  ] [txid:2] BEGIN (IosolationLevel: Default, ReadOnly: false)
2025-06-21T11:41:51.146+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  2 ms] [default] [gfast] [rows:0  ] [txid:2] SELECT `dict_id` FROM `sys_dict_type` WHERE (`dict_type`='sys_job_status') AND (dict_id !=3) LIMIT 1
2025-06-21T11:41:51.149+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  2 ms] [default] [gfast] [rows:1  ] [txid:2] SELECT `dict_type` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T11:41:51.151+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  2 ms] [default] [gfast] [rows:1  ] [txid:2] UPDATE `sys_dict_type` SET `pid`=54,`dict_name`='工作状态',`dict_type`='sys_job_status',`status`=1,`update_by`=31,`remark`='任务状态列表',`updated_at`='2025-06-21 11:41:51' WHERE `dict_id`=3
2025-06-21T11:41:51.153+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  2 ms] [default] [gfast] [rows:2  ] [txid:2] UPDATE `sys_dict_data` SET `dict_type`='sys_job_status',`updated_at`='2025-06-21 11:41:51' WHERE `dict_type`='sys_job_status'
2025-06-21T11:41:51.156+08:00 [DEBU] {54a2fc0dd7f14a182ca7c57f4914e1dd} [  3 ms] [default] [gfast] [rows:0  ] [txid:2] COMMIT
2025-06-21T11:41:51.166+08:00 [DEBU] {b402f50ed7f14a182da7c57f7e326500} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/type/edit','::1','内网IP','{"createBy":31,"createdAt":null,"dictId":3,"dictName":"工作状态","dictType":"sys_job_status","pid":54,"remark":"任务状态列表","status":1,"updateBy":31,"updatedAt":null}','2025-06-21 11:41:51') 
2025-06-21T11:41:51.180+08:00 [DEBU] {dcf21c10d7f14a182ea7c57f0f094f84} [  2 ms] [default] [gfast] [rows:33 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T11:41:51.187+08:00 [DEBU] {2c117710d7f14a182fa7c57ffd186b22} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:41:51') 
2025-06-21T11:41:54.715+08:00 [DEBU] {0880cce2d7f14a1831a7c57ff2e1c13d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:41:54') 
2025-06-21T11:41:54.716+08:00 [DEBU] {b49ffbe2d7f14a1832a7c57f0dbf3769} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T11:41:54.723+08:00 [DEBU] {********************************} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 11:41:54') 
2025-06-21T11:42:03.504+08:00 [DEBU] {d0e8c9eed9f14a1835a7c57f35dd1787} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T11:42:03.506+08:00 [DEBU] {b878d4eed9f14a1836a7c57f2d48afd3} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:42:03') 
2025-06-21T11:42:03.508+08:00 [DEBU] {5479f1eed9f14a1837a7c57f18a605e1} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 11:42:03') 
2025-06-21T11:42:16.214+08:00 [DEBU] {fced13e4dcf14a1839a7c57fb1c1d979} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_group')
2025-06-21T11:42:16.215+08:00 [DEBU] {fced13e4dcf14a1839a7c57fb1c1d979} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_group') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:42:16.220+08:00 [DEBU] {5c0b96e4dcf14a183aa7c57fd6ee1124} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=13','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"13"}','2025-06-21 11:42:16') 
2025-06-21T11:42:20.215+08:00 [DEBU] {3045d0d2ddf14a183ba7c57f3399936a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T11:42:20.215+08:00 [DEBU] {3045d0d2ddf14a183ba7c57f3399936a} [  0 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:42:20.219+08:00 [DEBU] {186801d3ddf14a183ca7c57fe45c7609} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 11:42:20') 
2025-06-21T11:42:49.134+08:00 [DEBU] {fcc6108ee4f14a183da7c57f32f2fc23} [  9 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T11:42:49.135+08:00 [DEBU] {fcc6108ee4f14a183da7c57f32f2fc23} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:42:49.139+08:00 [DEBU] {582abf8ee4f14a183ea7c57fce0833dc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 11:42:49') 
2025-06-21T11:42:51.861+08:00 [DEBU] {b45e1831e5f14a183fa7c57f1d191eb8} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T11:42:51.862+08:00 [DEBU] {b45e1831e5f14a183fa7c57f1d191eb8} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T11:42:51.878+08:00 [DEBU] {b4855031e5f14a1840a7c57fda8f1d22} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 11:42:51') 
2025-06-21T11:42:54.282+08:00 [DEBU] {a86654c1e5f14a1842a7c57f7c6e52ad} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 11:42:54') 
2025-06-21T11:50:05.335+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 10 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:50:05.336+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T11:50:05.337+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T11:50:05.338+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T11:50:05.344+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 11:50:05','在线用户定时更新，执行成功') 
2025-06-21T12:00:05.734+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:00:05.735+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:00:05.736+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:00:05.736+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:00:05.750+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:00:05','在线用户定时更新，执行成功') 
2025-06-21T12:10:05.038+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 13 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:10:05.041+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:10:05.042+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:10:05.042+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:10:05.047+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:10:05','在线用户定时更新，执行成功') 
2025-06-21T12:20:05.030+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:20:05.033+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:20:05.034+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:20:05.035+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:20:05.037+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:20:05','在线用户定时更新，执行成功') 
2025-06-21T12:30:05.332+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:30:05.333+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:30:05.335+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:30:05.335+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:30:05.340+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:30:05','在线用户定时更新，执行成功') 
2025-06-21T12:40:05.845+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 20 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:40:05.847+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:40:05.848+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:40:05.849+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:40:05.858+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:40:05','在线用户定时更新，执行成功') 
2025-06-21T12:50:05.834+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:50:05.836+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T12:50:05.836+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T12:50:05.837+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T12:50:05.842+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 12:50:05','在线用户定时更新，执行成功') 
2025-06-21T13:00:05.037+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 12 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T13:00:05.038+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T13:00:05.039+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T13:00:05.040+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T13:00:05.043+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 13:00:05','在线用户定时更新，执行成功') 
2025-06-21T15:40:05.936+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T15:40:05.938+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T15:40:05.952+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 13 ms] [default] [gfast] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKQ0+g068Z1wN1Q3kUujPN7Y6J19Pq4xVyOG6lBlkzx18icuYZpWlfLyf53ZIn7yueiI76Ya6ePvYYr5Gx4dUZow=='
2025-06-21T15:40:05.952+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T15:40:05.955+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T15:40:05.959+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 15:40:05','在线用户定时更新，执行成功') 
2025-06-21T15:50:05.947+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 22 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T15:50:05.949+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T15:50:05.967+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 18 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 15:50:05','在线用户定时更新，执行成功') 
2025-06-21T16:00:05.041+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 16 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:00:05.043+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:00:05.050+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:00:05','在线用户定时更新，执行成功') 
2025-06-21T16:10:05.633+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:10:05.634+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:10:05.640+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:10:05','在线用户定时更新，执行成功') 
2025-06-21T16:20:05.631+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:20:05.632+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:20:05.635+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:20:05','在线用户定时更新，执行成功') 
2025-06-21T16:26:24.819+08:00 [DEBU] {b8b175535e014b18a9a7c57f22b5b625} [ 14 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T16:26:24.828+08:00 [DEBU] {b8b175535e014b18a9a7c57f22b5b625} [  4 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 16:26:24' WHERE `id`=31
2025-06-21T16:26:24.843+08:00 [DEBU] {e0b506555e014b18aaa7c57fc942ba57} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 16:26:24','系统后台') 
2025-06-21T16:26:24.855+08:00 [DEBU] {74aece555e014b18aba7c57fcc5b5156} [ 13 ms] [default] [gfast] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKK1Nx9f0AIM1G/EzS9Ln2hXSQ0Q6aWvrq3a6EXsgD6ZtUadl49HstuXeL+MauC4Cz2GV3sLcdd/n3IAUJWV51oQ==' LIMIT 1
2025-06-21T16:26:24.860+08:00 [DEBU] {74aece555e014b18aba7c57fcc5b5156} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('21a91bba71446500e5e5b6adccccd8d1','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKK1Nx9f0AIM1G/EzS9Ln2hXSQ0Q6aWvrq3a6EXsgD6ZtUadl49HstuXeL+MauC4Cz2GV3sLcdd/n3IAUJWV51oQ==','2025-06-21 16:26:24','demo','::1','Chrome','Windows 10') 
2025-06-21T16:26:29.700+08:00 [DEBU] {309243775f014b18aca7c57fc7537a9d} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T16:26:29.705+08:00 [DEBU] {309243775f014b18aca7c57fc7537a9d} [  3 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:26:29.709+08:00 [DEBU] {1c4f5e775f014b18aea7c57f77647e61} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:26:29') 
2025-06-21T16:26:29.714+08:00 [DEBU] {8c8fe9775f014b18afa7c57f14aef2c1} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 16:26:29') 
2025-06-21T16:26:33.775+08:00 [DEBU] {488c1a6a60014b18b1a7c57f6e029351} [  3 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_type')
2025-06-21T16:26:33.777+08:00 [DEBU] {488c1a6a60014b18b1a7c57f6e029351} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:26:33.783+08:00 [DEBU] {f876936a60014b18b2a7c57f5a00a4c3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=61','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"61"}','2025-06-21 16:26:33') 
2025-06-21T16:27:17.532+08:00 [DEBU] {3c99cb996a014b18b5a7c57fb0f3bc82} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:27:17') 
2025-06-21T16:27:40.846+08:00 [DEBU] {fc155e0670014b18b7a7c57f471a0b86} [ 27 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='content_visibility' LIMIT 1
2025-06-21T16:27:40.854+08:00 [DEBU] {fc155e0670014b18b7a7c57f471a0b86} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'可见范围','content_visibility',1,31,'','2025-06-21 16:27:40','2025-06-21 16:27:40') 
2025-06-21T16:27:40.860+08:00 [DEBU] {9404b20870014b18b8a7c57ff9a71698} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"可见范围","dictType":"content_visibility","pid":54,"remark":"","status":1}','2025-06-21 16:27:40') 
2025-06-21T16:27:40.891+08:00 [DEBU] {787f830a70014b18b9a7c57ff7f9ba3f} [  3 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:27:40.896+08:00 [DEBU] {10e2d80a70014b18baa7c57f7d0b81be} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:27:40') 
2025-06-21T16:27:42.620+08:00 [DEBU] {8835a17170014b18bca7c57fc69ae423} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:27:42.621+08:00 [DEBU] {8835a17170014b18bca7c57fc69ae423} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:27:42.626+08:00 [DEBU] {50c4fe7170014b18bda7c57f1a4a9a8f} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:27:42') 
2025-06-21T16:27:45.657+08:00 [DEBU] {5cc69f2671014b18bfa7c57f40b76620} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:27:45') 
2025-06-21T16:28:06.461+08:00 [DEBU] {b8d03efe75014b18c1a7c57f0bb10c79} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'所有人','1','content_visibility','','',0,1,31,'','2025-06-21 16:28:06','2025-06-21 16:28:06') 
2025-06-21T16:28:06.465+08:00 [DEBU] {4c89e7fe75014b18c2a7c57fd8381179} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"所有人","dictSort":0,"dictType":"content_visibility","dictValue":"1","isDefault":0,"remark":"","status":1}','2025-06-21 16:28:06') 
2025-06-21T16:28:06.488+08:00 [DEBU] {9cf7360076014b18c3a7c57ff4e142a2} [  3 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:28:06.490+08:00 [DEBU] {9cf7360076014b18c3a7c57ff4e142a2} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:28:06.492+08:00 [DEBU] {9cf7360076014b18c3a7c57ff4e142a2} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:28:06.498+08:00 [DEBU] {0038d30076014b18c4a7c57fdaea3b99} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:28:06') 
2025-06-21T16:28:08.522+08:00 [DEBU] {40d87e7976014b18c6a7c57fe6b9b1e3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:28:08') 
2025-06-21T16:28:23.121+08:00 [DEBU] {68cc73ba79014b18c7a7c57f33e3bdf9} [627 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'平台可见','2','content_visibility','','',0,1,31,'','2025-06-21 16:28:22','2025-06-21 16:28:22') 
2025-06-21T16:28:23.316+08:00 [DEBU] {e4b37ae179014b18c9a7c57f4f413247} [165 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:28:23.318+08:00 [DEBU] {e4b37ae179014b18c9a7c57f4f413247} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:28:23.319+08:00 [DEBU] {e4b37ae179014b18c9a7c57f4f413247} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:28:23.413+08:00 [DEBU] {601508e079014b18c8a7c57f10844474} [288 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"平台可见","dictSort":0,"dictType":"content_visibility","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 16:28:23') 
2025-06-21T16:28:23.418+08:00 [DEBU] {5c59b2eb79014b18caa7c57fc0f69151} [ 97 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:28:23') 
2025-06-21T16:28:26.529+08:00 [DEBU] {dcabd4aa7a014b18cca7c57f27c23f12} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:28:26') 
2025-06-21T16:28:37.131+08:00 [DEBU] {fcaa1d227d014b18cda7c57fb7c20160} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'团队可见','3','content_visibility','','',1,1,31,'','2025-06-21 16:28:37','2025-06-21 16:28:37') 
2025-06-21T16:28:37.138+08:00 [DEBU] {8c111c237d014b18cea7c57f93e00d2f} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"团队可见","dictSort":0,"dictType":"content_visibility","dictValue":"3","isDefault":1,"remark":"","status":1}','2025-06-21 16:28:37') 
2025-06-21T16:28:37.157+08:00 [DEBU] {dcd943247d014b18cfa7c57f86802e4c} [  2 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:28:37.160+08:00 [DEBU] {dcd943247d014b18cfa7c57f86802e4c} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:28:37.161+08:00 [DEBU] {dcd943247d014b18cfa7c57f86802e4c} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:28:37.168+08:00 [DEBU] {8092cd247d014b18d0a7c57f06815c39} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:28:37') 
2025-06-21T16:28:39.266+08:00 [DEBU] {7479f7a17d014b18d2a7c57f0dd7db3d} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:28:39') 
2025-06-21T16:28:50.553+08:00 [DEBU] {84d8b74280014b18d3a7c57f84c33ee6} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'好友可见','4','content_visibility','','',0,1,31,'','2025-06-21 16:28:50','2025-06-21 16:28:50') 
2025-06-21T16:28:50.563+08:00 [DEBU] {989e4b4380014b18d4a7c57f3435795c} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"好友可见","dictSort":0,"dictType":"content_visibility","dictValue":"4","isDefault":0,"remark":"","status":1}','2025-06-21 16:28:50') 
2025-06-21T16:28:50.605+08:00 [DEBU] {f46ec74580014b18d5a7c57fc9d6b6f7} [  3 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:28:50.618+08:00 [DEBU] {f46ec74580014b18d5a7c57fc9d6b6f7} [ 10 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:28:50.621+08:00 [DEBU] {f46ec74580014b18d5a7c57fc9d6b6f7} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:28:50.633+08:00 [DEBU] {f0d0124780014b18d6a7c57f1075b070} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:28:50') 
2025-06-21T16:28:59.871+08:00 [DEBU] {b092c26d82014b18d8a7c57f1d13408e} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:28:59') 
2025-06-21T16:29:14.379+08:00 [DEBU] {307eacce85014b18d9a7c57ff712df1e} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'仅自己','5','content_visibility','','',0,1,31,'','2025-06-21 16:29:14','2025-06-21 16:29:14') 
2025-06-21T16:29:14.389+08:00 [DEBU] {00f438cf85014b18daa7c57f83efb3db} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"仅自己","dictSort":0,"dictType":"content_visibility","dictValue":"5","isDefault":0,"remark":"","status":1}','2025-06-21 16:29:14') 
2025-06-21T16:29:14.407+08:00 [DEBU] {446a6ed085014b18dba7c57f6e41502a} [  3 ms] [default] [gfast] [rows:34 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:29:14.409+08:00 [DEBU] {446a6ed085014b18dba7c57f6e41502a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility')
2025-06-21T16:29:14.412+08:00 [DEBU] {446a6ed085014b18dba7c57f6e41502a} [  1 ms] [default] [gfast] [rows:5  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('content_visibility') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:29:14.422+08:00 [DEBU] {20d63ed185014b18dca7c57f243b88bc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=62','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"62"}','2025-06-21 16:29:14') 
2025-06-21T16:29:38.512+08:00 [DEBU] {48d8dc6c8b014b18dea7c57fa3391929} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:29:38') 
2025-06-21T16:30:05.826+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:30:05.827+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:30:05.828+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:30:05.829+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T16:30:05.832+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:30:05','在线用户定时更新，执行成功') 
2025-06-21T16:30:10.311+08:00 [DEBU] {840938d492014b18dfa7c57f74db6599} [  9 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='sign_in_reward_type' LIMIT 1
2025-06-21T16:30:10.314+08:00 [DEBU] {840938d492014b18dfa7c57f74db6599} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'签到奖励','sign_in_reward_type',1,31,'','2025-06-21 16:30:10','2025-06-21 16:30:10') 
2025-06-21T16:30:10.319+08:00 [DEBU] {508828d592014b18e0a7c57f067d843d} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"签到奖励","dictType":"sign_in_reward_type","pid":54,"remark":"","status":1}','2025-06-21 16:30:10') 
2025-06-21T16:30:10.337+08:00 [DEBU] {d49a45d692014b18e1a7c57f1f5a1ff4} [  2 ms] [default] [gfast] [rows:35 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:30:10.341+08:00 [DEBU] {accb82d692014b18e2a7c57f5a71c3b8} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:30:10') 
2025-06-21T16:30:14.465+08:00 [DEBU] {60814acc93014b18e4a7c57f340ff1cb} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type')
2025-06-21T16:30:14.465+08:00 [DEBU] {60814acc93014b18e4a7c57f340ff1cb} [  0 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:30:14.470+08:00 [DEBU] {28f892cc93014b18e5a7c57f1a689243} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=63','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"63"}','2025-06-21 16:30:14') 
2025-06-21T16:30:18.280+08:00 [DEBU] {e094a5af94014b18e7a7c57f62ce1406} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:30:18') 
2025-06-21T16:30:29.480+08:00 [DEBU] {c881414b97014b18e8a7c57fda8c98a3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'积分','1','sign_in_reward_type','','',1,1,31,'','2025-06-21 16:30:29','2025-06-21 16:30:29') 
2025-06-21T16:30:29.484+08:00 [DEBU] {14d47f4b97014b18e9a7c57fadee1881} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"积分","dictSort":0,"dictType":"sign_in_reward_type","dictValue":"1","isDefault":1,"remark":"","status":1}','2025-06-21 16:30:29') 
2025-06-21T16:30:29.500+08:00 [DEBU] {dc50764c97014b18eaa7c57f3a352c7d} [  1 ms] [default] [gfast] [rows:35 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:30:29.502+08:00 [DEBU] {dc50764c97014b18eaa7c57f3a352c7d} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type')
2025-06-21T16:30:29.503+08:00 [DEBU] {dc50764c97014b18eaa7c57f3a352c7d} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:30:29.508+08:00 [DEBU] {d07be84c97014b18eba7c57f9593ae39} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=63','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"63"}','2025-06-21 16:30:29') 
2025-06-21T16:30:31.049+08:00 [DEBU] {7c25cda897014b18eda7c57fc0f7136a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:30:31') 
2025-06-21T16:30:47.261+08:00 [DEBU] {e0f5d86e9b014b18eea7c57f1737392f} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'成长值','2','sign_in_reward_type','','',0,1,31,'','2025-06-21 16:30:47','2025-06-21 16:30:47') 
2025-06-21T16:30:47.267+08:00 [DEBU] {84dd566f9b014b18efa7c57f4f30b203} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"成长值","dictSort":0,"dictType":"sign_in_reward_type","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 16:30:47') 
2025-06-21T16:30:47.286+08:00 [DEBU] {2c2f75709b014b18f0a7c57f7220736a} [  3 ms] [default] [gfast] [rows:35 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:30:47.288+08:00 [DEBU] {2c2f75709b014b18f0a7c57f7220736a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type')
2025-06-21T16:30:47.290+08:00 [DEBU] {2c2f75709b014b18f0a7c57f7220736a} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:30:47.296+08:00 [DEBU] {10d003719b014b18f1a7c57f8d56a397} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=63','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"63"}','2025-06-21 16:30:47') 
2025-06-21T16:30:49.094+08:00 [DEBU] {f8d851dc9b014b18f3a7c57f111172fe} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:30:49') 
2025-06-21T16:31:08.019+08:00 [DEBU] {b4993144a0014b18f4a7c57fdfc25d60} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'卡券','3','sign_in_reward_type','','',0,1,31,'','2025-06-21 16:31:08','2025-06-21 16:31:08') 
2025-06-21T16:31:08.023+08:00 [DEBU] {c44f9344a0014b18f5a7c57fca786c5a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"卡券","dictSort":0,"dictType":"sign_in_reward_type","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 16:31:08') 
2025-06-21T16:31:08.036+08:00 [DEBU] {549b7045a0014b18f6a7c57f008f7d50} [  1 ms] [default] [gfast] [rows:35 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:31:08.038+08:00 [DEBU] {549b7045a0014b18f6a7c57f008f7d50} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type')
2025-06-21T16:31:08.040+08:00 [DEBU] {549b7045a0014b18f6a7c57f008f7d50} [  2 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sign_in_reward_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:31:08.045+08:00 [DEBU] {c0d9cf45a0014b18f7a7c57f483a165d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=63','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"63"}','2025-06-21 16:31:08') 
2025-06-21T16:32:19.585+08:00 [DEBU] {842fb2edb0014b18f9a7c57f40aa3e1b} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:32:19') 
2025-06-21T16:32:32.773+08:00 [DEBU] {dc981200b4014b18faa7c57f921cabb4} [  2 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='promotion_status' LIMIT 1
2025-06-21T16:32:32.777+08:00 [DEBU] {dc981200b4014b18faa7c57f921cabb4} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'推广状态','promotion_status',1,31,'','2025-06-21 16:32:32','2025-06-21 16:32:32') 
2025-06-21T16:32:32.783+08:00 [DEBU] {00719d00b4014b18fba7c57f8c4b7755} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"推广状态","dictType":"promotion_status","pid":54,"remark":"","status":1}','2025-06-21 16:32:32') 
2025-06-21T16:32:32.802+08:00 [DEBU] {20f3b801b4014b18fca7c57fc299ae26} [  3 ms] [default] [gfast] [rows:36 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:32:32.807+08:00 [DEBU] {1c011802b4014b18fda7c57fbeec99e5} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:32:32') 
2025-06-21T16:32:34.215+08:00 [DEBU] {4cc29b55b4014b18ffa7c57f3d8009d7} [  9 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status')
2025-06-21T16:32:34.216+08:00 [DEBU] {4cc29b55b4014b18ffa7c57f3d8009d7} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:32:34.220+08:00 [DEBU] {3cd15a56b4014b1800a8c57f1436762a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=64','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"64"}','2025-06-21 16:32:34') 
2025-06-21T16:32:36.005+08:00 [DEBU] {8c1f89c0b4014b1802a8c57f691307a2} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:32:35') 
2025-06-21T16:32:53.993+08:00 [DEBU] {409648f0b8014b1803a8c57fb856d375} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'审核中','1','promotion_status','','',1,1,31,'','2025-06-21 16:32:53','2025-06-21 16:32:53') 
2025-06-21T16:32:54.000+08:00 [DEBU] {680c31f1b8014b1804a8c57f97ce5ab2} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"审核中","dictSort":0,"dictType":"promotion_status","dictValue":"1","isDefault":1,"remark":"","status":1}','2025-06-21 16:32:53') 
2025-06-21T16:32:54.014+08:00 [DEBU] {4ce82ef2b8014b1805a8c57fe6057f7e} [  2 ms] [default] [gfast] [rows:36 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:32:54.018+08:00 [DEBU] {4ce82ef2b8014b1805a8c57fe6057f7e} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status')
2025-06-21T16:32:54.019+08:00 [DEBU] {4ce82ef2b8014b1805a8c57fe6057f7e} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:32:54.024+08:00 [DEBU] {985abbf2b8014b1806a8c57fecd9d150} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=64','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"64"}','2025-06-21 16:32:54') 
2025-06-21T16:32:56.023+08:00 [DEBU] {3435e169b9014b1808a8c57f50b42c38} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:32:56') 
2025-06-21T16:33:04.820+08:00 [DEBU] {a4db1c75bb014b1809a8c57f67802212} [ 20 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'进行中','2','promotion_status','','',0,1,31,'','2025-06-21 16:33:04','2025-06-21 16:33:04') 
2025-06-21T16:33:04.837+08:00 [DEBU] {7c607876bb014b180aa8c57f220b6acc} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"进行中","dictSort":0,"dictType":"promotion_status","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 16:33:04') 
2025-06-21T16:33:04.850+08:00 [DEBU] {18dc1d78bb014b180ba8c57f8de0e0df} [  1 ms] [default] [gfast] [rows:36 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:33:04.856+08:00 [DEBU] {18dc1d78bb014b180ba8c57f8de0e0df} [  4 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status')
2025-06-21T16:33:04.859+08:00 [DEBU] {18dc1d78bb014b180ba8c57f8de0e0df} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:33:04.865+08:00 [DEBU] {f0f6e378bb014b180ca8c57f084f2134} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=64','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"64"}','2025-06-21 16:33:04') 
2025-06-21T16:33:06.484+08:00 [DEBU] {dcc851d9bb014b180ea8c57fbc8cf558} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:33:06') 
2025-06-21T16:33:15.063+08:00 [DEBU] {b0cbb5d8bd014b180fa8c57fc7816ff7} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'已结束','3','promotion_status','','',0,1,31,'','2025-06-21 16:33:15','2025-06-21 16:33:15') 
2025-06-21T16:33:15.066+08:00 [DEBU] {584601d9bd014b1810a8c57f07452bda} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"已结束","dictSort":0,"dictType":"promotion_status","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 16:33:15') 
2025-06-21T16:33:15.084+08:00 [DEBU] {c8c010dabd014b1811a8c57f9f163dc1} [  1 ms] [default] [gfast] [rows:36 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:33:15.086+08:00 [DEBU] {c8c010dabd014b1811a8c57f9f163dc1} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status')
2025-06-21T16:33:15.088+08:00 [DEBU] {c8c010dabd014b1811a8c57f9f163dc1} [  2 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('promotion_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:33:15.095+08:00 [DEBU] {00b48fdabd014b1812a8c57f2af5bc6a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=64','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"64"}','2025-06-21 16:33:15') 
2025-06-21T16:33:30.044+08:00 [DEBU] {ccf36e55c1014b1814a8c57fb625c605} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T16:33:30.045+08:00 [DEBU] {ccf36e55c1014b1814a8c57fb625c605} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:33:30.050+08:00 [DEBU] {cc330b56c1014b1815a8c57fe543a602} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 16:33:30') 
2025-06-21T16:33:35.600+08:00 [DEBU] {cc50fd9fc2014b1817a8c57f899ef970} [ 17 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:33:35') 
2025-06-21T16:33:35.613+08:00 [DEBU] {60e65ea1c2014b1819a8c57f7f225f1e} [  2 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T16:33:35.618+08:00 [DEBU] {f083f4a1c2014b181aa8c57f0d944943} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 16:33:35') 
2025-06-21T16:33:41.530+08:00 [DEBU] {fccd6902c4014b181ba8c57f2c5e6c88} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status')
2025-06-21T16:33:41.531+08:00 [DEBU] {fccd6902c4014b181ba8c57f2c5e6c88} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:33:41.534+08:00 [DEBU] {b4a69702c4014b181ca8c57f6b809ced} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 16:33:41') 
2025-06-21T16:33:45.079+08:00 [DEBU] {98dad8d5c4014b181fa8c57ff97648a7} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:33:45') 
2025-06-21T16:33:45.084+08:00 [DEBU] {946ec6d5c4014b181ea8c57f168a1859} [  6 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T16:33:45.090+08:00 [DEBU] {b8327ed6c4014b1820a8c57ff6d8c3b2} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 16:33:45') 
2025-06-21T16:33:59.888+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  0 ms] [default] [gfast] [rows:0  ] [txid:3] BEGIN (IosolationLevel: Default, ReadOnly: false)
2025-06-21T16:33:59.890+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  1 ms] [default] [gfast] [rows:0  ] [txid:3] SELECT `dict_id` FROM `sys_dict_type` WHERE (`dict_type`='sys_work_status') AND (dict_id !=3) LIMIT 1
2025-06-21T16:33:59.891+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  0 ms] [default] [gfast] [rows:1  ] [txid:3] SELECT `dict_type` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T16:33:59.894+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  2 ms] [default] [gfast] [rows:1  ] [txid:3] UPDATE `sys_dict_type` SET `pid`=54,`dict_name`='工作状态',`dict_type`='sys_work_status',`status`=1,`update_by`=31,`remark`='任务状态列表',`updated_at`='2025-06-21 16:33:59' WHERE `dict_id`=3
2025-06-21T16:33:59.896+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  1 ms] [default] [gfast] [rows:2  ] [txid:3] UPDATE `sys_dict_data` SET `dict_type`='sys_work_status',`updated_at`='2025-06-21 16:33:59' WHERE `dict_type`='sys_job_status'
2025-06-21T16:33:59.897+08:00 [DEBU] {d068a648c8014b1822a8c57f2aedfcd4} [  1 ms] [default] [gfast] [rows:0  ] [txid:3] COMMIT
2025-06-21T16:33:59.903+08:00 [DEBU] {08b86949c8014b1823a8c57f8e4043c3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/type/edit','::1','内网IP','{"createBy":31,"createdAt":null,"dictId":3,"dictName":"工作状态","dictType":"sys_work_status","pid":54,"remark":"任务状态列表","status":1,"updateBy":31,"updatedAt":"2025-06-21 11:41:51"}','2025-06-21 16:33:59') 
2025-06-21T16:33:59.921+08:00 [DEBU] {1cff8a4ac8014b1824a8c57faa6d2e67} [  2 ms] [default] [gfast] [rows:36 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:33:59.926+08:00 [DEBU] {30f8d74ac8014b1825a8c57f27236400} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:33:59') 
2025-06-21T16:34:10.158+08:00 [DEBU] {f814c1acca014b1827a8c57f4ea20ef4} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T16:34:10.158+08:00 [DEBU] {f814c1acca014b1827a8c57f4ea20ef4} [  0 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:34:10.163+08:00 [DEBU] {1c94faacca014b1828a8c57fbf9d9c69} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 16:34:10') 
2025-06-21T16:34:15.224+08:00 [DEBU] {94c969dacb014b1829a8c57f19a4aa8b} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status')
2025-06-21T16:34:15.225+08:00 [DEBU] {94c969dacb014b1829a8c57f19a4aa8b} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:34:15.230+08:00 [DEBU] {4cdef1dacb014b182aa8c57fb505224d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 16:34:15') 
2025-06-21T16:34:19.467+08:00 [DEBU] {f839a1d7cc014b182ba8c57f182d810a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T16:34:19.468+08:00 [DEBU] {f839a1d7cc014b182ba8c57f182d810a} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:34:19.472+08:00 [DEBU] {5486dad7cc014b182ca8c57f070c4331} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 16:34:19') 
2025-06-21T16:34:22.156+08:00 [DEBU] {cc5ad477cd014b182da8c57fcdac8634} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status')
2025-06-21T16:34:22.157+08:00 [DEBU] {cc5ad477cd014b182da8c57fcdac8634} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:34:22.161+08:00 [DEBU] {78b21b78cd014b182ea8c57f37a46922} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 16:34:22') 
2025-06-21T16:34:26.458+08:00 [DEBU] {a4ac5878ce014b182fa8c57f679da726} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T16:34:26.459+08:00 [DEBU] {a4ac5878ce014b182fa8c57f679da726} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:34:26.464+08:00 [DEBU] {4cbf9378ce014b1830a8c57f94383e79} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 16:34:26') 
2025-06-21T16:34:32.403+08:00 [DEBU] {1c1399dacf014b1832a8c57f3cbd4ae7} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) LIMIT 1
2025-06-21T16:34:32.406+08:00 [DEBU] {1c1399dacf014b1832a8c57f3cbd4ae7} [  2 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T16:34:32.414+08:00 [DEBU] {947031dbcf014b1833a8c57f86fcb50d} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_yes_no&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_yes_no"}','2025-06-21 16:34:32') 
2025-06-21T16:34:32.435+08:00 [DEBU] {b834e9dbcf014b1835a8c57f8e9dd5ad} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_config`
2025-06-21T16:34:32.440+08:00 [DEBU] {b834e9dbcf014b1835a8c57f8e9dd5ad} [  1 ms] [default] [gfast] [rows:5  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` ORDER BY `config_id` asc LIMIT 0,10
2025-06-21T16:34:32.445+08:00 [DEBU] {b40b15ddcf014b1836a8c57fd212e1be} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('参数管理','/api/v1/system/config/list','GET',1,'demo','财务部门','/api/v1/system/config/list?pageNum=1&pageSize=10&configName=&configKey=&configType=','::1','内网IP','{"configKey":"","configName":"","configType":"","pageNum":"1","pageSize":"10"}','2025-06-21 16:34:32') 
2025-06-21T16:34:54.592+08:00 [DEBU] {60971e05d5014b1838a8c57f5341d0fe} [  3 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_user_sex') AND (`status`=1) LIMIT 1
2025-06-21T16:34:54.639+08:00 [DEBU] {60971e05d5014b1838a8c57f5341d0fe} [ 47 ms] [default] [gfast] [rows:3  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_user_sex') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T16:34:54.648+08:00 [DEBU] {88f04e08d5014b183aa8c57fbee27955} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 16:34:54') 
2025-06-21T16:34:54.654+08:00 [DEBU] {14be8608d5014b183da8c57fb34d6718} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 16:34:54') 
2025-06-21T16:34:54.697+08:00 [DEBU] {00c27808d5014b183ca8c57faaf12664} [ 40 ms] [default] [gfast] [rows:7  ] SELECT a.*, count(u.id) user_cnt FROM `sys_role` AS a LEFT JOIN `casbin_rule` AS `b` ON (b.v1  = a.id ) LEFT JOIN `sys_user` AS `u` ON (CONCAT('u_',u.id) = b.v0 ) WHERE `u`.`deleted_at` IS NULL GROUP BY `a`.`id` ORDER BY `list_order` asc,`id` asc
2025-06-21T16:34:54.703+08:00 [DEBU] {64a1aa0bd5014b183ea8c57ff69b83d1} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('角色管理','/api/v1/system/role/list','GET',1,'demo','财务部门','/api/v1/system/role/list?roleName=&roleStatus=&pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10","roleName":"","roleStatus":""}','2025-06-21 16:34:54') 
2025-06-21T16:35:05.653+08:00 [DEBU] {ecce3e97d7014b1841a8c57f744fdb94} [ 22 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dept/treeSelect','GET',1,'demo','财务部门','/api/v1/system/dept/treeSelect','::1','内网IP','{}','2025-06-21 16:35:05') 
2025-06-21T16:35:05.703+08:00 [DEBU] {c8e22c9bd7014b1845a8c57fa8f343ba} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 16:35:05') 
2025-06-21T16:35:05.714+08:00 [DEBU] {4c0f549bd7014b1846a8c57f833f17a5} [ 11 ms] [default] [gfast] [rows:11 ] SHOW FULL COLUMNS FROM `sys_post`
2025-06-21T16:35:05.719+08:00 [DEBU] {74256b9bd7014b1847a8c57f36dffe14} [  4 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user` LEFT JOIN `casbin_rule` AS `b` ON (b.v0 = CONCAT('u_',sys_user.id )) WHERE (v1 = 2 and SUBSTR(v0,1,2) = 'u_') AND `sys_user`.`deleted_at` IS NULL
2025-06-21T16:35:05.726+08:00 [DEBU] {4c0f549bd7014b1846a8c57f833f17a5} [ 11 ms] [default] [gfast] [rows:4  ] SELECT `post_id`,`post_code`,`post_name`,`post_sort`,`status`,`remark`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_post` WHERE (`status`=1) AND `deleted_at` IS NULL ORDER BY `post_sort` ASC,`post_id` ASC
2025-06-21T16:35:05.728+08:00 [DEBU] {74256b9bd7014b1847a8c57f36dffe14} [  4 ms] [default] [gfast] [rows:6  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`birthday`,`user_status`,`user_email`,`sex`,`avatar`,`dept_id`,`remark`,`is_admin`,`address`,`describe`,`last_login_ip`,`last_login_time`,`created_at`,`updated_at`,`deleted_at` FROM `sys_user` LEFT JOIN `casbin_rule` AS `b` ON (b.v0 = CONCAT('u_',sys_user.id )) WHERE (v1 = 2 and SUBSTR(v0,1,2) = 'u_') AND `sys_user`.`deleted_at` IS NULL ORDER BY `id` asc LIMIT 0,10
2025-06-21T16:35:05.732+08:00 [DEBU] {642a2e9dd7014b1848a8c57fd782012f} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/params','GET',1,'demo','财务部门','/api/v1/system/user/params','::1','内网IP','{}','2025-06-21 16:35:05') 
2025-06-21T16:35:05.737+08:00 [DEBU] {c40f669dd7014b1849a8c57f677b5ace} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('用户管理','/api/v1/system/user/list','GET',1,'demo','财务部门','/api/v1/system/user/list?pageNum=1&pageSize=10&roleId=2&deptId=&mobile=&status=&keyWords=','::1','内网IP','{"deptId":"","keyWords":"","mobile":"","pageNum":"1","pageSize":"10","roleId":"2","status":""}','2025-06-21 16:35:05') 
2025-06-21T16:35:15.836+08:00 [DEBU] {141140f7d9014b184ca8c57f16b87bf3} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('部门管理','/api/v1/system/dept/list','GET',1,'demo','财务部门','/api/v1/system/dept/list?pageNum=1&pageSize=10&deptName=&status=','::1','内网IP','{"deptName":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 16:35:15') 
2025-06-21T16:35:23.639+08:00 [DEBU] {582060c8db014b1850a8c57fc30d1cab} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 16:35:23') 
2025-06-21T16:35:23.646+08:00 [DEBU] {7cf0cac8db014b1852a8c57ffbcfbf30} [  2 ms] [default] [gfast] [rows:3  ] SELECT `id`,`avatar`,`sex`,`user_nickname` FROM `sys_user` WHERE (`id` IN(1,2,3)) AND `deleted_at` IS NULL ORDER BY `id` ASC
2025-06-21T16:35:23.646+08:00 [DEBU] {4469e9c8db014b1853a8c57f739570e3} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('部门管理','/api/v1/system/dept/list','GET',1,'demo','财务部门','/api/v1/system/dept/list','::1','内网IP','{}','2025-06-21 16:35:23') 
2025-06-21T16:35:23.652+08:00 [DEBU] {08f444c9db014b1854a8c57ff4d9527a} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/getUserByIds','GET',1,'demo','财务部门','/api/v1/system/user/getUserByIds?ids%5B%5D=1&ids%5B%5D=2&ids%5B%5D=3','::1','内网IP','{"ids":["1","2","3"]}','2025-06-21 16:35:23') 
2025-06-21T16:35:42.107+08:00 [DEBU] {70319b14e0014b1856a8c57ffa5e9801} [ 11 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dept` SET `parent_id`=0,`dept_name`='傲英云',`order_num`=0,`leader`='[1,2,3]',`phone`='15888888888',`email`='<EMAIL>',`status`=1,`updated_by`=31,`updated_at`='2025-06-21 16:35:42' WHERE (`dept_id`=100) AND `deleted_at` IS NULL
2025-06-21T16:35:42.110+08:00 [DEBU] {3c8a8915e0014b1857a8c57f6dd7a82d} [  1 ms] [default] [gfast] [rows:14 ] SELECT `dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_dept` WHERE `deleted_at` IS NULL
2025-06-21T16:35:42.117+08:00 [DEBU] {3c8a8915e0014b1857a8c57f6dd7a82d} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('修改部门','/api/v1/system/dept/edit','PUT',1,'demo','财务部门','/api/v1/system/dept/edit','::1','内网IP','{"ancestors":"0","children":[{"ancestors":"0,100","children":[{"ancestors":"0,100,101","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":103,"deptName":"研发部门","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":101,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"0,100,101","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":104,"deptName":"市场部门","email":"<EMAIL>","leader":null,"orderNum":2,"parentId":101,"phone":"15888888888","status":1,"updatedAt":"2021-11-04 09:16:38","updatedBy":31},{"ancestors":"0,100,101","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":105,"deptName":"测试部门","email":"<EMAIL>","leader":null,"orderNum":3,"parentId":101,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"0,100,101","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":106,"deptName":"财务部门","email":"<EMAIL>","leader":null,"orderNum":4,"parentId":101,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"0,100,101","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":107,"deptName":"运维部门","email":"<EMAIL>","leader":null,"orderNum":5,"parentId":101,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0}],"createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":101,"deptName":"深圳总公司","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":100,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"0,100","children":[{"ancestors":"0,100,102","children":[{"ancestors":"","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":202,"deptName":"外勤","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":108,"phone":"18888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":203,"deptName":"行政","email":"<EMAIL>","leader":null,"orderNum":0,"parentId":108,"phone":"18888888888","status":1,"updatedAt":"2022-09-16 16:46:47","updatedBy":31}],"createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":108,"deptName":"市场部门","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":102,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"0,100,102","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":109,"deptName":"财务部门","email":"<EMAIL>","leader":null,"orderNum":2,"parentId":102,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0}],"createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":102,"deptName":"长沙分公司","email":"<EMAIL>","leader":null,"orderNum":2,"parentId":100,"phone":"15888888888","status":1,"updatedAt":"2021-07-13 15:56:52","updatedBy":0},{"ancestors":"","createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":200,"deptName":"大数据","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":100,"phone":"18888888888","status":0,"updatedAt":"2022-09-16 16:46:57","updatedBy":31},{"ancestors":"","createdAt":"2021-07-13 15:56:52","createdBy":31,"deletedAt":null,"deptId":201,"deptName":"开发","email":"<EMAIL>","leader":null,"orderNum":1,"parentId":100,"phone":"18888888888","status":0,"updatedAt":"2022-04-07 22:35:21","updatedBy":0}],"createdAt":"2021-07-13 15:56:52","createdBy":0,"deletedAt":null,"deptId":100,"deptName":"傲英云","email":"<EMAIL>","leader":[1,2,3],"orderNum":0,"parentId":0,"phone":"15888888888","status":1,"updatedAt":"2024-01-29 16:00:28","updatedBy":31}','2025-06-21 16:35:42') 
2025-06-21T16:35:42.130+08:00 [DEBU] {98e9a416e0014b1859a8c57f5e17f36e} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('部门管理','/api/v1/system/dept/list','GET',1,'demo','财务部门','/api/v1/system/dept/list?pageNum=1&pageSize=10&deptName=&status=','::1','内网IP','{"deptName":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 16:35:42') 
2025-06-21T16:40:05.433+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:40:05.435+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:40:05.438+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:40:05.438+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T16:40:05.445+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:40:05','在线用户定时更新，执行成功') 
2025-06-21T16:40:22.152+08:00 [DEBU] {34deff4821024b185ba8c57ff26305f2} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:40:22') 
2025-06-21T16:41:09.004+08:00 [DEBU] {348eb1312c024b185ca8c57f5f5ae9d2} [  5 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='study_level' LIMIT 1
2025-06-21T16:41:09.007+08:00 [DEBU] {348eb1312c024b185ca8c57f5f5ae9d2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'学习阶段','study_level',1,31,'','2025-06-21 16:41:09','2025-06-21 16:41:09') 
2025-06-21T16:41:09.013+08:00 [DEBU] {e09548322c024b185da8c57f527e0019} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"学习阶段","dictType":"study_level","pid":54,"remark":"","status":1}','2025-06-21 16:41:09') 
2025-06-21T16:41:09.028+08:00 [DEBU] {d06654332c024b185ea8c57f5b7ebe8b} [  1 ms] [default] [gfast] [rows:37 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:41:09.035+08:00 [DEBU] {e0159a332c024b185fa8c57f47fef97b} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:41:09') 
2025-06-21T16:41:10.524+08:00 [DEBU] {6044838c2c024b1861a8c57fb606e5a2} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('study_level')
2025-06-21T16:41:10.524+08:00 [DEBU] {6044838c2c024b1861a8c57fb606e5a2} [  0 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('study_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:41:10.529+08:00 [DEBU] {a8fbba8c2c024b1862a8c57f1ae85fb9} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=65','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"65"}','2025-06-21 16:41:10') 
2025-06-21T16:41:12.126+08:00 [DEBU] {90f8eeeb2c024b1864a8c57f55fcf287} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:41:12') 
2025-06-21T16:41:30.414+08:00 [DEBU] {9880d72d31024b1865a8c57f2cd73ca9} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'一阶段','1','study_level','','',0,1,31,'','2025-06-21 16:41:30','2025-06-21 16:41:30') 
2025-06-21T16:41:30.418+08:00 [DEBU] {a0d7362e31024b1866a8c57f153df7bb} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"一阶段","dictSort":0,"dictType":"study_level","dictValue":"1","isDefault":0,"remark":"","status":1}','2025-06-21 16:41:30') 
2025-06-21T16:41:30.434+08:00 [DEBU] {b4b02f2f31024b1867a8c57f978db8c8} [  2 ms] [default] [gfast] [rows:37 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:41:30.435+08:00 [DEBU] {b4b02f2f31024b1867a8c57f978db8c8} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('study_level')
2025-06-21T16:41:30.436+08:00 [DEBU] {b4b02f2f31024b1867a8c57f978db8c8} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('study_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:41:30.441+08:00 [DEBU] {b061912f31024b1868a8c57f6e55d44d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=65','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"65"}','2025-06-21 16:41:30') 
2025-06-21T16:41:31.936+08:00 [DEBU] {d8fe958831024b186aa8c57f1e1680e5} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:41:31') 
2025-06-21T16:41:41.625+08:00 [DEBU] {f80706ca33024b186ba8c57f59fcebf8} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'二阶段','2','study_level','','',0,1,31,'','2025-06-21 16:41:41','2025-06-21 16:41:41') 
2025-06-21T16:41:41.631+08:00 [DEBU] {e4977fca33024b186ca8c57f997201c5} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"二阶段","dictSort":0,"dictType":"study_level","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 16:41:41') 
2025-06-21T16:41:41.646+08:00 [DEBU] {d05188cb33024b186da8c57f1ae43d40} [  1 ms] [default] [gfast] [rows:37 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:41:41.648+08:00 [DEBU] {d05188cb33024b186da8c57f1ae43d40} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('study_level')
2025-06-21T16:41:41.650+08:00 [DEBU] {d05188cb33024b186da8c57f1ae43d40} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('study_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:41:41.655+08:00 [DEBU] {78c8f5cb33024b186ea8c57f02e3888d} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=65','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"65"}','2025-06-21 16:41:41') 
2025-06-21T16:41:42.842+08:00 [DEBU] {24bc5a1234024b1870a8c57fd6cc36bc} [  8 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:41:42') 
2025-06-21T16:41:54.227+08:00 [DEBU] {288057b936024b1871a8c57f69b00770} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'三阶段','3','study_level','','',0,1,31,'','2025-06-21 16:41:54','2025-06-21 16:41:54') 
2025-06-21T16:41:54.231+08:00 [DEBU] {38099bb936024b1872a8c57f7809d81e} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"三阶段","dictSort":0,"dictType":"study_level","dictValue":"3","isDefault":0,"remark":"","status":1}','2025-06-21 16:41:54') 
2025-06-21T16:41:54.248+08:00 [DEBU] {40639cba36024b1873a8c57f5c6b1c86} [  1 ms] [default] [gfast] [rows:37 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:41:54.250+08:00 [DEBU] {40639cba36024b1873a8c57f5c6b1c86} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('study_level')
2025-06-21T16:41:54.252+08:00 [DEBU] {40639cba36024b1873a8c57f5c6b1c86} [  2 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('study_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:41:54.256+08:00 [DEBU] {58d516bb36024b1874a8c57fc3566afc} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=65','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"65"}','2025-06-21 16:41:54') 
2025-06-21T16:41:56.409+08:00 [DEBU] {3453233b37024b1876a8c57f28770f22} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:41:56') 
2025-06-21T16:41:56.430+08:00 [DEBU] {d49cba3c37024b1878a8c57f38d6bc5a} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_code`=152 LIMIT 1
2025-06-21T16:41:56.435+08:00 [DEBU] {04d9e93c37024b1879a8c57f238bfbe0} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/get','GET',1,'demo','财务部门','/api/v1/system/dict/data/get?dictCode=152','::1','内网IP','{"dictCode":"152"}','2025-06-21 16:41:56') 
2025-06-21T16:41:59.142+08:00 [DEBU] {f09148de37024b187ba8c57ffa0acf1f} [  3 ms] [default] [gfast] [rows:1  ] UPDATE `sys_dict_data` SET `dict_sort`=0,`dict_label`='一阶段',`dict_value`='1',`dict_type`='study_level',`css_class`='',`list_class`='',`is_default`=1,`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 16:41:59' WHERE `dict_code`=152
2025-06-21T16:41:59.146+08:00 [DEBU] {808f83de37024b187ca8c57ff7e3083e} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/data/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 16:41:30","cssClass":"","dictCode":152,"dictLabel":"一阶段","dictSort":0,"dictType":"study_level","dictValue":"1","isDefault":1,"listClass":"","remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 16:41:30"}','2025-06-21 16:41:59') 
2025-06-21T16:41:59.160+08:00 [DEBU] {901d71df37024b187da8c57fd46c1bb1} [  1 ms] [default] [gfast] [rows:37 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:41:59.162+08:00 [DEBU] {901d71df37024b187da8c57fd46c1bb1} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('study_level')
2025-06-21T16:41:59.164+08:00 [DEBU] {901d71df37024b187da8c57fd46c1bb1} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('study_level') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:41:59.169+08:00 [DEBU] {4c86dddf37024b187ea8c57f469f2216} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=65','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"65"}','2025-06-21 16:41:59') 
2025-06-21T16:45:16.837+08:00 [DEBU] {a44fb0e565024b1880a8c57fa863247d} [  5 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('menu_module_type')
2025-06-21T16:45:16.840+08:00 [DEBU] {a44fb0e565024b1880a8c57fa863247d} [  2 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('menu_module_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:16.846+08:00 [DEBU] {c4a449e665024b1881a8c57fc74aeb94} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=30','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"30"}','2025-06-21 16:45:16') 
2025-06-21T16:45:21.505+08:00 [DEBU] {545f2efc66024b1883a8c57fed0bd17e} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type')
2025-06-21T16:45:21.507+08:00 [DEBU] {545f2efc66024b1883a8c57fed0bd17e} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:21.512+08:00 [DEBU] {089e72fc66024b1884a8c57f0e8baf90} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=33','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"33"}','2025-06-21 16:45:21') 
2025-06-21T16:45:32.317+08:00 [DEBU] {f034a48069024b1885a8c57f4360a385} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type')
2025-06-21T16:45:32.319+08:00 [DEBU] {f034a48069024b1885a8c57f4360a385} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:32.323+08:00 [DEBU] {90bee38069024b1886a8c57f36aa590d} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=33','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"33"}','2025-06-21 16:45:32') 
2025-06-21T16:45:35.465+08:00 [DEBU] {90d5423c6a024b1888a8c57ff17fbe6c} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign')
2025-06-21T16:45:35.466+08:00 [DEBU] {90d5423c6a024b1888a8c57ff17fbe6c} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:35.471+08:00 [DEBU] {a0eb7b3c6a024b1889a8c57f074068e0} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=34','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"34"}','2025-06-21 16:45:35') 
2025-06-21T16:45:46.415+08:00 [DEBU] {14f8e4c86c024b188aa8c57f69214987} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type')
2025-06-21T16:45:46.417+08:00 [DEBU] {14f8e4c86c024b188aa8c57f69214987} [  2 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:46.421+08:00 [DEBU] {549534c96c024b188ba8c57f75df5c85} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=33','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"33"}','2025-06-21 16:45:46') 
2025-06-21T16:45:49.315+08:00 [DEBU] {64917b756d024b188ca8c57f9471a8ed} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign')
2025-06-21T16:45:49.317+08:00 [DEBU] {64917b756d024b188ca8c57f9471a8ed} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:45:49.323+08:00 [DEBU] {7cd622766d024b188da8c57f0b61ecc4} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=34','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"34"}','2025-06-21 16:45:49') 
2025-06-21T16:46:19.419+08:00 [DEBU] {8c86517674024b188fa8c57f7a6e08f3} [ 31 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:46:19') 
2025-06-21T16:47:10.044+08:00 [DEBU] {dc51ce4080024b1890a8c57f7980cd61} [  9 ms] [default] [gfast] [rows:0  ] SELECT `dict_id` FROM `sys_dict_type` WHERE `dict_type`='group_status' LIMIT 1
2025-06-21T16:47:10.049+08:00 [DEBU] {dc51ce4080024b1890a8c57f7980cd61} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_type`(`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(54,'群组标志','group_status',1,31,'','2025-06-21 16:47:10','2025-06-21 16:47:10') 
2025-06-21T16:47:10.053+08:00 [DEBU] {50e4164280024b1891a8c57fb67775ff} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/add','POST',1,'demo','财务部门','/api/v1/system/dict/type/add','::1','内网IP','{"dictId":0,"dictName":"群组标志","dictType":"group_status","pid":54,"remark":"","status":1}','2025-06-21 16:47:10') 
2025-06-21T16:47:10.074+08:00 [DEBU] {0c68534380024b1892a8c57fbd734b23} [  2 ms] [default] [gfast] [rows:38 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:47:10.080+08:00 [DEBU] {109aa94380024b1893a8c57f8ca0a74f} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:47:10') 
2025-06-21T16:47:11.384+08:00 [DEBU] {60957e9180024b1895a8c57fec695635} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_status')
2025-06-21T16:47:11.385+08:00 [DEBU] {60957e9180024b1895a8c57fec695635} [  1 ms] [default] [gfast] [rows:0  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:47:11.394+08:00 [DEBU] {6093c19180024b1896a8c57f705483b7} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=66','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"66"}','2025-06-21 16:47:11') 
2025-06-21T16:47:12.874+08:00 [DEBU] {342139ea80024b1898a8c57fb17c80f0} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:47:12') 
2025-06-21T16:47:28.169+08:00 [DEBU] {1032d27984024b1899a8c57f62039d52} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'一般','1','group_status','','',1,1,31,'','2025-06-21 16:47:28','2025-06-21 16:47:28') 
2025-06-21T16:47:28.173+08:00 [DEBU] {844f0e7a84024b189aa8c57fe7511b2e} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"一般","dictSort":0,"dictType":"group_status","dictValue":"1","isDefault":1,"remark":"","status":1}','2025-06-21 16:47:28') 
2025-06-21T16:47:28.188+08:00 [DEBU] {1418057b84024b189ba8c57f5cae2ba9} [  1 ms] [default] [gfast] [rows:38 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:47:28.190+08:00 [DEBU] {1418057b84024b189ba8c57f5cae2ba9} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_status')
2025-06-21T16:47:28.191+08:00 [DEBU] {1418057b84024b189ba8c57f5cae2ba9} [  0 ms] [default] [gfast] [rows:1  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:47:28.195+08:00 [DEBU] {20ae717b84024b189ca8c57f4652dcc7} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=66','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"66"}','2025-06-21 16:47:28') 
2025-06-21T16:47:32.374+08:00 [DEBU] {14ce8b7485024b189ea8c57f84b80e82} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 16:47:32') 
2025-06-21T16:47:44.131+08:00 [DEBU] {dc840c3188024b189fa8c57fe9a61010} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_dict_data`(`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`remark`,`created_at`,`updated_at`) VALUES(0,'置顶','2','group_status','','',0,1,31,'','2025-06-21 16:47:44','2025-06-21 16:47:44') 
2025-06-21T16:47:44.137+08:00 [DEBU] {40269d3188024b18a0a8c57fa8689dfe} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/add','POST',1,'demo','财务部门','/api/v1/system/dict/data/add','::1','内网IP','{"dictCode":0,"dictLabel":"置顶","dictSort":0,"dictType":"group_status","dictValue":"2","isDefault":0,"remark":"","status":1}','2025-06-21 16:47:44') 
2025-06-21T16:47:44.160+08:00 [DEBU] {f063fe3288024b18a1a8c57f0e66d8c2} [  2 ms] [default] [gfast] [rows:38 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T16:47:44.161+08:00 [DEBU] {f063fe3288024b18a1a8c57f0e66d8c2} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('group_status')
2025-06-21T16:47:44.163+08:00 [DEBU] {f063fe3288024b18a1a8c57f0e66d8c2} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('group_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T16:47:44.168+08:00 [DEBU] {2ced6f3388024b18a2a8c57f177885ea} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=66','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"66"}','2025-06-21 16:47:44') 
2025-06-21T16:50:05.431+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  5 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:50:05.432+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T16:50:05.433+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T16:50:05.433+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T16:50:05.438+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 16:50:05','在线用户定时更新，执行成功') 
2025-06-21T17:00:05.336+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:00:05.340+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:00:05.342+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:00:05.343+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:00:05.345+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:00:05','在线用户定时更新，执行成功') 
2025-06-21T17:10:05.935+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 10 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:10:05.936+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:10:05.938+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:10:05.938+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:10:05.942+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:10:05','在线用户定时更新，执行成功') 
2025-06-21T17:20:05.933+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  8 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:20:05.934+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:20:05.936+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:20:05.937+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:20:05.940+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:20:05','在线用户定时更新，执行成功') 
2025-06-21T17:30:05.030+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  5 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:30:05.032+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:30:05.033+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:30:05.034+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:30:05.037+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:30:05','在线用户定时更新，执行成功') 
2025-06-21T17:40:05.839+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 13 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:40:05.840+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:40:05.841+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:40:05.842+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:40:05.853+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 10 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:40:05','在线用户定时更新，执行成功') 
2025-06-21T17:50:05.832+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:50:05.833+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T17:50:05.835+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T17:50:05.836+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T17:50:05.838+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 17:50:05','在线用户定时更新，执行成功') 
2025-06-21T19:20:05.843+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 18 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:20:05.848+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T19:20:05.849+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:20:05.850+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T19:20:05.863+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 19:20:05','在线用户定时更新，执行成功') 
2025-06-21T19:27:27.345+08:00 [DEBU] {f8a2d8743f0b4b18a8a8c57f039f03c7} [ 10 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data`
2025-06-21T19:27:27.350+08:00 [DEBU] {f8a2d8743f0b4b18a8a8c57f039f03c7} [  4 ms] [default] [gfast] [rows:10 ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:27.358+08:00 [DEBU] {14fa0a753f0b4b18a9a8c57fb43c4ac6} [ 20 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:27:27') 
2025-06-21T19:27:27.363+08:00 [DEBU] {7c3313763f0b4b18aaa8c57f529c3e25} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 19:27:27') 
2025-06-21T19:27:40.338+08:00 [DEBU] {dc5ac97b420b4b18aca8c57f613caa6a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type')
2025-06-21T19:27:40.340+08:00 [DEBU] {dc5ac97b420b4b18aca8c57f613caa6a} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:40.347+08:00 [DEBU] {e854367c420b4b18ada8c57f4ed4215f} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=33','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"33"}','2025-06-21 19:27:40') 
2025-06-21T19:27:45.015+08:00 [DEBU] {487da092430b4b18afa8c57f04d121d0} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign')
2025-06-21T19:27:45.016+08:00 [DEBU] {487da092430b4b18afa8c57f04d121d0} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:45.050+08:00 [DEBU] {40f5bb93430b4b18b0a8c57fac87c73d} [ 17 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=34','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"34"}','2025-06-21 19:27:45') 
2025-06-21T19:27:48.190+08:00 [DEBU] {ec7cdf4f440b4b18b2a8c57f2d192982} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type')
2025-06-21T19:27:48.191+08:00 [DEBU] {ec7cdf4f440b4b18b2a8c57f2d192982} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:48.215+08:00 [DEBU] {8c941850440b4b18b3a8c57f434890ce} [ 21 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=50','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"50"}','2025-06-21 19:27:48') 
2025-06-21T19:27:53.855+08:00 [DEBU] {94e98ba1450b4b18b5a8c57fe500fe35} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('notice_tag')
2025-06-21T19:27:53.857+08:00 [DEBU] {94e98ba1450b4b18b5a8c57fe500fe35} [  1 ms] [default] [gfast] [rows:6  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('notice_tag') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:53.863+08:00 [DEBU] {b4ded0a1450b4b18b6a8c57fc87888ac} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=51','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"51"}','2025-06-21 19:27:53') 
2025-06-21T19:27:55.933+08:00 [DEBU] {2891601d460b4b18b7a8c57f31e30046} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type')
2025-06-21T19:27:55.935+08:00 [DEBU] {2891601d460b4b18b7a8c57f31e30046} [  2 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_blog_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:55.940+08:00 [DEBU] {30d0aa1d460b4b18b8a8c57f8fb33d0d} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=33','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"33"}','2025-06-21 19:27:55') 
2025-06-21T19:27:57.792+08:00 [DEBU] {305cf68b460b4b18b9a8c57fec9ca07f} [  4 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign')
2025-06-21T19:27:57.794+08:00 [DEBU] {305cf68b460b4b18b9a8c57fec9ca07f} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_log_sign') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:57.800+08:00 [DEBU] {04488b8c460b4b18baa8c57f3e277cdd} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=34','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"34"}','2025-06-21 19:27:57') 
2025-06-21T19:27:58.600+08:00 [DEBU] {902f24bc460b4b18bba8c57f6b17ff54} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type')
2025-06-21T19:27:58.604+08:00 [DEBU] {902f24bc460b4b18bba8c57f6b17ff54} [  3 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:27:58.609+08:00 [DEBU] {b079c3bc460b4b18bca8c57f1d6738f7} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=50','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"50"}','2025-06-21 19:27:58') 
2025-06-21T19:28:00.303+08:00 [DEBU] {1063d321470b4b18bea8c57ffccd5b08} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_drive')
2025-06-21T19:28:00.304+08:00 [DEBU] {1063d321470b4b18bea8c57ffccd5b08} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_drive') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:00.310+08:00 [DEBU] {90652822470b4b18bfa8c57fbda16251} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=52','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"52"}','2025-06-21 19:28:00') 
2025-06-21T19:28:14.004+08:00 [DEBU] {7cba81524a0b4b18c0a8c57fb85cfac4} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_drive')
2025-06-21T19:28:14.007+08:00 [DEBU] {7cba81524a0b4b18c0a8c57fb85cfac4} [  2 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_upload_drive') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:14.013+08:00 [DEBU] {ac91de524a0b4b18c1a8c57f73bcc305} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=52','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"52"}','2025-06-21 19:28:14') 
2025-06-21T19:28:20.248+08:00 [DEBU] {88fca9c64b0b4b18c3a8c57f1402d11a} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T19:28:20.250+08:00 [DEBU] {88fca9c64b0b4b18c3a8c57f1402d11a} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:20.255+08:00 [DEBU] {a011f5c64b0b4b18c4a8c57f8309cea2} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 19:28:20') 
2025-06-21T19:28:25.029+08:00 [DEBU] {ac0695e34c0b4b18c5a8c57f0a8e7752} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type')
2025-06-21T19:28:25.031+08:00 [DEBU] {ac0695e34c0b4b18c5a8c57f0a8e7752} [  1 ms] [default] [gfast] [rows:4  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_oper_log_type') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:25.036+08:00 [DEBU] {2865f1e34c0b4b18c6a8c57f0c960e7b} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=50','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"50"}','2025-06-21 19:28:25') 
2025-06-21T19:28:35.158+08:00 [DEBU] {9800ca3e4f0b4b18c7a8c57f294a263c} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T19:28:35.163+08:00 [DEBU] {9800ca3e4f0b4b18c7a8c57f294a263c} [  5 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:35.168+08:00 [DEBU] {c042dc3f4f0b4b18c8a8c57f8cf46168} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 19:28:35') 
2025-06-21T19:28:37.824+08:00 [DEBU] {f07944de4f0b4b18caa8c57f3ee87984} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T19:28:37.824+08:00 [DEBU] {f07944de4f0b4b18caa8c57f3ee87984} [  0 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:37.831+08:00 [DEBU] {24f47cde4f0b4b18cba8c57f494551df} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 19:28:37') 
2025-06-21T19:28:41.735+08:00 [DEBU] {fcc363c7500b4b18cda8c57f2365dab1} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_group')
2025-06-21T19:28:41.735+08:00 [DEBU] {fcc363c7500b4b18cda8c57f2365dab1} [  0 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_job_group') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:41.740+08:00 [DEBU] {60bf9ac7500b4b18cea8c57fd5b32217} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=13','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"13"}','2025-06-21 19:28:41') 
2025-06-21T19:28:53.047+08:00 [DEBU] {302ba169530b4b18d0a8c57ff68d689c} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status')
2025-06-21T19:28:53.048+08:00 [DEBU] {302ba169530b4b18d0a8c57ff68d689c} [  1 ms] [default] [gfast] [rows:2  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('sys_work_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:28:53.053+08:00 [DEBU] {88ede569530b4b18d1a8c57f2d3d87f7} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=3','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"3"}','2025-06-21 19:28:53') 
2025-06-21T19:29:03.980+08:00 [DEBU] {607d22f5550b4b18d3a8c57fbc104e85} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:03') 
2025-06-21T19:29:03.987+08:00 [DEBU] {c48899f5550b4b18d5a8c57fc364fe01} [  3 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T19:29:03.994+08:00 [DEBU] {bcbdf3f5550b4b18d6a8c57fa4bb247c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=3','::1','内网IP','{"dictId":"3"}','2025-06-21 19:29:03') 
2025-06-21T19:29:14.784+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [  7 ms] [default] [gfast] [rows:0  ] [txid:4] BEGIN (IosolationLevel: Default, ReadOnly: false)
2025-06-21T19:29:14.785+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [  1 ms] [default] [gfast] [rows:0  ] [txid:4] SELECT `dict_id` FROM `sys_dict_type` WHERE (`dict_type`='sys_job_status') AND (dict_id !=3) LIMIT 1
2025-06-21T19:29:14.787+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [  1 ms] [default] [gfast] [rows:1  ] [txid:4] SELECT `dict_type` FROM `sys_dict_type` WHERE `dict_id`=3 LIMIT 1
2025-06-21T19:29:14.798+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [ 11 ms] [default] [gfast] [rows:1  ] [txid:4] UPDATE `sys_dict_type` SET `pid`=54,`dict_name`='工作状态',`dict_type`='sys_job_status',`status`=1,`update_by`=31,`remark`='任务状态列表',`updated_at`='2025-06-21 19:29:14' WHERE `dict_id`=3
2025-06-21T19:29:14.803+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [  5 ms] [default] [gfast] [rows:2  ] [txid:4] UPDATE `sys_dict_data` SET `dict_type`='sys_job_status',`updated_at`='2025-06-21 19:29:14' WHERE `dict_type`='sys_work_status'
2025-06-21T19:29:14.807+08:00 [DEBU] {705dc078580b4b18d8a8c57f8b9c1170} [  3 ms] [default] [gfast] [rows:0  ] [txid:4] COMMIT
2025-06-21T19:29:14.812+08:00 [DEBU] {18b3cf7a580b4b18d9a8c57fe9b5b0dc} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/type/edit','::1','内网IP','{"createBy":31,"createdAt":null,"dictId":3,"dictName":"工作状态","dictType":"sys_job_status","pid":54,"remark":"任务状态列表","status":1,"updateBy":31,"updatedAt":"2025-06-21 16:34:00"}','2025-06-21 19:29:14') 
2025-06-21T19:29:14.832+08:00 [DEBU] {40ee107c580b4b18daa8c57fdb419ff8} [  2 ms] [default] [gfast] [rows:38 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T19:29:14.838+08:00 [DEBU] {282f567c580b4b18dba8c57f097a630c} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:14') 
2025-06-21T19:29:17.636+08:00 [DEBU] {080d3c23590b4b18dca8c57f40893411} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T19:29:17.637+08:00 [DEBU] {080d3c23590b4b18dca8c57f40893411} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:29:17.640+08:00 [DEBU] {e0eb7523590b4b18dda8c57fe5f30ecf} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 19:29:17') 
2025-06-21T19:29:20.570+08:00 [DEBU] {b4a4f3d1590b4b18e0a8c57f44bd7a0e} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:20') 
2025-06-21T19:29:20.586+08:00 [DEBU] {78390dd3590b4b18e1a8c57f9a2d5acb} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=58 LIMIT 1
2025-06-21T19:29:20.592+08:00 [DEBU] {dcff50d3590b4b18e2a8c57fae9454d2} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=58','::1','内网IP','{"dictId":"58"}','2025-06-21 19:29:20') 
2025-06-21T19:29:25.484+08:00 [DEBU] {4c6803f75a0b4b18e3a8c57fe22d2f54} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('moment_category')
2025-06-21T19:29:25.485+08:00 [DEBU] {4c6803f75a0b4b18e3a8c57fe22d2f54} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('moment_category') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:29:25.489+08:00 [DEBU] {bca73ff75a0b4b18e4a8c57ff7f37bc0} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=57','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"57"}','2025-06-21 19:29:25') 
2025-06-21T19:29:27.387+08:00 [DEBU] {94047b685b0b4b18e5a8c57f3e125d49} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_status')
2025-06-21T19:29:27.388+08:00 [DEBU] {94047b685b0b4b18e5a8c57f3e125d49} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_status') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:29:27.392+08:00 [DEBU] {4808b4685b0b4b18e6a8c57f54dab1f1} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=58','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"58"}','2025-06-21 19:29:27') 
2025-06-21T19:29:28.728+08:00 [DEBU] {f0fb54b85b0b4b18e8a8c57f64f3b977} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:28') 
2025-06-21T19:29:28.730+08:00 [DEBU] {74b076b85b0b4b18e9a8c57f1520ea77} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=58 LIMIT 1
2025-06-21T19:29:28.735+08:00 [DEBU] {7029c0b85b0b4b18eaa8c57f0545fe60} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=58','::1','内网IP','{"dictId":"58"}','2025-06-21 19:29:28') 
2025-06-21T19:29:31.242+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  0 ms] [default] [gfast] [rows:0  ] [txid:5] BEGIN (IosolationLevel: Default, ReadOnly: false)
2025-06-21T19:29:31.244+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  0 ms] [default] [gfast] [rows:0  ] [txid:5] SELECT `dict_id` FROM `sys_dict_type` WHERE (`dict_type`='task_status') AND (dict_id !=58) LIMIT 1
2025-06-21T19:29:31.245+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  0 ms] [default] [gfast] [rows:1  ] [txid:5] SELECT `dict_type` FROM `sys_dict_type` WHERE `dict_id`=58 LIMIT 1
2025-06-21T19:29:31.248+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  2 ms] [default] [gfast] [rows:1  ] [txid:5] UPDATE `sys_dict_type` SET `pid`=54,`dict_name`='任务状态',`dict_type`='task_status',`status`=1,`update_by`=31,`remark`='',`updated_at`='2025-06-21 19:29:31' WHERE `dict_id`=58
2025-06-21T19:29:31.250+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  1 ms] [default] [gfast] [rows:3  ] [txid:5] UPDATE `sys_dict_data` SET `dict_type`='task_status',`updated_at`='2025-06-21 19:29:31' WHERE `dict_type`='task_status'
2025-06-21T19:29:31.251+08:00 [DEBU] {a037504e5c0b4b18eba8c57f5983d114} [  1 ms] [default] [gfast] [rows:0  ] [txid:5] COMMIT
2025-06-21T19:29:31.257+08:00 [DEBU] {c8d8034f5c0b4b18eca8c57f453004de} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/edit','PUT',1,'demo','财务部门','/api/v1/system/dict/type/edit','::1','内网IP','{"createBy":31,"createdAt":"2025-06-21 11:12:14","dictId":58,"dictName":"任务状态","dictType":"task_status","pid":54,"remark":"","status":1,"updateBy":0,"updatedAt":"2025-06-21 11:12:14"}','2025-06-21 19:29:31') 
2025-06-21T19:29:31.272+08:00 [DEBU] {208ef54f5c0b4b18eda8c57f08ab6cbf} [  1 ms] [default] [gfast] [rows:38 ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` ORDER BY `dict_id` ASC
2025-06-21T19:29:31.278+08:00 [DEBU] {a0074c505c0b4b18eea8c57f46d9deea} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:31') 
2025-06-21T19:29:37.222+08:00 [DEBU] {7052aab25d0b4b18f0a8c57f94171ec6} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_dict_data` WHERE `dict_type` IN('task_priority')
2025-06-21T19:29:37.223+08:00 [DEBU] {7052aab25d0b4b18f0a8c57f94171ec6} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_code`,`dict_sort`,`dict_label`,`dict_value`,`dict_type`,`css_class`,`list_class`,`is_default`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_data` WHERE `dict_type` IN('task_priority') ORDER BY `dict_sort` asc,`dict_code` asc LIMIT 0,10
2025-06-21T19:29:37.228+08:00 [DEBU] {0468ecb25d0b4b18f1a8c57fba13ee44} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/list','GET',1,'demo','财务部门','/api/v1/system/dict/data/list?pageNum=1&pageSize=10&dictLabel=&status=&typeId=59','::1','内网IP','{"dictLabel":"","pageNum":"1","pageSize":"10","status":"","typeId":"59"}','2025-06-21 19:29:37') 
2025-06-21T19:29:39.594+08:00 [DEBU] {74d7d93f5e0b4b18f4a8c57f26524528} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/optionSelect','GET',1,'demo','财务部门','/api/v1/system/dict/type/optionSelect?all=true','::1','内网IP','{"all":"true"}','2025-06-21 19:29:39') 
2025-06-21T19:29:39.599+08:00 [DEBU] {902754405e0b4b18f5a8c57f70d81f30} [  1 ms] [default] [gfast] [rows:1  ] SELECT `dict_id`,`pid`,`dict_name`,`dict_type`,`status`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_dict_type` WHERE `dict_id`=59 LIMIT 1
2025-06-21T19:29:39.609+08:00 [DEBU] {ec4bb5405e0b4b18f6a8c57f1acd6199} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/type/get','GET',1,'demo','财务部门','/api/v1/system/dict/type/get?dictId=59','::1','内网IP','{"dictId":"59"}','2025-06-21 19:29:39') 
2025-06-21T19:29:47.135+08:00 [DEBU] {20c11701600b4b18f8a8c57fe2b0f3ca} [  9 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) LIMIT 1
2025-06-21T19:29:47.139+08:00 [DEBU] {20c11701600b4b18f8a8c57fe2b0f3ca} [  4 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_yes_no') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T19:29:47.146+08:00 [DEBU] {10c1fe01600b4b18faa8c57ffd218695} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_yes_no&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_yes_no"}','2025-06-21 19:29:47') 
2025-06-21T19:29:47.167+08:00 [DEBU] {28f61a02600b4b18fba8c57fb2200307} [ 23 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_config`
2025-06-21T19:29:47.168+08:00 [DEBU] {28f61a02600b4b18fba8c57fb2200307} [  1 ms] [default] [gfast] [rows:5  ] SELECT `config_id`,`config_name`,`config_key`,`config_value`,`config_type`,`create_by`,`update_by`,`remark`,`created_at`,`updated_at` FROM `sys_config` ORDER BY `config_id` asc LIMIT 0,10
2025-06-21T19:29:47.175+08:00 [DEBU] {7877b603600b4b18fca8c57f6f8e4d94} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('参数管理','/api/v1/system/config/list','GET',1,'demo','财务部门','/api/v1/system/config/list?pageNum=1&pageSize=10&configName=&configKey=&configType=','::1','内网IP','{"configKey":"","configName":"","configType":"","pageNum":"1","pageSize":"10"}','2025-06-21 19:29:47') 
2025-06-21T19:30:05.828+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:30:05.830+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T19:30:05.832+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:30:05.832+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T19:30:05.838+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 19:30:05','在线用户定时更新，执行成功') 
2025-06-21T19:30:06.250+08:00 [DEBU] {7496ad74640b4b18ffa8c57f15d88234} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('部门管理','/api/v1/system/dept/list','GET',1,'demo','财务部门','/api/v1/system/dept/list?pageNum=1&pageSize=10&deptName=&status=','::1','内网IP','{"deptName":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 19:30:06') 
2025-06-21T19:30:08.580+08:00 [DEBU] {4cc719ff640b4b1801a9c57f5dbadc1c} [  9 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_post` WHERE `deleted_at` IS NULL
2025-06-21T19:30:08.581+08:00 [DEBU] {4cc719ff640b4b1801a9c57f5dbadc1c} [  1 ms] [default] [gfast] [rows:5  ] SELECT `post_id`,`post_code`,`post_name`,`post_sort`,`status`,`remark`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_post` WHERE `deleted_at` IS NULL ORDER BY `post_sort` asc,`post_id` asc LIMIT 0,10
2025-06-21T19:30:08.590+08:00 [DEBU] {b4f14900650b4b1802a9c57ffb3ab7bb} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('岗位管理','/api/v1/system/post/list','GET',1,'demo','财务部门','/api/v1/system/post/list?postName=&status=&postCode=&pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10","postCode":"","postName":"","status":""}','2025-06-21 19:30:08') 
2025-06-21T19:30:12.432+08:00 [DEBU] {e4392de5650b4b1804a9c57ff4f3e2ce} [  3 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_user_sex') AND (`status`=1) LIMIT 1
2025-06-21T19:30:12.433+08:00 [DEBU] {e4392de5650b4b1804a9c57ff4f3e2ce} [  1 ms] [default] [gfast] [rows:3  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_user_sex') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T19:30:12.439+08:00 [DEBU] {cc3ca6e5650b4b1805a9c57fbe475e18} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 19:30:12') 
2025-06-21T19:30:12.455+08:00 [DEBU] {60c87be6650b4b1808a9c57fb187d468} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 19:30:12') 
2025-06-21T19:30:12.459+08:00 [DEBU] {bc87a6e6650b4b180ba9c57f2b2877fa} [  1 ms] [default] [gfast] [rows:4  ] SELECT `post_id`,`post_code`,`post_name`,`post_sort`,`status`,`remark`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_post` WHERE (`status`=1) AND `deleted_at` IS NULL ORDER BY `post_sort` ASC,`post_id` ASC
2025-06-21T19:30:12.466+08:00 [DEBU] {ccb305e7650b4b180da9c57f9dce9638} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dept/treeSelect','GET',1,'demo','财务部门','/api/v1/system/dept/treeSelect?showOwner=true','::1','内网IP','{"showOwner":"true"}','2025-06-21 19:30:12') 
2025-06-21T19:30:12.478+08:00 [DEBU] {34cc3ee7650b4b180fa9c57f13339cd3} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/params','GET',1,'demo','财务部门','/api/v1/system/user/params','::1','内网IP','{}','2025-06-21 19:30:12') 
2025-06-21T19:30:12.480+08:00 [DEBU] {ccb305e7650b4b180ea9c57ffb5d6ae6} [ 21 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user` WHERE `deleted_at` IS NULL
2025-06-21T19:30:12.482+08:00 [DEBU] {ccb305e7650b4b180ea9c57ffb5d6ae6} [  1 ms] [default] [gfast] [rows:10 ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`birthday`,`user_status`,`user_email`,`sex`,`avatar`,`dept_id`,`remark`,`is_admin`,`address`,`describe`,`last_login_ip`,`last_login_time`,`created_at`,`updated_at`,`deleted_at`,`open_id` FROM `sys_user` WHERE `deleted_at` IS NULL ORDER BY `id` asc LIMIT 0,10
2025-06-21T19:30:12.491+08:00 [DEBU] {acdfbbe8650b4b1810a9c57fccd401b8} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('用户管理','/api/v1/system/user/list','GET',1,'demo','财务部门','/api/v1/system/user/list?pageNum=1&pageSize=10&deptId=&mobile=&status=&keyWords=','::1','内网IP','{"deptId":"","keyWords":"","mobile":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 19:30:12') 
2025-06-21T19:40:05.639+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 14 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:40:05.642+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T19:40:05.643+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:40:05.646+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T19:40:05.650+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 19:40:05','在线用户定时更新，执行成功') 
2025-06-21T19:50:05.742+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 16 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:50:05.744+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T19:50:05.744+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T19:50:05.745+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T19:50:05.751+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 19:50:05','在线用户定时更新，执行成功') 
2025-06-21T20:00:05.747+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 21 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:00:05.750+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T20:00:05.751+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:00:05.754+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T20:00:05.759+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 20:00:05','在线用户定时更新，执行成功') 
2025-06-21T20:10:05.245+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 18 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:10:05.251+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  4 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T20:10:05.252+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:10:05.252+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T20:10:05.260+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 20:10:05','在线用户定时更新，执行成功') 
2025-06-21T20:20:05.840+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [ 14 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:20:05.845+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T20:20:05.851+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T20:20:05.852+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T20:20:05.858+08:00 [DEBU] {6cf21af847ed4a18a3a5c57f7d76cb78} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 20:20:05','在线用户定时更新，执行成功') 
2025-06-21T20:24:55.848+08:00 [DEBU] {7477565d620e4b1814a9c57fcded446a} [ 42 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user` WHERE `deleted_at` IS NULL
2025-06-21T20:24:55.855+08:00 [DEBU] {7477565d620e4b1814a9c57fcded446a} [  6 ms] [default] [gfast] [rows:10 ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`birthday`,`user_status`,`user_email`,`sex`,`avatar`,`dept_id`,`remark`,`is_admin`,`address`,`describe`,`last_login_ip`,`last_login_time`,`created_at`,`updated_at`,`deleted_at`,`open_id` FROM `sys_user` WHERE `deleted_at` IS NULL ORDER BY `id` asc LIMIT 0,10
2025-06-21T20:24:55.873+08:00 [DEBU] {308a325d620e4b1813a9c57f32fa0aa8} [ 64 ms] [default] [gfast] [rows:4  ] SELECT `post_id`,`post_code`,`post_name`,`post_sort`,`status`,`remark`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_post` WHERE (`status`=1) AND `deleted_at` IS NULL ORDER BY `post_sort` ASC,`post_id` ASC
2025-06-21T20:24:55.889+08:00 [DEBU] {708c805e620e4b1818a9c57f65508052} [ 68 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 20:24:55') 
2025-06-21T20:24:55.893+08:00 [DEBU] {149a6a5e620e4b1817a9c57ff590aa42} [ 71 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dept/treeSelect','GET',1,'demo','财务部门','/api/v1/system/dept/treeSelect?showOwner=true','::1','内网IP','{"showOwner":"true"}','2025-06-21 20:24:55') 
2025-06-21T20:24:55.893+08:00 [DEBU] {04fef461620e4b181ba9c57f9d307f8d} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/user/params','GET',1,'demo','财务部门','/api/v1/system/user/params','::1','内网IP','{}','2025-06-21 20:24:55') 
2025-06-21T20:24:55.894+08:00 [DEBU] {b8df0f61620e4b181aa9c57f7b4a2c95} [ 30 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('用户管理','/api/v1/system/user/list','GET',1,'demo','财务部门','/api/v1/system/user/list?pageNum=1&pageSize=10&deptId=&mobile=&status=&keyWords=','::1','内网IP','{"deptId":"","keyWords":"","mobile":"","pageNum":"1","pageSize":"10","status":""}','2025-06-21 20:24:55') 
2025-06-21T20:24:55.895+08:00 [DEBU] {b4fcbc5e620e4b1819a9c57f17366a98} [ 69 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_user_sex&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_user_sex"}','2025-06-21 20:24:55') 
2025-06-21T21:14:32.248+08:00 [DEBU] {6052165b17114b18ab04b6644991d45a} [ 73 ms] [default] [gfast] [rows:33 ] SHOW TABLES
2025-06-21T21:14:32.286+08:00 [DEBU] {6052165b17114b18ab04b6644991d45a} [ 34 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-21T21:14:32.291+08:00 [DEBU] {6052165b17114b18ab04b6644991d45a} [  3 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-21T21:14:32.312+08:00 [DEBU] {6052165b17114b18ab04b6644991d45a} [ 18 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-21T21:14:32.328+08:00 [DEBU] {c438786317114b18ad04b66491b80765} [ 14 ms] [default] [gfast] [rows:5  ] SHOW FULL COLUMNS FROM `plugin_sms_config`
2025-06-21T21:14:32.331+08:00 [DEBU] {c438786317114b18ad04b66491b80765} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-06-21T21:14:32.334+08:00 [DEBU] {8467976417114b18ae04b66429a5b440} [  1 ms] [default] [gfast] [rows:1  ] SELECT * FROM `plugin_sms_config` WHERE `sms_type`='yunxin' LIMIT 1
2025-06-21T21:20:05.423+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [ 39 ms] [default] [gfast] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-06-21T21:20:05.428+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  2 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:20:05.431+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T21:20:05.439+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  6 ms] [default] [gfast] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKK1Nx9f0AIM1G/EzS9Ln2hXSQ0Q6aWvrq3a6EXsgD6ZtUadl49HstuXeL+MauC4Cz2GV3sLcdd/n3IAUJWV51oQ=='
2025-06-21T21:20:05.441+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:20:05.446+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  3 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T21:20:05.451+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  3 ms] [default] [gfast] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-06-21T21:20:05.460+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 21:20:05','在线用户定时更新，执行成功') 
2025-06-21T21:21:46.531+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 33 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-21T21:21:46.544+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 10 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T21:21:46.555+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [  7 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 21:21:46' WHERE `id`=31
2025-06-21T21:21:46.588+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 25 ms] [default] [gfast] [rows:11 ] SHOW FULL COLUMNS FROM `sys_role`
2025-06-21T21:21:46.597+08:00 [DEBU] {e8d8567e7c114b18cb04b66463c830ae} [  7 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-21T21:21:46.610+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 10 ms] [default] [gfast] [rows:7  ] SELECT `id`,`pid`,`status`,`list_order`,`name`,`remark`,`data_scope`,`created_at`,`updated_at`,`created_by`,`effectiveTime` FROM `sys_role` ORDER BY `list_order` asc,`id` asc
2025-06-21T21:21:46.621+08:00 [DEBU] {e8d8567e7c114b18cb04b66463c830ae} [ 21 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 21:21:46','系统后台') 
2025-06-21T21:21:46.640+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 20 ms] [default] [gfast] [rows:7  ] SHOW FULL COLUMNS FROM `casbin_rule`
2025-06-21T21:21:46.643+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [  2 ms] [default] [gfast] [rows:98 ] SELECT `ptype`,`v0`,`v1`,`v2`,`v3`,`v4`,`v5` FROM `casbin_rule`
2025-06-21T21:21:46.666+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [ 18 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_auth_rule`
2025-06-21T21:21:46.675+08:00 [DEBU] {340db27a7c114b18ca04b66483fa2fbc} [  6 ms] [default] [gfast] [rows:68 ] SELECT `id`,`pid`,`name`,`title`,`icon`,`condition`,`remark`,`menu_type`,`weigh`,`is_hide`,`is_cached`,`is_affix`,`path`,`redirect`,`component`,`is_iframe`,`is_link`,`link_url` FROM `sys_auth_rule` ORDER BY `weigh` desc,`id` asc
2025-06-21T21:21:46.685+08:00 [DEBU] {f8cdc2857c114b18cc04b664e88955ae} [  2 ms] [default] [gfast] [rows:0  ] SELECT `id` FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKCwNbiMxycAg8LmL0CTE9rc66TKEZhRTQq3632w4EpPwTeQ5g9dZVXXj+9WFgmzyPOjpxk52MxqYDWyvHc7yRiw==' LIMIT 1
2025-06-21T21:21:46.691+08:00 [DEBU] {f8cdc2857c114b18cc04b664e88955ae} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_user_online`(`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os`) VALUES('2792da95d371e4d5298b59d8638a1a6e','7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKCwNbiMxycAg8LmL0CTE9rc66TKEZhRTQq3632w4EpPwTeQ5g9dZVXXj+9WFgmzyPOjpxk52MxqYDWyvHc7yRiw==','2025-06-21 21:21:46','demo','::1','Chrome','Windows 10') 
2025-06-21T21:22:04.188+08:00 [DEBU] {68b6499880114b18ce04b66490bcd391} [ 10 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_dict_type`
2025-06-21T21:22:04.200+08:00 [DEBU] {68b6499880114b18ce04b66490bcd391} [ 10 ms] [default] [gfast] [rows:1  ] SELECT `dict_name`,`remark` FROM `sys_dict_type` WHERE (`dict_type`='sys_normal_disable') AND (`status`=1) LIMIT 1
2025-06-21T21:22:04.208+08:00 [DEBU] {68b6499880114b18ce04b66490bcd391} [  6 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dict_data`
2025-06-21T21:22:04.215+08:00 [DEBU] {68b6499880114b18ce04b66490bcd391} [  5 ms] [default] [gfast] [rows:2  ] SELECT `dict_value`,`dict_label`,`is_default`,`remark` FROM `sys_dict_data` WHERE (`dict_type`='sys_normal_disable') AND (`status`=1) ORDER BY `dict_sort` asc,`dict_code` asc
2025-06-21T21:22:04.217+08:00 [DEBU] {34a9169980114b18d004b664ad3c81fe} [ 27 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_config`
2025-06-21T21:22:04.220+08:00 [DEBU] {34a9169980114b18d004b664ad3c81fe} [  1 ms] [default] [gfast] [rows:4  ] SELECT `id`,`sms_type`,`remark`,`status` FROM `plugin_sms_config` ORDER BY `id` asc LIMIT 0,10
2025-06-21T21:22:04.233+08:00 [DEBU] {dcd0359b80114b18d104b664edab3026} [ 12 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_dept`
2025-06-21T21:22:04.238+08:00 [DEBU] {dcd0359b80114b18d104b664edab3026} [  3 ms] [default] [gfast] [rows:14 ] SELECT `dept_id`,`parent_id`,`ancestors`,`dept_name`,`order_num`,`leader`,`phone`,`email`,`status`,`created_by`,`updated_by`,`created_at`,`updated_at`,`deleted_at` FROM `sys_dept` WHERE `deleted_at` IS NULL
2025-06-21T21:22:04.246+08:00 [DEBU] {dcd0359b80114b18d104b664edab3026} [  5 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_oper_log`
2025-06-21T21:22:04.262+08:00 [DEBU] {dcd0359b80114b18d104b664edab3026} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_normal_disable&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_normal_disable"}','2025-06-21 21:22:04') 
2025-06-21T21:22:04.265+08:00 [DEBU] {7cea709c80114b18d204b664e5b1b872} [ 14 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信配置列表','/api/v1/plugins/sms/pluginSmsConfig/list','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 21:22:04') 
2025-06-21T21:22:09.561+08:00 [DEBU] {2c99acd881114b18d404b6649aa2f1e4} [ 14 ms] [default] [gfast] [rows:1  ] UPDATE `plugin_sms_config` SET `status`=0 WHERE `id`=1
2025-06-21T21:22:09.563+08:00 [DEBU] {2c99acd881114b18d404b6649aa2f1e4} [  0 ms] [default] [gfast] [rows:0  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `status`=1 LIMIT 1
2025-06-21T21:22:09.576+08:00 [DEBU] {d86910da81114b18d504b664530c9d19} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/plugins/sms/pluginSmsConfig/changeStatus','PUT',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/changeStatus','::1','内网IP','{"id":1,"status":0}','2025-06-21 21:22:09') 
2025-06-21T21:22:16.187+08:00 [DEBU] {a4bc676283114b18d804b66447fb9304} [ 32 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `plugin_sms_log`
2025-06-21T21:22:16.192+08:00 [DEBU] {a4bc676283114b18d804b66447fb9304} [  3 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_log` WHERE `deleted_at` IS NULL
2025-06-21T21:22:16.194+08:00 [DEBU] {a4bc676283114b18d804b66447fb9304} [  1 ms] [default] [gfast] [rows:10 ] SELECT `id`,`sms_type`,`msg_type`,`templateid`,`mobiles`,`params`,`created_at` FROM `plugin_sms_log` WHERE `deleted_at` IS NULL ORDER BY `id` desc LIMIT 0,10
2025-06-21T21:22:16.236+08:00 [DEBU] {44ed2a6583114b18d904b66455bb14de} [ 37 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信日志列表','/api/v1/plugins/sms/pluginSmsLog/list','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsLog/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 21:22:16') 
2025-06-21T21:22:19.067+08:00 [DEBU] {4cea6b0f84114b18db04b66413430350} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_normal_disable&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_normal_disable"}','2025-06-21 21:22:19') 
2025-06-21T21:22:19.075+08:00 [DEBU] {e432ef0f84114b18dc04b664f05925f7} [ 11 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_config`
2025-06-21T21:22:19.082+08:00 [DEBU] {e432ef0f84114b18dc04b664f05925f7} [  3 ms] [default] [gfast] [rows:4  ] SELECT `id`,`sms_type`,`remark`,`status` FROM `plugin_sms_config` ORDER BY `id` asc LIMIT 0,10
2025-06-21T21:22:19.092+08:00 [DEBU] {e801431184114b18dd04b6649ee16e78} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信配置列表','/api/v1/plugins/sms/pluginSmsConfig/list','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 21:22:19') 
2025-06-21T21:22:52.508+08:00 [DEBU] {309e8fd88b114b18de04b6642a8993f6} [ 13 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_log` WHERE `deleted_at` IS NULL
2025-06-21T21:22:52.510+08:00 [DEBU] {309e8fd88b114b18de04b6642a8993f6} [  1 ms] [default] [gfast] [rows:10 ] SELECT `id`,`sms_type`,`msg_type`,`templateid`,`mobiles`,`params`,`created_at` FROM `plugin_sms_log` WHERE `deleted_at` IS NULL ORDER BY `id` desc LIMIT 0,10
2025-06-21T21:22:52.518+08:00 [DEBU] {bcfcadd98b114b18df04b664cf67330b} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信日志列表','/api/v1/plugins/sms/pluginSmsLog/list','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsLog/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 21:22:52') 
2025-06-21T21:22:56.165+08:00 [DEBU] {44d621b38c114b18e104b6648353dcff} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('','/api/v1/system/dict/data/getDictData','GET',1,'demo','财务部门','/api/v1/system/dict/data/getDictData?dictType=sys_normal_disable&defaultValue=','::1','内网IP','{"defaultValue":"","dictType":"sys_normal_disable"}','2025-06-21 21:22:56') 
2025-06-21T21:22:56.178+08:00 [DEBU] {10f5a4b38c114b18e204b664139f2705} [  7 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `plugin_sms_config`
2025-06-21T21:22:56.181+08:00 [DEBU] {10f5a4b38c114b18e204b664139f2705} [  1 ms] [default] [gfast] [rows:4  ] SELECT `id`,`sms_type`,`remark`,`status` FROM `plugin_sms_config` ORDER BY `id` asc LIMIT 0,10
2025-06-21T21:22:56.190+08:00 [DEBU] {00e38eb48c114b18e304b664139e5faa} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信配置列表','/api/v1/plugins/sms/pluginSmsConfig/list','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/list?pageNum=1&pageSize=10','::1','内网IP','{"pageNum":"1","pageSize":"10"}','2025-06-21 21:22:56') 
2025-06-21T21:22:58.165+08:00 [DEBU] {c810732a8d114b18e504b664ad682803} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `id`=1 LIMIT 1
2025-06-21T21:22:58.175+08:00 [DEBU] {38fae62a8d114b18e604b664c0a71100} [  3 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信配置查询','/api/v1/plugins/sms/pluginSmsConfig/get','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/get?id=1','::1','内网IP','{"id":"1"}','2025-06-21 21:22:58') 
2025-06-21T21:23:06.646+08:00 [DEBU] {e4dcfd238f114b18e804b664582e832b} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`sms_type`,`remark`,`status`,`config` FROM `plugin_sms_config` WHERE `id`=6 LIMIT 1
2025-06-21T21:23:06.654+08:00 [DEBU] {042036248f114b18e904b66492399b2f} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_oper_log`(`title`,`method`,`request_method`,`operator_type`,`oper_name`,`dept_name`,`oper_url`,`oper_ip`,`oper_location`,`oper_param`,`oper_time`) VALUES('短信配置查询','/api/v1/plugins/sms/pluginSmsConfig/get','GET',1,'demo','财务部门','/api/v1/plugins/sms/pluginSmsConfig/get?id=6','::1','内网IP','{"id":"6"}','2025-06-21 21:23:06') 
2025-06-21T21:30:05.398+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [ 18 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:30:05.404+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T21:30:05.406+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:30:05.409+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T21:30:05.418+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  7 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 21:30:05','在线用户定时更新，执行成功') 
2025-06-21T21:40:05.281+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [ 90 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:40:05.289+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  3 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T21:40:05.292+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:40:05.296+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T21:40:05.304+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 21:40:05','在线用户定时更新，执行成功') 
2025-06-21T21:50:05.207+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [ 30 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:50:05.217+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  5 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T21:50:05.220+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T21:50:05.222+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-21T21:50:05.233+08:00 [DEBU] {f0cb3c6217114b18ac04b664adc2737e} [  9 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 21:50:05','在线用户定时更新，执行成功') 
