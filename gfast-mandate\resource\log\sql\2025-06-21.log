2025-06-21T10:04:29.991+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [220 ms] [default] [gfast] [rows:29 ] SHOW TABLES
2025-06-21T10:04:30.079+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 88 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-21T10:04:30.101+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 22 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-21T10:04:30.124+08:00 [DEBU] {dcb5220187ec4a1809c6d86bddca701a} [ 22 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-21T10:06:01.255+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 60 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-21T10:06:01.277+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 15 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:06:01.295+08:00 [DEBU] {fc14d7499cec4a180fc6d86bc6f1bc9f} [ 15 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 10:06:01' WHERE `id`=31
2025-06-21T10:06:01.348+08:00 [DEBU] {78396c509cec4a1810c6d86b85dfb2e7} [ 43 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-21T10:06:01.360+08:00 [DEBU] {78396c509cec4a1810c6d86b85dfb2e7} [ 12 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 10:06:01','系统后台') 
2025-06-21T10:07:37.058+08:00 [DEBU] {9c0bc26bb2ec4a1817c6d86b6a766b5f} [692 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-21T10:07:37.108+08:00 [DEBU] {9c0bc26bb2ec4a1817c6d86b6a766b5f} [ 44 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-21 10:07:37' WHERE `id`=31
2025-06-21T10:07:37.192+08:00 [DEBU] {a4f36b9fb2ec4a1818c6d86b59838160} [ 49 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-21 10:07:37','系统后台') 
2025-06-21T10:10:05.331+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [ 27 ms] [default] [gfast] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-06-21T10:10:05.332+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-21T10:10:05.343+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  1 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-21T10:10:05.353+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  8 ms] [default] [gfast] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-06-21T10:10:05.359+08:00 [DEBU] {f8b7e61487ec4a180ac6d86ba2e38eec} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-21 10:10:05','在线用户定时更新，执行成功') 
