2025-06-16T19:52:58.815+08:00 [INFO] {20037c31bd8349184fe1c94107df3983} C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:25: 
   ____________           __ 
  / ____/ ____/___ ______/ /_
 / / __/ /_  / __ `/ ___/ __/
/ /_/ / __/ / /_/ (__  ) /_  
\____/_/    \__,_/____/\__/   Version: 3.3.5
2025-06-16T19:54:22.863+08:00 [ERRO] {b04d3633bd83491850e1c941bc05cd6f} C:/Git/COACH/gfast-mandate/task/bind_function.go:46: SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at`,`CreatedUser`,`UpdatedUser` FROM `sys_job` WHERE `status`=0: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/task.Run
    C:/Git/COACH/gfast-mandate/task/bind_function.go:46
2.  github.com/tiger1103/gfast/v3/task.init.0.func1
    C:/Git/COACH/gfast-mandate/task/bind_function.go:22
3.  github.com/tiger1103/gfast/v3/internal/mounter.DoMount
    C:/Git/COACH/gfast-mandate/internal/mounter/mount.go:27
4.  github.com/tiger1103/gfast/v3/internal/cmd.init.func1
    C:/Git/COACH/gfast-mandate/internal/cmd/cmd.go:28
5.  main.main
    C:/Git/COACH/gfast-mandate/main.go:16

2025-06-16T19:54:22.864+08:00 [DEBU] C:/Git/COACH/gfast-mandate/library/libWebsocket/init.go:20: start websocket..
