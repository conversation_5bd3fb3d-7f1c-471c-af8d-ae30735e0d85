// ================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth/response"
	phoneNumberResp "github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/phoneNumber/response"
)

type (
	IWechat interface {
		Session(ctx context.Context, code string) (data *response.ResponseCode2Session, err error)
		GetUserPhoneNumber(ctx context.Context, code string) (data *phoneNumberResp.ResponseGetUserPhoneNumber, err error)
		IsDebug(ctx context.Context)bool
		GetWxUrl(ctx context.Context) (url string,err error)
	}
)

var (
	localWechat IWechat
)

func Wechat() IWechat {
	if localWechat == nil {
		panic("implement not found for interface IWechat, forgot register?")
	}
	return localWechat
}

func RegisterWechat(i IWechat) {
	localWechat = i
}
