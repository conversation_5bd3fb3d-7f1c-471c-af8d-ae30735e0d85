0 verbose cli C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@11.3.0
2 info using node@v22.14.0
3 silly config load:file:C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\npmrc
4 silly config load:file:C:\Git\coach\go-admin-ui\.npmrc
5 warn Unknown project config "npm_config_sass_binary_site". This will stop working in the next major version of npm.
6 warn Unknown project config "npm_config_phantomjs_cdnurl". This will stop working in the next major version of npm.
7 warn Unknown project config "npm_config_electron_mirror". This will stop working in the next major version of npm.
8 silly config load:file:C:\Users\<USER>\.npmrc
9 silly config load:file:C:\nvm4w\nodejs\etc\npmrc
10 verbose title npm install
11 verbose argv "install"
12 verbose logfile logs-max:10 dir:C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-04-13T15_22_11_659Z-
13 verbose logfile C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-04-13T15_22_11_659Z-debug-0.log
14 silly logfile start cleaning logs, removing 1 files
15 silly packumentCache heap:2197815296 maxSize:549453824 maxEntrySize:274726912
16 silly logfile done cleaning log files
17 silly idealTree buildDeps
18 silly fetch manifest @riophae/vue-treeselect@0.4.0
19 silly packumentCache full:https://registry.npmmirror.com/@riophae%2fvue-treeselect cache-miss
20 http fetch GET 200 https://registry.npmmirror.com/@riophae%2fvue-treeselect 1090ms (cache miss)
21 silly packumentCache full:https://registry.npmmirror.com/@riophae%2fvue-treeselect set size:22925 disposed:false
22 silly fetch manifest vue@2.6.11
23 silly packumentCache full:https://registry.npmmirror.com/vue cache-miss
24 http fetch GET 200 https://registry.npmmirror.com/vue 588ms (cache revalidated)
25 silly packumentCache full:https://registry.npmmirror.com/vue set size:1971192 disposed:false
26 silly fetch manifest awe-dnd@^0.3.4
27 silly packumentCache full:https://registry.npmmirror.com/awe-dnd cache-miss
28 http fetch GET 200 https://registry.npmmirror.com/awe-dnd 621ms (cache miss)
29 silly packumentCache full:https://registry.npmmirror.com/awe-dnd set size:4754 disposed:false
30 silly fetch manifest axios@0.21.1
31 silly packumentCache full:https://registry.npmmirror.com/axios cache-miss
32 http fetch GET 200 https://registry.npmmirror.com/axios 431ms (cache miss)
33 silly packumentCache full:https://registry.npmmirror.com/axios set size:104357 disposed:false
34 silly fetch manifest clipboard@2.0.6
35 silly packumentCache full:https://registry.npmmirror.com/clipboard cache-miss
36 http fetch GET 200 https://registry.npmmirror.com/clipboard 1003ms (cache miss)
37 silly packumentCache full:https://registry.npmmirror.com/clipboard set size:21840 disposed:false
38 silly fetch manifest codemirror@5.62.0
39 silly packumentCache full:https://registry.npmmirror.com/codemirror cache-miss
40 http fetch GET 200 https://registry.npmmirror.com/codemirror 728ms (cache miss)
41 silly packumentCache full:https://registry.npmmirror.com/codemirror set size:336080 disposed:false
42 silly fetch manifest core-js@^3.6.5
43 silly packumentCache full:https://registry.npmmirror.com/core-js cache-miss
44 http fetch GET 200 https://registry.npmmirror.com/core-js 593ms (cache miss)
45 silly packumentCache full:https://registry.npmmirror.com/core-js set size:107719 disposed:false
46 silly fetch manifest driver.js@0.9.8
47 silly packumentCache full:https://registry.npmmirror.com/driver.js cache-miss
48 http fetch GET 200 https://registry.npmmirror.com/driver.js 561ms (cache miss)
49 silly packumentCache full:https://registry.npmmirror.com/driver.js set size:25412 disposed:false
50 silly fetch manifest dropzone@5.7.2
51 silly packumentCache full:https://registry.npmmirror.com/dropzone cache-miss
52 http fetch GET 200 https://registry.npmmirror.com/dropzone 562ms (cache miss)
53 silly packumentCache full:https://registry.npmmirror.com/dropzone set size:18677 disposed:false
54 silly fetch manifest echarts@4.8.0
55 silly packumentCache full:https://registry.npmmirror.com/echarts cache-miss
56 http fetch GET 200 https://registry.npmmirror.com/echarts 539ms (cache miss)
57 silly packumentCache full:https://registry.npmmirror.com/echarts set size:55667 disposed:false
58 silly fetch manifest element-ui@2.15.6
59 silly packumentCache full:https://registry.npmmirror.com/element-ui cache-miss
60 http fetch GET 200 https://registry.npmmirror.com/element-ui 642ms (cache miss)
61 silly packumentCache full:https://registry.npmmirror.com/element-ui set size:88559 disposed:false
62 silly fetch manifest file-saver@2.0.2
63 silly packumentCache full:https://registry.npmmirror.com/file-saver cache-miss
64 http fetch GET 200 https://registry.npmmirror.com/file-saver 530ms (cache miss)
65 silly packumentCache full:https://registry.npmmirror.com/file-saver set size:8890 disposed:false
66 silly fetch manifest fuse.js@6.4.1
67 silly packumentCache full:https://registry.npmmirror.com/fuse.js cache-miss
68 http fetch GET 200 https://registry.npmmirror.com/fuse.js 551ms (cache miss)
69 silly packumentCache full:https://registry.npmmirror.com/fuse.js set size:45677 disposed:false
70 silly fetch manifest js-cookie@2.2.1
71 silly packumentCache full:https://registry.npmmirror.com/js-cookie cache-miss
72 http fetch GET 200 https://registry.npmmirror.com/js-cookie 548ms (cache miss)
73 silly packumentCache full:https://registry.npmmirror.com/js-cookie set size:19462 disposed:false
74 silly fetch manifest jsonlint@1.6.3
75 silly packumentCache full:https://registry.npmmirror.com/jsonlint cache-miss
76 http fetch GET 200 https://registry.npmmirror.com/jsonlint 559ms (cache miss)
77 silly packumentCache full:https://registry.npmmirror.com/jsonlint set size:5625 disposed:false
78 silly fetch manifest jszip@3.5.0
79 silly packumentCache full:https://registry.npmmirror.com/jszip cache-miss
80 http fetch GET 200 https://registry.npmmirror.com/jszip 553ms (cache miss)
81 silly packumentCache full:https://registry.npmmirror.com/jszip set size:17871 disposed:false
82 silly fetch manifest moment@^2.27.0
83 silly packumentCache full:https://registry.npmmirror.com/moment cache-miss
84 http fetch GET 200 https://registry.npmmirror.com/moment 571ms (cache miss)
85 silly packumentCache full:https://registry.npmmirror.com/moment set size:27529 disposed:false
86 silly fetch manifest normalize.css@8.0.1
87 silly packumentCache full:https://registry.npmmirror.com/normalize.css cache-miss
88 http fetch GET 200 https://registry.npmmirror.com/normalize.css 548ms (cache miss)
89 silly packumentCache full:https://registry.npmmirror.com/normalize.css set size:6212 disposed:false
90 silly fetch manifest nprogress@0.2.0
91 silly packumentCache full:https://registry.npmmirror.com/nprogress cache-miss
92 http fetch GET 200 https://registry.npmmirror.com/nprogress 542ms (cache miss)
93 silly packumentCache full:https://registry.npmmirror.com/nprogress set size:5684 disposed:false
94 silly fetch manifest path-to-regexp@6.1.0
95 silly packumentCache full:https://registry.npmmirror.com/path-to-regexp cache-miss
96 http fetch GET 200 https://registry.npmmirror.com/path-to-regexp 547ms (cache revalidated)
97 silly packumentCache full:https://registry.npmmirror.com/path-to-regexp set size:136494 disposed:false
98 silly fetch manifest remixicon@^2.5.0
99 silly packumentCache full:https://registry.npmmirror.com/remixicon cache-miss
100 http fetch GET 200 https://registry.npmmirror.com/remixicon 586ms (cache miss)
101 silly packumentCache full:https://registry.npmmirror.com/remixicon set size:17764 disposed:false
102 silly fetch manifest sass-resources-loader@^2.0.3
103 silly packumentCache full:https://registry.npmmirror.com/sass-resources-loader cache-miss
104 http fetch GET 200 https://registry.npmmirror.com/sass-resources-loader 604ms (cache miss)
105 silly packumentCache full:https://registry.npmmirror.com/sass-resources-loader set size:15277 disposed:false
106 silly fetch manifest screenfull@5.0.2
107 silly packumentCache full:https://registry.npmmirror.com/screenfull cache-miss
108 http fetch GET 200 https://registry.npmmirror.com/screenfull 545ms (cache miss)
109 silly packumentCache full:https://registry.npmmirror.com/screenfull set size:13704 disposed:false
110 silly fetch manifest viser-vue@^2.4.8
111 silly packumentCache full:https://registry.npmmirror.com/viser-vue cache-miss
112 http fetch GET 200 https://registry.npmmirror.com/viser-vue 562ms (cache miss)
113 silly packumentCache full:https://registry.npmmirror.com/viser-vue set size:15062 disposed:false
114 silly fetch manifest vue-codemirror@^4.0.6
115 silly packumentCache full:https://registry.npmmirror.com/vue-codemirror cache-miss
116 http fetch GET 200 https://registry.npmmirror.com/vue-codemirror 593ms (cache miss)
117 silly packumentCache full:https://registry.npmmirror.com/vue-codemirror set size:27205 disposed:false
118 silly fetch manifest vue-count-to@1.0.13
119 silly packumentCache full:https://registry.npmmirror.com/vue-count-to cache-miss
120 http fetch GET 200 https://registry.npmmirror.com/vue-count-to 572ms (cache miss)
121 silly packumentCache full:https://registry.npmmirror.com/vue-count-to set size:4935 disposed:false
122 silly fetch manifest vue-cropper@^0.5.5
123 silly packumentCache full:https://registry.npmmirror.com/vue-cropper cache-miss
124 http fetch GET 200 https://registry.npmmirror.com/vue-cropper 540ms (cache miss)
125 silly packumentCache full:https://registry.npmmirror.com/vue-cropper set size:35201 disposed:false
126 silly fetch manifest vue-particles@^1.0.9
127 silly packumentCache full:https://registry.npmmirror.com/vue-particles cache-miss
128 http fetch GET 200 https://registry.npmmirror.com/vue-particles 1204ms (cache miss)
129 silly packumentCache full:https://registry.npmmirror.com/vue-particles set size:3519 disposed:false
130 silly fetch manifest vue-router@3.4.7
131 silly packumentCache full:https://registry.npmmirror.com/vue-router cache-miss
132 http fetch GET 200 https://registry.npmmirror.com/vue-router 573ms (cache miss)
133 silly packumentCache full:https://registry.npmmirror.com/vue-router set size:115694 disposed:false
134 silly fetch manifest vuedraggable@2.24.0
135 silly packumentCache full:https://registry.npmmirror.com/vuedraggable cache-miss
136 http fetch GET 200 https://registry.npmmirror.com/vuedraggable 688ms (cache miss)
137 silly packumentCache full:https://registry.npmmirror.com/vuedraggable set size:23413 disposed:false
138 silly fetch manifest vuex@3.5.1
139 silly packumentCache full:https://registry.npmmirror.com/vuex cache-miss
140 http fetch GET 200 https://registry.npmmirror.com/vuex 595ms (cache miss)
141 silly packumentCache full:https://registry.npmmirror.com/vuex set size:29526 disposed:false
142 silly fetch manifest webpack-bundle-analyzer@^3.8.0
143 silly packumentCache full:https://registry.npmmirror.com/webpack-bundle-analyzer cache-miss
144 http fetch GET 200 https://registry.npmmirror.com/webpack-bundle-analyzer 760ms (cache miss)
145 silly packumentCache full:https://registry.npmmirror.com/webpack-bundle-analyzer set size:41615 disposed:false
146 silly fetch manifest xlsx@0.16.5
147 silly packumentCache full:https://registry.npmmirror.com/xlsx cache-miss
148 http fetch GET 200 https://registry.npmmirror.com/xlsx 540ms (cache miss)
149 silly packumentCache full:https://registry.npmmirror.com/xlsx set size:59561 disposed:false
150 silly fetch manifest @babel/core@7.11.1
151 silly packumentCache full:https://registry.npmmirror.com/@babel%2fcore cache-miss
152 http fetch GET 200 https://registry.npmmirror.com/@babel%2fcore 319ms (cache revalidated)
153 silly packumentCache full:https://registry.npmmirror.com/@babel%2fcore set size:624119 disposed:false
154 silly fetch manifest @babel/register@^7.10.5
155 silly packumentCache full:https://registry.npmmirror.com/@babel%2fregister cache-miss
156 http fetch GET 200 https://registry.npmmirror.com/@babel%2fregister 685ms (cache revalidated)
157 silly packumentCache full:https://registry.npmmirror.com/@babel%2fregister set size:208383 disposed:false
158 silly fetch manifest @babel/runtime@^7.12.1
159 silly packumentCache full:https://registry.npmmirror.com/@babel%2fruntime cache-miss
160 http fetch GET 200 https://registry.npmmirror.com/@babel%2fruntime 1119ms (cache revalidated)
161 silly packumentCache full:https://registry.npmmirror.com/@babel%2fruntime set size:2868040 disposed:false
162 silly fetch manifest @vue/babel-preset-app@^4.5.7
163 silly packumentCache full:https://registry.npmmirror.com/@vue%2fbabel-preset-app cache-miss
164 http fetch GET 200 https://registry.npmmirror.com/@vue%2fbabel-preset-app 326ms (cache miss)
165 silly packumentCache full:https://registry.npmmirror.com/@vue%2fbabel-preset-app set size:55745 disposed:false
166 silly fetch manifest @vue/cli-plugin-babel@4.4.6
167 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-plugin-babel cache-miss
168 http fetch GET 200 https://registry.npmmirror.com/@vue%2fcli-plugin-babel 327ms (cache miss)
169 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-plugin-babel set size:52558 disposed:false
170 silly fetch manifest @vue/cli-service@^4.5.13
171 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-service cache-miss
172 http fetch GET 200 https://registry.npmmirror.com/@vue%2fcli-service 341ms (cache miss)
173 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-service set size:70508 disposed:false
174 silly fetch manifest @vue/compiler-sfc@^3.0.0-beta.14
175 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcompiler-sfc cache-miss
176 http fetch GET 200 https://registry.npmmirror.com/@vue%2fcompiler-sfc 535ms (cache revalidated)
177 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcompiler-sfc set size:786552 disposed:false
178 silly fetch manifest vue-template-compiler@2.6.11
179 silly packumentCache full:https://registry.npmmirror.com/vue-template-compiler cache-miss
180 http fetch GET 200 https://registry.npmmirror.com/vue-template-compiler 580ms (cache miss)
181 silly packumentCache full:https://registry.npmmirror.com/vue-template-compiler set size:60977 disposed:false
182 silly fetch manifest @vue/cli-plugin-eslint@^4.4.6
183 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-plugin-eslint cache-miss
184 http fetch GET 200 https://registry.npmmirror.com/@vue%2fcli-plugin-eslint 314ms (cache miss)
185 silly packumentCache full:https://registry.npmmirror.com/@vue%2fcli-plugin-eslint set size:53134 disposed:false
186 silly fetch manifest eslint@7.6.0
187 silly packumentCache full:https://registry.npmmirror.com/eslint cache-miss
188 http fetch GET 200 https://registry.npmmirror.com/eslint 359ms (cache miss)
189 silly packumentCache full:https://registry.npmmirror.com/eslint set size:197072 disposed:false
190 silly fetch manifest eslint@>= 1.6.0 < 7.0.0
191 silly packumentCache full:https://registry.npmmirror.com/eslint cache-hit
192 verbose stack Error: unable to resolve dependency tree
192 verbose stack     at #failPeerConflict (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:1374:25)
192 verbose stack     at #loadPeerSet (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:1340:33)
192 verbose stack     at async #buildDepStep (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:915:11)
192 verbose stack     at async Arborist.buildIdealTree (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\build-ideal-tree.js:182:7)
192 verbose stack     at async Promise.all (index 1)
192 verbose stack     at async Arborist.reify (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\node_modules\@npmcli\arborist\lib\arborist\reify.js:130:5)
192 verbose stack     at async Install.exec (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\lib\commands\install.js:149:5)
192 verbose stack     at async Npm.exec (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\lib\npm.js:208:9)
192 verbose stack     at async module.exports (C:\Users\<USER>\AppData\Local\nvm\v22.14.0\node_modules\npm\lib\cli\entry.js:67:5)
193 error code ERESOLVE
194 error ERESOLVE unable to resolve dependency tree
195 error
196 error While resolving: go-admin@2.0.9
196 error Found: eslint@7.6.0
196 error node_modules/eslint
196 error   dev eslint@"7.6.0" from the root project
196 error
196 error Could not resolve dependency:
196 error peer eslint@">= 1.6.0 < 7.0.0" from @vue/cli-plugin-eslint@4.5.19
196 error node_modules/@vue/cli-plugin-eslint
196 error   dev @vue/cli-plugin-eslint@"^4.4.6" from the root project
196 error
196 error Fix the upstream dependency conflict, or retry
196 error this command with --force or --legacy-peer-deps
196 error to accept an incorrect (and potentially broken) dependency resolution.
197 error
197 error
197 error For a full report see:
197 error C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-04-13T15_22_11_659Z-eresolve-report.txt
198 silly unfinished npm timer reify 1744557734395
199 silly unfinished npm timer reify:loadTrees 1744557734396
200 silly unfinished npm timer idealTree:buildDeps 1744557734420
201 silly unfinished npm timer idealTree:#root 1744557734420
202 verbose cwd C:\Git\coach\go-admin-ui
203 verbose os Windows_NT 10.0.17763
204 verbose node v22.14.0
205 verbose npm  v11.3.0
206 verbose exit 1
207 verbose code 1
208 error A complete log of this run can be found in: C:\Users\<USER>\AppData\Local\npm-cache\_logs\2025-04-13T15_22_11_659Z-debug-0.log
