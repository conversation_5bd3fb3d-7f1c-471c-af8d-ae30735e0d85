// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT. Created at 2022-04-16 16:32:52
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SysDictType is the golang structure for table sys_dict_type.
type SysDictType struct {
	DictId    uint64      `json:"dictId"    description:"字典主键"`
	Pid       uint64      `json:"pid"       description:"上级ID"`
	DictName  string      `json:"dictName"  description:"字典名称"`
	DictType  string      `json:"dictType"  description:"字典类型"`
	Status    uint        `json:"status"    description:"状态（0正常 1停用）"`
	CreateBy  uint        `json:"createBy"  description:"创建者"`
	UpdateBy  uint        `json:"updateBy"  description:"更新者"`
	Remark    string      `json:"remark"    description:"备注"`
	CreatedAt *gtime.Time `json:"createdAt" description:"创建日期"`
	UpdatedAt *gtime.Time `json:"updatedAt" description:"修改日期"`
}
