import{a1 as He,a2 as We,a3 as qe,a4 as Je,a5 as R,ax as Ze,ay as $e,a7 as et,az as tt,ab as st,ae as De,av as Ve,aa as M}from"./doc-30bb18f4.js";var Be=function(){var e=function(v,n,r,c){for(r=r||{},c=v.length;c--;r[v[c]]=n);return r},u=[1,34],l=[1,35],o=[1,36],h=[1,37],b=[1,9],g=[1,8],_=[1,19],D=[1,20],z=[1,21],Y=[1,40],L=[1,41],K=[1,27],Q=[1,25],j=[1,26],X=[1,32],H=[1,33],W=[1,28],q=[1,29],J=[1,30],Z=[1,31],f=[1,45],d=[1,42],E=[1,43],C=[1,44],m=[1,46],pe=[1,24],a=[1,16,24],$=[1,60],ee=[1,61],te=[1,62],se=[1,63],ie=[1,64],fe=[1,65],de=[1,66],ue=[1,16,24,52],Ne=[1,77],w=[1,16,24,27,28,36,50,52,55,68,69,70,71,72,73,74,79,81],ne=[1,16,24,27,28,34,36,50,52,55,59,68,69,70,71,72,73,74,79,81,94,96,97,98,99],Le=[1,86],re=[28,94,96,97,98,99],V=[28,73,74,94,96,97,98,99],Ie=[28,68,69,70,71,72,94,96,97,98,99],Ee=[1,99],Ce=[1,16,24,50,52,55],ae=[1,16,24,36],xe=[8,9,10,11,19,23,44,46,48,53,57,58,60,61,63,65,75,76,78,82,94,96,97,98,99],ke={trace:function(){},yy:{},symbols_:{error:2,start:3,mermaidDoc:4,directive:5,statements:6,direction:7,direction_tb:8,direction_bt:9,direction_rl:10,direction_lr:11,graphConfig:12,openDirective:13,typeDirective:14,closeDirective:15,NEWLINE:16,":":17,argDirective:18,open_directive:19,type_directive:20,arg_directive:21,close_directive:22,CLASS_DIAGRAM:23,EOF:24,statement:25,classLabel:26,SQS:27,STR:28,SQE:29,namespaceName:30,alphaNumToken:31,className:32,classLiteralName:33,GENERICTYPE:34,relationStatement:35,LABEL:36,namespaceStatement:37,classStatement:38,methodStatement:39,annotationStatement:40,clickStatement:41,cssClassStatement:42,noteStatement:43,acc_title:44,acc_title_value:45,acc_descr:46,acc_descr_value:47,acc_descr_multiline_value:48,namespaceIdentifier:49,STRUCT_START:50,classStatements:51,STRUCT_STOP:52,NAMESPACE:53,classIdentifier:54,STYLE_SEPARATOR:55,members:56,CLASS:57,ANNOTATION_START:58,ANNOTATION_END:59,MEMBER:60,SEPARATOR:61,relation:62,NOTE_FOR:63,noteText:64,NOTE:65,relationType:66,lineType:67,AGGREGATION:68,EXTENSION:69,COMPOSITION:70,DEPENDENCY:71,LOLLIPOP:72,LINE:73,DOTTED_LINE:74,CALLBACK:75,LINK:76,LINK_TARGET:77,CLICK:78,CALLBACK_NAME:79,CALLBACK_ARGS:80,HREF:81,CSSCLASS:82,commentToken:83,textToken:84,graphCodeTokens:85,textNoTagsToken:86,TAGSTART:87,TAGEND:88,"==":89,"--":90,PCT:91,DEFAULT:92,SPACE:93,MINUS:94,keywords:95,UNICODE_TEXT:96,NUM:97,ALPHA:98,BQUOTE_STR:99,$accept:0,$end:1},terminals_:{2:"error",8:"direction_tb",9:"direction_bt",10:"direction_rl",11:"direction_lr",16:"NEWLINE",17:":",19:"open_directive",20:"type_directive",21:"arg_directive",22:"close_directive",23:"CLASS_DIAGRAM",24:"EOF",27:"SQS",28:"STR",29:"SQE",34:"GENERICTYPE",36:"LABEL",44:"acc_title",45:"acc_title_value",46:"acc_descr",47:"acc_descr_value",48:"acc_descr_multiline_value",50:"STRUCT_START",52:"STRUCT_STOP",53:"NAMESPACE",55:"STYLE_SEPARATOR",57:"CLASS",58:"ANNOTATION_START",59:"ANNOTATION_END",60:"MEMBER",61:"SEPARATOR",63:"NOTE_FOR",65:"NOTE",68:"AGGREGATION",69:"EXTENSION",70:"COMPOSITION",71:"DEPENDENCY",72:"LOLLIPOP",73:"LINE",74:"DOTTED_LINE",75:"CALLBACK",76:"LINK",77:"LINK_TARGET",78:"CLICK",79:"CALLBACK_NAME",80:"CALLBACK_ARGS",81:"HREF",82:"CSSCLASS",85:"graphCodeTokens",87:"TAGSTART",88:"TAGEND",89:"==",90:"--",91:"PCT",92:"DEFAULT",93:"SPACE",94:"MINUS",95:"keywords",96:"UNICODE_TEXT",97:"NUM",98:"ALPHA",99:"BQUOTE_STR"},productions_:[0,[3,1],[3,2],[3,1],[7,1],[7,1],[7,1],[7,1],[4,1],[5,4],[5,6],[13,1],[14,1],[18,1],[15,1],[12,4],[6,1],[6,2],[6,3],[26,3],[30,1],[30,2],[32,1],[32,1],[32,2],[32,2],[32,2],[25,1],[25,2],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,2],[25,2],[25,1],[37,4],[37,5],[49,2],[51,1],[51,2],[51,3],[38,1],[38,3],[38,4],[38,6],[54,2],[54,3],[40,4],[56,1],[56,2],[39,1],[39,2],[39,1],[39,1],[35,3],[35,4],[35,4],[35,5],[43,3],[43,2],[62,3],[62,2],[62,2],[62,1],[66,1],[66,1],[66,1],[66,1],[66,1],[67,1],[67,1],[41,3],[41,4],[41,3],[41,4],[41,4],[41,5],[41,3],[41,4],[41,4],[41,5],[41,4],[41,5],[41,5],[41,6],[42,3],[83,1],[83,1],[84,1],[84,1],[84,1],[84,1],[84,1],[84,1],[84,1],[86,1],[86,1],[86,1],[86,1],[31,1],[31,1],[31,1],[31,1],[33,1],[64,1]],performAction:function(n,r,c,i,A,t,G){var s=t.length-1;switch(A){case 4:i.setDirection("TB");break;case 5:i.setDirection("BT");break;case 6:i.setDirection("RL");break;case 7:i.setDirection("LR");break;case 11:i.parseDirective("%%{","open_directive");break;case 12:i.parseDirective(t[s],"type_directive");break;case 13:t[s]=t[s].trim().replace(/'/g,'"'),i.parseDirective(t[s],"arg_directive");break;case 14:i.parseDirective("}%%","close_directive","class");break;case 19:this.$=t[s-1];break;case 20:case 22:case 23:this.$=t[s];break;case 21:case 24:this.$=t[s-1]+t[s];break;case 25:case 26:this.$=t[s-1]+"~"+t[s]+"~";break;case 27:i.addRelation(t[s]);break;case 28:t[s-1].title=i.cleanupLabel(t[s]),i.addRelation(t[s-1]);break;case 37:this.$=t[s].trim(),i.setAccTitle(this.$);break;case 38:case 39:this.$=t[s].trim(),i.setAccDescription(this.$);break;case 40:i.addClassesToNamespace(t[s-3],t[s-1]);break;case 41:i.addClassesToNamespace(t[s-4],t[s-1]);break;case 42:this.$=t[s],i.addNamespace(t[s]);break;case 43:this.$=[t[s]];break;case 44:this.$=[t[s-1]];break;case 45:t[s].unshift(t[s-2]),this.$=t[s];break;case 47:i.setCssClass(t[s-2],t[s]);break;case 48:i.addMembers(t[s-3],t[s-1]);break;case 49:i.setCssClass(t[s-5],t[s-3]),i.addMembers(t[s-5],t[s-1]);break;case 50:this.$=t[s],i.addClass(t[s]);break;case 51:this.$=t[s-1],i.addClass(t[s-1]),i.setClassLabel(t[s-1],t[s]);break;case 52:i.addAnnotation(t[s],t[s-2]);break;case 53:this.$=[t[s]];break;case 54:t[s].push(t[s-1]),this.$=t[s];break;case 55:break;case 56:i.addMember(t[s-1],i.cleanupLabel(t[s]));break;case 57:break;case 58:break;case 59:this.$={id1:t[s-2],id2:t[s],relation:t[s-1],relationTitle1:"none",relationTitle2:"none"};break;case 60:this.$={id1:t[s-3],id2:t[s],relation:t[s-1],relationTitle1:t[s-2],relationTitle2:"none"};break;case 61:this.$={id1:t[s-3],id2:t[s],relation:t[s-2],relationTitle1:"none",relationTitle2:t[s-1]};break;case 62:this.$={id1:t[s-4],id2:t[s],relation:t[s-2],relationTitle1:t[s-3],relationTitle2:t[s-1]};break;case 63:i.addNote(t[s],t[s-1]);break;case 64:i.addNote(t[s]);break;case 65:this.$={type1:t[s-2],type2:t[s],lineType:t[s-1]};break;case 66:this.$={type1:"none",type2:t[s],lineType:t[s-1]};break;case 67:this.$={type1:t[s-1],type2:"none",lineType:t[s]};break;case 68:this.$={type1:"none",type2:"none",lineType:t[s]};break;case 69:this.$=i.relationType.AGGREGATION;break;case 70:this.$=i.relationType.EXTENSION;break;case 71:this.$=i.relationType.COMPOSITION;break;case 72:this.$=i.relationType.DEPENDENCY;break;case 73:this.$=i.relationType.LOLLIPOP;break;case 74:this.$=i.lineType.LINE;break;case 75:this.$=i.lineType.DOTTED_LINE;break;case 76:case 82:this.$=t[s-2],i.setClickEvent(t[s-1],t[s]);break;case 77:case 83:this.$=t[s-3],i.setClickEvent(t[s-2],t[s-1]),i.setTooltip(t[s-2],t[s]);break;case 78:this.$=t[s-2],i.setLink(t[s-1],t[s]);break;case 79:this.$=t[s-3],i.setLink(t[s-2],t[s-1],t[s]);break;case 80:this.$=t[s-3],i.setLink(t[s-2],t[s-1]),i.setTooltip(t[s-2],t[s]);break;case 81:this.$=t[s-4],i.setLink(t[s-3],t[s-2],t[s]),i.setTooltip(t[s-3],t[s-1]);break;case 84:this.$=t[s-3],i.setClickEvent(t[s-2],t[s-1],t[s]);break;case 85:this.$=t[s-4],i.setClickEvent(t[s-3],t[s-2],t[s-1]),i.setTooltip(t[s-3],t[s]);break;case 86:this.$=t[s-3],i.setLink(t[s-2],t[s]);break;case 87:this.$=t[s-4],i.setLink(t[s-3],t[s-1],t[s]);break;case 88:this.$=t[s-4],i.setLink(t[s-3],t[s-1]),i.setTooltip(t[s-3],t[s]);break;case 89:this.$=t[s-5],i.setLink(t[s-4],t[s-2],t[s]),i.setTooltip(t[s-4],t[s-1]);break;case 90:i.setCssClass(t[s-1],t[s]);break}},table:[{3:1,4:2,5:3,6:4,7:18,8:u,9:l,10:o,11:h,12:5,13:6,19:b,23:g,25:7,31:38,32:22,33:39,35:10,37:11,38:12,39:13,40:14,41:15,42:16,43:17,44:_,46:D,48:z,49:23,53:Y,54:24,57:L,58:K,60:Q,61:j,63:X,65:H,75:W,76:q,78:J,82:Z,94:f,96:d,97:E,98:C,99:m},{1:[3]},{1:[2,1]},{3:47,4:2,5:3,6:4,7:18,8:u,9:l,10:o,11:h,12:5,13:6,19:b,23:g,25:7,31:38,32:22,33:39,35:10,37:11,38:12,39:13,40:14,41:15,42:16,43:17,44:_,46:D,48:z,49:23,53:Y,54:24,57:L,58:K,60:Q,61:j,63:X,65:H,75:W,76:q,78:J,82:Z,94:f,96:d,97:E,98:C,99:m},{1:[2,3]},{1:[2,8]},{14:48,20:[1,49]},e(pe,[2,16],{16:[1,50]}),{16:[1,51]},{20:[2,11]},e(a,[2,27],{36:[1,52]}),e(a,[2,29]),e(a,[2,30]),e(a,[2,31]),e(a,[2,32]),e(a,[2,33]),e(a,[2,34]),e(a,[2,35]),e(a,[2,36]),{45:[1,53]},{47:[1,54]},e(a,[2,39]),e(a,[2,55],{62:55,66:58,67:59,28:[1,56],36:[1,57],68:$,69:ee,70:te,71:se,72:ie,73:fe,74:de}),{50:[1,67]},e(ue,[2,46],{50:[1,69],55:[1,68]}),e(a,[2,57]),e(a,[2,58]),{31:70,94:f,96:d,97:E,98:C},{31:38,32:71,33:39,94:f,96:d,97:E,98:C,99:m},{31:38,32:72,33:39,94:f,96:d,97:E,98:C,99:m},{31:38,32:73,33:39,94:f,96:d,97:E,98:C,99:m},{28:[1,74]},{31:38,32:75,33:39,94:f,96:d,97:E,98:C,99:m},{28:Ne,64:76},e(a,[2,4]),e(a,[2,5]),e(a,[2,6]),e(a,[2,7]),e(w,[2,22],{31:38,33:39,32:78,34:[1,79],94:f,96:d,97:E,98:C,99:m}),e(w,[2,23],{34:[1,80]}),{30:81,31:82,94:f,96:d,97:E,98:C},{31:38,32:83,33:39,94:f,96:d,97:E,98:C,99:m},e(ne,[2,104]),e(ne,[2,105]),e(ne,[2,106]),e(ne,[2,107]),e([1,16,24,27,28,34,36,50,52,55,68,69,70,71,72,73,74,79,81],[2,108]),{1:[2,2]},{15:84,17:[1,85],22:Le},e([17,22],[2,12]),e(pe,[2,17],{25:7,35:10,37:11,38:12,39:13,40:14,41:15,42:16,43:17,7:18,32:22,49:23,54:24,31:38,33:39,6:87,8:u,9:l,10:o,11:h,44:_,46:D,48:z,53:Y,57:L,58:K,60:Q,61:j,63:X,65:H,75:W,76:q,78:J,82:Z,94:f,96:d,97:E,98:C,99:m}),{6:88,7:18,8:u,9:l,10:o,11:h,25:7,31:38,32:22,33:39,35:10,37:11,38:12,39:13,40:14,41:15,42:16,43:17,44:_,46:D,48:z,49:23,53:Y,54:24,57:L,58:K,60:Q,61:j,63:X,65:H,75:W,76:q,78:J,82:Z,94:f,96:d,97:E,98:C,99:m},e(a,[2,28]),e(a,[2,37]),e(a,[2,38]),{28:[1,90],31:38,32:89,33:39,94:f,96:d,97:E,98:C,99:m},{62:91,66:58,67:59,68:$,69:ee,70:te,71:se,72:ie,73:fe,74:de},e(a,[2,56]),{67:92,73:fe,74:de},e(re,[2,68],{66:93,68:$,69:ee,70:te,71:se,72:ie}),e(V,[2,69]),e(V,[2,70]),e(V,[2,71]),e(V,[2,72]),e(V,[2,73]),e(Ie,[2,74]),e(Ie,[2,75]),{16:[1,95],38:96,51:94,54:24,57:L},{31:97,94:f,96:d,97:E,98:C},{56:98,60:Ee},{59:[1,100]},{28:[1,101]},{28:[1,102]},{79:[1,103],81:[1,104]},{31:105,94:f,96:d,97:E,98:C},{28:Ne,64:106},e(a,[2,64]),e(a,[2,109]),e(w,[2,24]),e(w,[2,25]),e(w,[2,26]),{50:[2,42]},{30:107,31:82,50:[2,20],94:f,96:d,97:E,98:C},e(Ce,[2,50],{26:108,27:[1,109]}),{16:[1,110]},{18:111,21:[1,112]},{16:[2,14]},e(pe,[2,18]),{24:[1,113]},e(ae,[2,59]),{31:38,32:114,33:39,94:f,96:d,97:E,98:C,99:m},{28:[1,116],31:38,32:115,33:39,94:f,96:d,97:E,98:C,99:m},e(re,[2,67],{66:117,68:$,69:ee,70:te,71:se,72:ie}),e(re,[2,66]),{52:[1,118]},{38:96,51:119,54:24,57:L},{16:[1,120],52:[2,43]},e(ue,[2,47],{50:[1,121]}),{52:[1,122]},{52:[2,53],56:123,60:Ee},{31:38,32:124,33:39,94:f,96:d,97:E,98:C,99:m},e(a,[2,76],{28:[1,125]}),e(a,[2,78],{28:[1,127],77:[1,126]}),e(a,[2,82],{28:[1,128],80:[1,129]}),{28:[1,130]},e(a,[2,90]),e(a,[2,63]),{50:[2,21]},e(Ce,[2,51]),{28:[1,131]},e(xe,[2,9]),{15:132,22:Le},{22:[2,13]},{1:[2,15]},e(ae,[2,61]),e(ae,[2,60]),{31:38,32:133,33:39,94:f,96:d,97:E,98:C,99:m},e(re,[2,65]),e(a,[2,40]),{52:[1,134]},{38:96,51:135,52:[2,44],54:24,57:L},{56:136,60:Ee},e(ue,[2,48]),{52:[2,54]},e(a,[2,52]),e(a,[2,77]),e(a,[2,79]),e(a,[2,80],{77:[1,137]}),e(a,[2,83]),e(a,[2,84],{28:[1,138]}),e(a,[2,86],{28:[1,140],77:[1,139]}),{29:[1,141]},{16:[1,142]},e(ae,[2,62]),e(a,[2,41]),{52:[2,45]},{52:[1,143]},e(a,[2,81]),e(a,[2,85]),e(a,[2,87]),e(a,[2,88],{77:[1,144]}),e(Ce,[2,19]),e(xe,[2,10]),e(ue,[2,49]),e(a,[2,89])],defaultActions:{2:[2,1],4:[2,3],5:[2,8],9:[2,11],47:[2,2],81:[2,42],86:[2,14],107:[2,21],112:[2,13],113:[2,15],123:[2,54],135:[2,45]},parseError:function(n,r){if(r.recoverable)this.trace(n);else{var c=new Error(n);throw c.hash=r,c}},parse:function(n){var r=this,c=[0],i=[],A=[null],t=[],G=this.table,s="",ce=0,Oe=0,Ke=2,Re=1,Qe=t.slice.call(arguments,1),k=Object.create(this.lexer),I={yy:{}};for(var me in this.yy)Object.prototype.hasOwnProperty.call(this.yy,me)&&(I.yy[me]=this.yy[me]);k.setInput(n,I.yy),I.yy.lexer=k,I.yy.parser=this,typeof k.yylloc>"u"&&(k.yylloc={});var be=k.yylloc;t.push(be);var je=k.options&&k.options.ranges;typeof I.yy.parseError=="function"?this.parseError=I.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Xe(){var y;return y=i.pop()||k.lex()||Re,typeof y!="number"&&(y instanceof Array&&(i=y,y=i.pop()),y=r.symbols_[y]||y),y}for(var F,x,B,Fe,O={},le,T,Pe,oe;;){if(x=c[c.length-1],this.defaultActions[x]?B=this.defaultActions[x]:((F===null||typeof F>"u")&&(F=Xe()),B=G[x]&&G[x][F]),typeof B>"u"||!B.length||!B[0]){var _e="";oe=[];for(le in G[x])this.terminals_[le]&&le>Ke&&oe.push("'"+this.terminals_[le]+"'");k.showPosition?_e="Parse error on line "+(ce+1)+`:
`+k.showPosition()+`
Expecting `+oe.join(", ")+", got '"+(this.terminals_[F]||F)+"'":_e="Parse error on line "+(ce+1)+": Unexpected "+(F==Re?"end of input":"'"+(this.terminals_[F]||F)+"'"),this.parseError(_e,{text:k.match,token:this.terminals_[F]||F,line:k.yylineno,loc:be,expected:oe})}if(B[0]instanceof Array&&B.length>1)throw new Error("Parse Error: multiple actions possible at state: "+x+", token: "+F);switch(B[0]){case 1:c.push(F),A.push(k.yytext),t.push(k.yylloc),c.push(B[1]),F=null,Oe=k.yyleng,s=k.yytext,ce=k.yylineno,be=k.yylloc;break;case 2:if(T=this.productions_[B[1]][1],O.$=A[A.length-T],O._$={first_line:t[t.length-(T||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(T||1)].first_column,last_column:t[t.length-1].last_column},je&&(O._$.range=[t[t.length-(T||1)].range[0],t[t.length-1].range[1]]),Fe=this.performAction.apply(O,[s,Oe,ce,I.yy,B[1],A,t].concat(Qe)),typeof Fe<"u")return Fe;T&&(c=c.slice(0,-1*T*2),A=A.slice(0,-1*T),t=t.slice(0,-1*T)),c.push(this.productions_[B[1]][0]),A.push(O.$),t.push(O._$),Pe=G[c[c.length-2]][c[c.length-1]],c.push(Pe);break;case 3:return!0}}return!0}},Ye=function(){var v={EOF:1,parseError:function(r,c){if(this.yy.parser)this.yy.parser.parseError(r,c);else throw new Error(r)},setInput:function(n,r){return this.yy=r||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var r=n.match(/(?:\r\n?|\n).*/g);return r?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},unput:function(n){var r=n.length,c=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-r),this.offset-=r;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var A=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===i.length?this.yylloc.first_column:0)+i[i.length-c.length].length-c[0].length:this.yylloc.first_column-r},this.options.ranges&&(this.yylloc.range=[A[0],A[0]+this.yyleng-r]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},less:function(n){this.unput(this.match.slice(n))},pastInput:function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var n=this.pastInput(),r=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+r+"^"},test_match:function(n,r){var c,i,A;if(this.options.backtrack_lexer&&(A={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(A.yylloc.range=this.yylloc.range.slice(0))),i=n[0].match(/(?:\r\n?|\n).*/g),i&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],c=this.performAction.call(this,this.yy,this,r,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in A)this[t]=A[t];return!1}return!1},next:function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,r,c,i;this._more||(this.yytext="",this.match="");for(var A=this._currentRules(),t=0;t<A.length;t++)if(c=this._input.match(this.rules[A[t]]),c&&(!r||c[0].length>r[0].length)){if(r=c,i=t,this.options.backtrack_lexer){if(n=this.test_match(c,A[t]),n!==!1)return n;if(this._backtrack){r=!1;continue}else return!1}else if(!this.options.flex)break}return r?(n=this.test_match(r,A[i]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var r=this.next();return r||this.lex()},begin:function(r){this.conditionStack.push(r)},popState:function(){var r=this.conditionStack.length-1;return r>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(r){return r=this.conditionStack.length-1-Math.abs(r||0),r>=0?this.conditionStack[r]:"INITIAL"},pushState:function(r){this.begin(r)},stateStackSize:function(){return this.conditionStack.length},options:{},performAction:function(r,c,i,A){switch(i){case 0:return this.begin("open_directive"),19;case 1:return 8;case 2:return 9;case 3:return 10;case 4:return 11;case 5:return this.begin("type_directive"),20;case 6:return this.popState(),this.begin("arg_directive"),17;case 7:return this.popState(),this.popState(),22;case 8:return 21;case 9:break;case 10:break;case 11:return this.begin("acc_title"),44;case 12:return this.popState(),"acc_title_value";case 13:return this.begin("acc_descr"),46;case 14:return this.popState(),"acc_descr_value";case 15:this.begin("acc_descr_multiline");break;case 16:this.popState();break;case 17:return"acc_descr_multiline_value";case 18:return 16;case 19:break;case 20:return 23;case 21:return 23;case 22:return"EDGE_STATE";case 23:this.begin("callback_name");break;case 24:this.popState();break;case 25:this.popState(),this.begin("callback_args");break;case 26:return 79;case 27:this.popState();break;case 28:return 80;case 29:this.popState();break;case 30:return"STR";case 31:this.begin("string");break;case 32:return this.begin("namespace"),53;case 33:return this.popState(),16;case 34:break;case 35:return this.begin("namespace-body"),50;case 36:return this.popState(),52;case 37:return"EOF_IN_STRUCT";case 38:return 16;case 39:break;case 40:return"EDGE_STATE";case 41:return this.begin("class"),57;case 42:return this.popState(),16;case 43:break;case 44:return this.popState(),this.popState(),52;case 45:return this.begin("class-body"),50;case 46:return this.popState(),52;case 47:return"EOF_IN_STRUCT";case 48:return"EDGE_STATE";case 49:return"OPEN_IN_STRUCT";case 50:break;case 51:return"MEMBER";case 52:return 82;case 53:return 75;case 54:return 76;case 55:return 78;case 56:return 63;case 57:return 65;case 58:return 58;case 59:return 59;case 60:return 81;case 61:this.popState();break;case 62:return"GENERICTYPE";case 63:this.begin("generic");break;case 64:this.popState();break;case 65:return"BQUOTE_STR";case 66:this.begin("bqstring");break;case 67:return 77;case 68:return 77;case 69:return 77;case 70:return 77;case 71:return 69;case 72:return 69;case 73:return 71;case 74:return 71;case 75:return 70;case 76:return 68;case 77:return 72;case 78:return 73;case 79:return 74;case 80:return 36;case 81:return 55;case 82:return 94;case 83:return"DOT";case 84:return"PLUS";case 85:return 91;case 86:return"EQUALS";case 87:return"EQUALS";case 88:return 98;case 89:return 27;case 90:return 29;case 91:return"PUNCTUATION";case 92:return 97;case 93:return 96;case 94:return 93;case 95:return 24}},rules:[/^(?:%%\{)/,/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:((?:(?!\}%%)[^:.])*))/,/^(?::)/,/^(?:\}%%)/,/^(?:((?:(?!\}%%).|\n)*))/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[31,36,37,38,39,40,41,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},namespace:{rules:[31,32,33,34,35,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},"class-body":{rules:[31,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},class:{rules:[31,42,43,44,45,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},acc_descr_multiline:{rules:[16,17,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},acc_descr:{rules:[14,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},acc_title:{rules:[12,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},arg_directive:{rules:[7,8,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},type_directive:{rules:[6,7,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},open_directive:{rules:[5,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},callback_args:{rules:[27,28,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},callback_name:{rules:[24,25,26,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},href:{rules:[31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},struct:{rules:[31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},generic:{rules:[31,52,53,54,55,56,57,58,59,60,61,62,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},bqstring:{rules:[31,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},string:{rules:[29,30,31,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,9,10,11,13,15,18,19,20,21,22,23,31,32,41,52,53,54,55,56,57,58,59,60,63,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95],inclusive:!0}}};return v}();ke.lexer=Ye;function ge(){this.yy={}}return ge.prototype=ke,ke.Parser=ge,new ge}();Be.parser=Be;const Lt=Be,Ae="classId-";let Se=[],p={},he=[],we=0,N={},Te=0,U=[];const S=e=>De.sanitizeText(e,R()),it=function(e,u,l){et.parseDirective(this,e,u,l)},P=function(e){let u="",l=e;if(e.indexOf("~")>0){const o=e.split("~");l=S(o[0]),u=S(o[1])}return{className:l,type:u}},ut=function(e,u){u&&(u=S(u));const{className:l}=P(e);p[l].label=u},ye=function(e){const u=P(e);p[u.className]===void 0&&(p[u.className]={id:u.className,type:u.type,label:u.className,cssClasses:[],methods:[],members:[],annotations:[],domId:Ae+u.className+"-"+we},we++)},Ge=function(e){if(e in p)return p[e].domId;throw new Error("Class not found: "+e)},nt=function(){Se=[],p={},he=[],U=[],U.push(Ue),N={},Te=0,tt()},rt=function(e){return p[e]},at=function(){return p},ct=function(){return Se},lt=function(){return he},ot=function(e){st.debug("Adding relation: "+JSON.stringify(e)),ye(e.id1),ye(e.id2),e.id1=P(e.id1).className,e.id2=P(e.id2).className,e.relationTitle1=De.sanitizeText(e.relationTitle1.trim(),R()),e.relationTitle2=De.sanitizeText(e.relationTitle2.trim(),R()),Se.push(e)},ht=function(e,u){const l=P(e).className;p[l].annotations.push(u)},Me=function(e,u){const l=P(e).className,o=p[l];if(typeof u=="string"){const h=u.trim();h.startsWith("<<")&&h.endsWith(">>")?o.annotations.push(S(h.substring(2,h.length-2))):h.indexOf(")")>0?o.methods.push(S(h)):h&&o.members.push(S(h))}},At=function(e,u){Array.isArray(u)&&(u.reverse(),u.forEach(l=>Me(e,l)))},pt=function(e,u){const l={id:`note${he.length}`,class:u,text:e};he.push(l)},ft=function(e){return e.startsWith(":")&&(e=e.substring(1)),S(e.trim())},ve=function(e,u){e.split(",").forEach(function(l){let o=l;l[0].match(/\d/)&&(o=Ae+o),p[o]!==void 0&&p[o].cssClasses.push(u)})},dt=function(e,u){e.split(",").forEach(function(l){u!==void 0&&(p[l].tooltip=S(u))})},Et=function(e,u){return u?N[u].classes[e].tooltip:p[e].tooltip},Ct=function(e,u,l){const o=R();e.split(",").forEach(function(h){let b=h;h[0].match(/\d/)&&(b=Ae+b),p[b]!==void 0&&(p[b].link=Ve.formatUrl(u,o),o.securityLevel==="sandbox"?p[b].linkTarget="_top":typeof l=="string"?p[b].linkTarget=S(l):p[b].linkTarget="_blank")}),ve(e,"clickable")},kt=function(e,u,l){e.split(",").forEach(function(o){gt(o,u,l),p[o].haveCallback=!0}),ve(e,"clickable")},gt=function(e,u,l){if(R().securityLevel!=="loose"||u===void 0)return;const h=e;if(p[h]!==void 0){const b=Ge(h);let g=[];if(typeof l=="string"){g=l.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let _=0;_<g.length;_++){let D=g[_].trim();D.charAt(0)==='"'&&D.charAt(D.length-1)==='"'&&(D=D.substr(1,D.length-2)),g[_]=D}}g.length===0&&g.push(b),U.push(function(){const _=document.querySelector(`[id="${b}"]`);_!==null&&_.addEventListener("click",function(){Ve.runFunc(u,...g)},!1)})}},mt=function(e){U.forEach(function(u){u(e)})},bt={LINE:0,DOTTED_LINE:1},Ft={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},Ue=function(e){let u=M(".mermaidTooltip");(u._groups||u)[0][0]===null&&(u=M("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),M(e).select("svg").selectAll("g.node").on("mouseover",function(){const h=M(this);if(h.attr("title")===null)return;const g=this.getBoundingClientRect();u.transition().duration(200).style("opacity",".9"),u.text(h.attr("title")).style("left",window.scrollX+g.left+(g.right-g.left)/2+"px").style("top",window.scrollY+g.top-14+document.body.scrollTop+"px"),u.html(u.html().replace(/&lt;br\/&gt;/g,"<br/>")),h.classed("hover",!0)}).on("mouseout",function(){u.transition().duration(500).style("opacity",0),M(this).classed("hover",!1)})};U.push(Ue);let ze="TB";const _t=()=>ze,Dt=e=>{ze=e},Bt=function(e){N[e]===void 0&&(N[e]={id:e,classes:{},children:{},domId:Ae+e+"-"+Te},Te++)},Tt=function(e){return N[e]},yt=function(){return N},St=function(e,u){N[e]!==void 0&&u.map(l=>{p[l].parent=e,N[e].classes[l]=p[l]})},It={parseDirective:it,setAccTitle:He,getAccTitle:We,getAccDescription:qe,setAccDescription:Je,getConfig:()=>R().class,addClass:ye,bindFunctions:mt,clear:nt,getClass:rt,getClasses:at,getNotes:lt,addAnnotation:ht,addNote:pt,getRelations:ct,addRelation:ot,getDirection:_t,setDirection:Dt,addMember:Me,addMembers:At,cleanupLabel:ft,lineType:bt,relationType:Ft,setClickEvent:kt,setCssClass:ve,setLink:Ct,getTooltip:Et,setTooltip:dt,lookUpDomId:Ge,setDiagramTitle:Ze,getDiagramTitle:$e,setClassLabel:ut,addNamespace:Bt,addClassesToNamespace:St,getNamespace:Tt,getNamespaces:yt},vt=e=>`g.classGroup text {
  fill: ${e.nodeBorder};
  fill: ${e.classText};
  stroke: none;
  font-family: ${e.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${e.classText};
}
.edgeLabel .label rect {
  fill: ${e.mainBkg};
}
.label text {
  fill: ${e.classText};
}
.edgeLabel .label span {
  background: ${e.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${e.mainBkg};
  stroke: ${e.nodeBorder};
}

g.classGroup line {
  stroke: ${e.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${e.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${e.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${e.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${e.lineColor} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${e.mainBkg} !important;
  stroke: ${e.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${e.textColor};
}
`,xt=vt;export{It as d,Lt as p,xt as s};
