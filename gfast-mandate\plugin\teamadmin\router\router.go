// plugin/teamadmin/router/router.go
func init() {
	gfast.RouterGroup("/teamadmin", func(group *ghttp.RouterGroup) {
		group.Middleware(middleware.PlatformAuth) // 平台鉴权

		// 团队管理
		group.Group("/team", func(team *ghttp.RouterGroup) {
			team.GET("/list", controller.Team.List)
			team.POST("/create", controller.Team.Create)
			team.PUT("/update/:id", controller.Team.Update)
			team.DELETE("/:id", controller.Team.Delete)
			team.POST("/import", controller.Team.ImportExcel)
		})

		// 成员管理
		group.Group("/member", func(m *ghttp.RouterGroup) {
			m.GET("/list", controller.Member.List)
			m.POST("/role", controller.Member.SetRole)
			m.POST("/ban", controller.Member.Ban)
		})

		// 群聊管理
		group.Group("/group", func(g *ghttp.RouterGroup) {
			g.GET("/list", controller.Group.List)
			g.POST("/announcement", controller.Group.Announce)
			g.POST("/mute", controller.Group.Mute)
		})

		// 项目管理
		group.Group("/project", func(p *ghttp.RouterGroup) {
			p.GET("/list", controller.Project.List)
			p.POST("/create", controller.Project.Create)
			p.POST("/assign", controller.Project.Assign)
			p.GET("/gantt", controller.Project.Gantt)
		})

		// 数据统计
		group.Group("/stats", func(s *ghttp.RouterGroup) {
			s.GET("/activity", controller.Stats.Activity)
			s.GET("/plan", controller.Stats.PlanRate)
		})

		// 设置模块
		group.Group("/config", func(c *ghttp.RouterGroup) {
			c.GET("/notification", controller.Config.GetNotify)
			c.POST("/notification", controller.Config.SetNotify)
		})
	})
}
