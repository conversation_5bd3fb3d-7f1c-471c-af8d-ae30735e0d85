2025-06-20T16:41:05.447+08:00 [ERRO] {04ae52df91b34a1854e1c94146d84d58} [21017 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW TABLES
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:26.479+08:00 [ERRO] {04ae52df91b34a1854e1c94146d84d58} [21005 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_user`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:34.299+08:00 [ERRO] {04ae52df91b34a1854e1c94146d84d58} [7813 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_user`
Error: dial tcp *************:3306: operation was canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:34.302+08:00 [ERRO] {04ae52df91b34a1854e1c94146d84d58} [  0 ms] [default] [gfast-v32mandate] [rows:0  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE `user_name`='demo' LIMIT 1
Error: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T16:41:55.336+08:00 [ERRO] {f41db0849db34a1857e1c941a9982833} [21013 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_login_log`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).LoginLog
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:136
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog.(*sSysLoginLog).Invoke.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysLoginLog/sys_login_log.go:42

2025-06-20T16:42:16.342+08:00 [ERRO] {f41db0849db34a1857e1c941a9982833} [21003 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_login_log`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).LoginLog
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:136
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog.(*sSysLoginLog).Invoke.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysLoginLog/sys_login_log.go:42

2025-06-20T16:42:37.352+08:00 [ERRO] {f41db0849db34a1857e1c941a9982833} [21010 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_login_log`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).LoginLog
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:136
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog.(*sSysLoginLog).Invoke.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysLoginLog/sys_login_log.go:42

2025-06-20T16:42:58.358+08:00 [ERRO] {f41db0849db34a1857e1c941a9982833} [21005 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_login_log`
Error: dial tcp *************:3306: connectex: A connection attempt failed because the connected party did not properly respond after a period of time, or established connection failed because connected host has failed to respond. 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).LoginLog
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:136
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysLoginLog.(*sSysLoginLog).Invoke.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysLoginLog/sys_login_log.go:42

2025-06-20T17:02:18.923+08:00 [ERRO] {5867b1d7bdb44a1880e1c94108c607ae} [6242 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW TABLES
Error: dial tcp *************:3306: operation was canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:02:18.929+08:00 [ERRO] {5867b1d7bdb44a1880e1c94108c607ae} [  1 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_user`
Error: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:02:18.931+08:00 [ERRO] {5867b1d7bdb44a1880e1c94108c607ae} [  0 ms] [default] [gfast-v32mandate] [rows:0  ] SHOW FULL COLUMNS FROM `sys_user`
Error: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:02:18.932+08:00 [ERRO] {5867b1d7bdb44a1880e1c94108c607ae} [  0 ms] [default] [gfast-v32mandate] [rows:0  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE `user_name`='demo' LIMIT 1
Error: context canceled 
Stack:
1.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:97
2.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetUserByUsername
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:95
3.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword.func1
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:78
4.  github.com/tiger1103/gfast/v3/internal/app/system/logic/sysUser.(*sSysUser).GetAdminUserByUsernamePassword
    C:/Git/COACH/gfast-mandate/internal/app/system/logic/sysUser/sys_user.go:77
5.  github.com/tiger1103/gfast/v3/internal/app/system/controller.(*loginController).Login
    C:/Git/COACH/gfast-mandate/internal/app/system/controller/sys_login.go:66
6.  github.com/tiger1103/gfast/v3/internal/app/common/logic/middleware.(*sMiddleware).MiddlewareCORS
    C:/Git/COACH/gfast-mandate/internal/app/common/logic/middleware/middleware.go:30

2025-06-20T17:03:28.215+08:00 [DEBU] {e0bee66acfb44a185d163778badaed0b} [ 83 ms] [default] [gfast] [rows:29 ] SHOW TABLES
2025-06-20T17:03:28.253+08:00 [DEBU] {e0bee66acfb44a185d163778badaed0b} [ 36 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-20T17:03:28.266+08:00 [DEBU] {e0bee66acfb44a185d163778badaed0b} [ 11 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-20T17:03:28.278+08:00 [DEBU] {e0bee66acfb44a185d163778badaed0b} [ 12 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-20T17:03:46.132+08:00 [DEBU] {34bf369ad3b44a18611637784e546989} [ 27 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-20T17:03:46.143+08:00 [DEBU] {34bf369ad3b44a18611637784e546989} [ 10 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:03:46.154+08:00 [DEBU] {34bf369ad3b44a18611637784e546989} [ 10 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:03:46' WHERE `id`=31
2025-06-20T17:03:46.170+08:00 [DEBU] {68f2219dd3b44a1862163778a715ec6c} [ 16 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-20T17:03:46.177+08:00 [DEBU] {68f2219dd3b44a1862163778a715ec6c} [  6 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:03:46','系统后台') 
2025-06-20T17:03:54.401+08:00 [DEBU] {7898ab88d5b44a186416377892d73d82} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:03:54.404+08:00 [DEBU] {7898ab88d5b44a186416377892d73d82} [  2 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:03:54' WHERE `id`=31
2025-06-20T17:03:54.407+08:00 [DEBU] {78b5ed88d5b44a186516377817371710} [  2 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:03:54','系统后台') 
2025-06-20T17:04:19.657+08:00 [DEBU] {0c013467dbb44a186a1637784ce3de46} [ 48 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:04:19.661+08:00 [DEBU] {0c013467dbb44a186a1637784ce3de46} [  4 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:04:19' WHERE `id`=31
2025-06-20T17:04:19.675+08:00 [DEBU] {34075d6adbb44a186b163778ff56b450} [ 13 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:04:19','系统后台') 
2025-06-20T17:04:39.375+08:00 [DEBU] {e499c000e0b44a1886a1550e05e6d23d} [ 10 ms] [default] [gfast] [rows:29 ] SHOW TABLES
2025-06-20T17:04:39.395+08:00 [DEBU] {e499c000e0b44a1886a1550e05e6d23d} [ 19 ms] [default] [gfast] [rows:14 ] SHOW FULL COLUMNS FROM `sys_job`
2025-06-20T17:04:39.399+08:00 [DEBU] {e499c000e0b44a1886a1550e05e6d23d} [  3 ms] [default] [gfast] [rows:1  ] SELECT `job_id`,`job_name`,`job_params`,`job_group`,`invoke_target`,`cron_expression`,`misfire_policy`,`concurrent`,`status`,`created_by`,`updated_by`,`remark`,`created_at`,`updated_at` FROM `sys_job` WHERE `status`=0
2025-06-20T17:04:39.404+08:00 [DEBU] {e499c000e0b44a1886a1550e05e6d23d} [  4 ms] [default] [gfast] [rows:0  ] UPDATE `sys_job` SET `status`=0 WHERE `job_id`=8
2025-06-20T17:05:18.003+08:00 [DEBU] {88848ffee8b44a188aa1550e30b40d5b} [ 20 ms] [default] [gfast] [rows:22 ] SHOW FULL COLUMNS FROM `sys_user`
2025-06-20T17:05:18.005+08:00 [DEBU] {88848ffee8b44a188aa1550e30b40d5b} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:05:18.010+08:00 [DEBU] {88848ffee8b44a188aa1550e30b40d5b} [  5 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:05:18' WHERE `id`=31
2025-06-20T17:05:18.022+08:00 [DEBU] {2cce3100e9b44a188ba1550ed9c9618c} [ 11 ms] [default] [gfast] [rows:10 ] SHOW FULL COLUMNS FROM `sys_login_log`
2025-06-20T17:05:18.027+08:00 [DEBU] {2cce3100e9b44a188ba1550ed9c9618c} [  4 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:05:18','系统后台') 
2025-06-20T17:05:38.144+08:00 [DEBU] {146233b0edb44a188da1550e5c3d0bee} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:05:38.149+08:00 [DEBU] {146233b0edb44a188da1550e5c3d0bee} [  4 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:05:38' WHERE `id`=31
2025-06-20T17:05:38.154+08:00 [DEBU] {f8338cb0edb44a188ea1550e27fa6021} [  5 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:05:38','系统后台') 
2025-06-20T17:05:39.816+08:00 [DEBU] {8089d213eeb44a1890a1550e4f6b8a87} [  1 ms] [default] [gfast] [rows:1  ] SELECT `id`,`user_name`,`mobile`,`user_nickname`,`user_password`,`user_salt`,`user_status`,`is_admin`,`avatar`,`dept_id` FROM `sys_user` WHERE (`user_name`='demo') AND `deleted_at` IS NULL LIMIT 1
2025-06-20T17:05:39.856+08:00 [DEBU] {8089d213eeb44a1890a1550e4f6b8a87} [ 39 ms] [default] [gfast] [rows:1  ] UPDATE `sys_user` SET `last_login_ip`='::1',`last_login_time`='2025-06-20 17:05:39' WHERE `id`=31
2025-06-20T17:05:39.871+08:00 [DEBU] {dcb25416eeb44a1891a1550e0afc92ae} [ 15 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_login_log`(`login_name`,`ipaddr`,`login_location`,`browser`,`os`,`status`,`msg`,`login_time`,`module`) VALUES('demo','::1','内网IP','Chrome','Windows 10',1,'登录成功','2025-06-20 17:05:39','系统后台') 
2025-06-20T17:10:05.435+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [ 34 ms] [default] [gfast] [rows:8  ] SHOW FULL COLUMNS FROM `sys_user_online`
2025-06-20T17:10:05.437+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [  1 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-20T17:10:05.440+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [  3 ms] [default] [gfast] [rows:1  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 0,50
2025-06-20T17:10:05.457+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [ 13 ms] [default] [gfast] [rows:1  ] DELETE FROM `sys_user_online` WHERE `token`='7ZUSfVIf2HyYjcv86SKPPs29v003ECPEScsdYsYYqO06noFT+/dzT6BC8AAIZikKW6dGLg5hvT8K/xVlUc9coKdbLzQVYyLE+SUlOalfngAX6+MBXbvGTkRB1m7RyAQ/un2aAMxdkTh2INpqIxezZQ=='
2025-06-20T17:10:05.458+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [  0 ms] [default] [gfast] [rows:1  ] SELECT COUNT(1) FROM `sys_user_online`
2025-06-20T17:10:05.460+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [  2 ms] [default] [gfast] [rows:0  ] SELECT `id`,`uuid`,`token`,`create_time`,`user_name`,`ip`,`explorer`,`os` FROM `sys_user_online` ORDER BY `create_time` DESC LIMIT 50,50
2025-06-20T17:10:05.472+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [ 11 ms] [default] [gfast] [rows:4  ] SHOW FULL COLUMNS FROM `sys_job_log`
2025-06-20T17:10:05.474+08:00 [DEBU] {6066d502e0b44a1887a1550eda1ae1cf} [  1 ms] [default] [gfast] [rows:1  ] INSERT INTO `sys_job_log`(`target_name`,`created_at`,`result`) VALUES('checkUserOnline','2025-06-20 17:10:05','在线用户定时更新，执行成功') 
